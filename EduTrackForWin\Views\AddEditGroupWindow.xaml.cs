using System.Windows;
using EduTrackForWin.Models;
using EduTrackForWin.Data;
using Microsoft.EntityFrameworkCore;

namespace EduTrackForWin.Views
{
    public partial class AddEditGroupWindow : Window
    {
        private readonly EduTrackDbContext _context;
        private Group? _currentGroup;
        private bool _isEditMode;

        public AddEditGroupWindow(Group? group = null)
        {
            try
            {
                InitializeComponent();
                _context = new EduTrackDbContext();
                _currentGroup = group;
                _isEditMode = group != null;

                InitializeWindow();
                LoadTeachers();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تهيئة نافذة المجموعة: {ex.Message}", "خطأ",
                              MessageBoxButton.OK, MessageBoxImage.Error);
                throw;
            }
        }

        private void InitializeWindow()
        {
            if (_isEditMode && _currentGroup != null)
            {
                TxtTitle.Text = "تعديل بيانات المجموعة";
                Title = "تعديل بيانات المجموعة";
                BtnSave.Content = "تحديث";
                LoadGroupData();
            }
            else
            {
                TxtTitle.Text = "إنشاء مجموعة جديدة";
                Title = "إنشاء مجموعة جديدة";
                BtnSave.Content = "حفظ";
            }
        }

        private async void LoadTeachers()
        {
            try
            {
                var teachers = await _context.Teachers
                    .OrderBy(t => t.Name)
                    .ToListAsync();

                CmbTeacher.ItemsSource = teachers;
                
                if (teachers.Any())
                {
                    CmbTeacher.SelectedIndex = 0;
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل المعلمين: {ex.Message}", "خطأ", 
                              MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void LoadGroupData()
        {
            if (_currentGroup == null) return;

            TxtName.Text = _currentGroup.Name;
            TxtRoom.Text = _currentGroup.Room;
            TxtDescription.Text = _currentGroup.Description;
            TxtMonthlyFee.Text = _currentGroup.MonthlyFee.ToString();
            ChkIsActive.IsChecked = _currentGroup.IsActive;

            // Set subject
            if (!string.IsNullOrEmpty(_currentGroup.Subject))
            {
                CmbSubject.Text = _currentGroup.Subject;
            }

            // Set grade
            if (!string.IsNullOrEmpty(_currentGroup.Grade))
            {
                foreach (var item in CmbGrade.Items.Cast<System.Windows.Controls.ComboBoxItem>())
                {
                    if (item.Content.ToString() == _currentGroup.Grade)
                    {
                        CmbGrade.SelectedItem = item;
                        break;
                    }
                }
            }

            // Set teacher
            CmbTeacher.SelectedValue = _currentGroup.TeacherId;
        }

        private async void BtnSave_Click(object sender, RoutedEventArgs e)
        {
            if (!ValidateInput())
                return;

            try
            {
                var group = _currentGroup ?? new Group();

                // Update group properties
                group.Name = TxtName.Text.Trim();
                group.Room = TxtRoom.Text.Trim();
                group.Description = TxtDescription.Text.Trim();
                group.IsActive = ChkIsActive.IsChecked == true;

                // Set subject
                group.Subject = CmbSubject.Text.Trim();

                // Set grade
                if (CmbGrade.SelectedItem is System.Windows.Controls.ComboBoxItem selectedGrade)
                {
                    group.Grade = selectedGrade.Content.ToString() ?? "";
                }

                // Set monthly fee
                if (decimal.TryParse(TxtMonthlyFee.Text, out var monthlyFee))
                {
                    group.MonthlyFee = monthlyFee;
                }

                // Set teacher
                if (CmbTeacher.SelectedValue is int teacherId)
                {
                    group.TeacherId = teacherId;
                }

                if (_isEditMode)
                {
                    group.UpdatedAt = DateTime.Now;
                    _context.Groups.Update(group);
                    await _context.SaveChangesAsync();
                    MessageBox.Show("تم تحديث بيانات المجموعة بنجاح", "نجح", 
                                  MessageBoxButton.OK, MessageBoxImage.Information);
                }
                else
                {
                    group.CreatedAt = DateTime.Now;
                    _context.Groups.Add(group);
                    await _context.SaveChangesAsync();
                    MessageBox.Show("تم إنشاء المجموعة بنجاح", "نجح", 
                                  MessageBoxButton.OK, MessageBoxImage.Information);
                }

                DialogResult = true;
                Close();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في حفظ بيانات المجموعة: {ex.Message}", "خطأ", 
                              MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private bool ValidateInput()
        {
            if (string.IsNullOrWhiteSpace(TxtName.Text))
            {
                MessageBox.Show("يرجى إدخال اسم المجموعة", "خطأ في البيانات", 
                              MessageBoxButton.OK, MessageBoxImage.Warning);
                TxtName.Focus();
                return false;
            }

            if (TxtName.Text.Trim().Length < 2)
            {
                MessageBox.Show("يجب أن يكون اسم المجموعة أكثر من حرفين", "خطأ في البيانات", 
                              MessageBoxButton.OK, MessageBoxImage.Warning);
                TxtName.Focus();
                return false;
            }

            if (CmbTeacher.SelectedValue == null)
            {
                MessageBox.Show("يرجى اختيار المعلم المسؤول", "خطأ في البيانات", 
                              MessageBoxButton.OK, MessageBoxImage.Warning);
                CmbTeacher.Focus();
                return false;
            }

            // Validate monthly fee
            if (!decimal.TryParse(TxtMonthlyFee.Text, out var monthlyFee) || monthlyFee < 0)
            {
                MessageBox.Show("يرجى إدخال رسوم شهرية صحيحة", "خطأ في البيانات", 
                              MessageBoxButton.OK, MessageBoxImage.Warning);
                TxtMonthlyFee.Focus();
                return false;
            }

            return true;
        }

        private void BtnCancel_Click(object sender, RoutedEventArgs e)
        {
            DialogResult = false;
            Close();
        }

        protected override void OnClosed(EventArgs e)
        {
            _context?.Dispose();
            base.OnClosed(e);
        }
    }
}
