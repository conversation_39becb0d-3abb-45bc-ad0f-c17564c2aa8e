<Window x:Class="EduTrackForWin.Views.AddEditStudentWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:EduTrackForWin.Views"
        mc:Ignorable="d"
        Title="إضافة/تعديل طالب" Height="600" Width="500"
        WindowStartupLocation="CenterOwner"
        ResizeMode="NoResize">

    <Window.Resources>
        <!-- Input Field Style -->
        <Style x:Key="InputFieldStyle" TargetType="TextBox">
            <Setter Property="Padding" Value="10"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="BorderBrush" Value="#E0E0E0"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="Margin" Value="0,5,0,15"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="TextBox">
                        <Border Background="{TemplateBinding Background}" 
                              BorderBrush="{TemplateBinding BorderBrush}" 
                              BorderThickness="{TemplateBinding BorderThickness}"
                              CornerRadius="5">
                            <ScrollViewer x:Name="PART_ContentHost" 
                                        VerticalAlignment="Center" 
                                        Margin="{TemplateBinding Padding}"/>
                        </Border>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <!-- ComboBox Style -->
        <Style x:Key="ComboBoxStyle" TargetType="ComboBox">
            <Setter Property="Padding" Value="10"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="BorderBrush" Value="#E0E0E0"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="Margin" Value="0,5,0,15"/>
        </Style>

        <!-- Label Style -->
        <Style x:Key="LabelStyle" TargetType="TextBlock">
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="FontWeight" Value="SemiBold"/>
            <Setter Property="Foreground" Value="#212121"/>
            <Setter Property="Margin" Value="0,10,0,5"/>
        </Style>

        <!-- Button Styles -->
        <Style x:Key="PrimaryButtonStyle" TargetType="Button">
            <Setter Property="Background" Value="#2196F3"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Padding" Value="20,10"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}" 
                              CornerRadius="5">
                            <ContentPresenter HorizontalAlignment="Center" 
                                            VerticalAlignment="Center"
                                            Margin="{TemplateBinding Padding}"/>
                        </Border>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <Style x:Key="SecondaryButtonStyle" TargetType="Button">
            <Setter Property="Background" Value="#757575"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Padding" Value="20,10"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}" 
                              CornerRadius="5">
                            <ContentPresenter HorizontalAlignment="Center" 
                                            VerticalAlignment="Center"
                                            Margin="{TemplateBinding Padding}"/>
                        </Border>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>
    </Window.Resources>

    <Grid Margin="30">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <StackPanel Grid.Row="0" Margin="0,0,0,20">
            <TextBlock Name="TxtTitle" Text="إضافة طالب جديد" FontSize="24" FontWeight="Bold" 
                     Foreground="#212121" HorizontalAlignment="Center"/>
            <TextBlock Text="املأ البيانات التالية لإضافة طالب جديد" FontSize="14" 
                     Foreground="#757575" HorizontalAlignment="Center" Margin="0,5,0,0"/>
        </StackPanel>

        <!-- Form -->
        <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto">
            <StackPanel>
                <!-- Student Name -->
                <TextBlock Text="اسم الطالب *" Style="{StaticResource LabelStyle}"/>
                <TextBox Name="TxtName" Style="{StaticResource InputFieldStyle}" 
                       MaxLength="100"/>

                <!-- Grade -->
                <TextBlock Text="الصف الدراسي" Style="{StaticResource LabelStyle}"/>
                <ComboBox Name="CmbGrade" Style="{StaticResource ComboBoxStyle}">
                    <ComboBoxItem Content="الصف الأول"/>
                    <ComboBoxItem Content="الصف الثاني"/>
                    <ComboBoxItem Content="الصف الثالث"/>
                    <ComboBoxItem Content="الصف الرابع"/>
                    <ComboBoxItem Content="الصف الخامس"/>
                    <ComboBoxItem Content="الصف السادس"/>
                    <ComboBoxItem Content="الصف السابع"/>
                    <ComboBoxItem Content="الصف الثامن"/>
                    <ComboBoxItem Content="الصف التاسع"/>
                    <ComboBoxItem Content="الصف العاشر"/>
                    <ComboBoxItem Content="الصف الحادي عشر"/>
                    <ComboBoxItem Content="الصف الثاني عشر"/>
                </ComboBox>

                <!-- Phone -->
                <TextBlock Text="رقم الهاتف" Style="{StaticResource LabelStyle}"/>
                <TextBox Name="TxtPhone" Style="{StaticResource InputFieldStyle}" 
                       MaxLength="20"/>

                <!-- Address -->
                <TextBlock Text="العنوان" Style="{StaticResource LabelStyle}"/>
                <TextBox Name="TxtAddress" Style="{StaticResource InputFieldStyle}" 
                       MaxLength="200" Height="60" TextWrapping="Wrap" 
                       VerticalScrollBarVisibility="Auto"/>

                <!-- Group -->
                <TextBlock Text="المجموعة" Style="{StaticResource LabelStyle}"/>
                <ComboBox Name="CmbGroup" Style="{StaticResource ComboBoxStyle}" 
                        DisplayMemberPath="Name" SelectedValuePath="Id"/>

                <!-- Notes -->
                <TextBlock Text="ملاحظات" Style="{StaticResource LabelStyle}"/>
                <TextBox Name="TxtNotes" Style="{StaticResource InputFieldStyle}" 
                       MaxLength="500" Height="80" TextWrapping="Wrap" 
                       VerticalScrollBarVisibility="Auto"/>
            </StackPanel>
        </ScrollViewer>

        <!-- Buttons -->
        <StackPanel Grid.Row="2" Orientation="Horizontal" HorizontalAlignment="Center" 
                  Margin="0,20,0,0">
            <Button Name="BtnSave" Content="حفظ" Style="{StaticResource PrimaryButtonStyle}" 
                  Margin="0,0,10,0" Click="BtnSave_Click"/>
            <Button Name="BtnCancel" Content="إلغاء" Style="{StaticResource SecondaryButtonStyle}" 
                  Margin="10,0,0,0" Click="BtnCancel_Click"/>
        </StackPanel>
    </Grid>
</Window>
