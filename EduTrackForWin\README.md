# EduTrack for Windows - نظام إدارة المراكز التعليمية

## نظرة عامة
EduTrack for Windows هو نظام شامل لإدارة المراكز التعليمية مصمم خصيصاً للمراكز التعليمية والمدارس الخاصة في الوطن العربي. يوفر النظام حلولاً متكاملة لإدارة الطلاب والمعلمين والمجموعات والحصص والنظام المالي.

## الميزات الرئيسية

### 📊 لوحة التحكم
- عرض إحصائيات شاملة للمركز
- متابعة الأداء اليومي والشهري
- إشعارات وتنبيهات مهمة
- إجراءات سريعة للمهام الشائعة

### 👦 إدارة الطلاب
- تسجيل بيانات الطلاب الكاملة
- تتبع المعلومات الشخصية وبيانات الاتصال
- إدارة المجموعات والتخصصات
- تتبع الحالة المالية لكل طالب
- عرض تفاصيل شاملة لكل طالب

### 🧑‍🏫 إدارة المعلمين
- تسجيل بيانات المعلمين
- إدارة التخصصات والمؤهلات
- تتبع الرواتب (ثابت أو بالحصة)
- عرض جداول المعلمين
- تقارير الأداء والرواتب

### 🧩 إدارة المجموعات
- إنشاء وإدارة المجموعات الدراسية
- ربط المجموعات بالمعلمين
- إدارة طلاب كل مجموعة
- تحديد الرسوم الشهرية
- تتبع حالة المجموعات (نشطة/غير نشطة)

### 📅 إدارة الحصص
- جدولة الحصص
- تسجيل الحضور والغياب
- تتبع إكمال الحصص
- إدارة المواضيع والملاحظات
- عرض الجداول الأسبوعية

### ✅ نظام الحضور والغياب
- تسجيل حضور الطلاب
- إحصائيات الحضور
- تقارير الغياب
- تتبع الطلاب المتأخرين

### 💰 النظام المالي
- إدارة المدفوعات والرسوم
- تتبع المبالغ المستحقة
- إنشاء الإيصالات
- تقارير مالية شاملة
- إدارة أنواع الدفع المختلفة

### 📊 التقارير والإحصائيات
- تقارير مالية (شهرية/سنوية)
- تقارير الحضور والغياب
- تقارير الطلاب والمعلمين
- تقارير المجموعات والحصص
- تقارير مخصصة

### ⚙️ الإعدادات
- إعدادات المركز التعليمي
- الإعدادات المالية
- الإعدادات الأكاديمية
- إعدادات النظام

## التقنيات المستخدمة

- **Framework**: .NET 8 WPF
- **قاعدة البيانات**: SQLite مع Entity Framework Core
- **اللغة**: C#
- **واجهة المستخدم**: WPF مع تصميم Material Design
- **الهندسة المعمارية**: MVVM Pattern

## متطلبات النظام

- Windows 10 أو أحدث
- .NET 8 Runtime
- 4 GB RAM (الحد الأدنى)
- 500 MB مساحة تخزين
- دقة شاشة 1366x768 أو أعلى

## التثبيت والتشغيل

### 1. تثبيت .NET 8
```bash
# تحميل وتثبيت .NET 8 SDK من الموقع الرسمي
https://dotnet.microsoft.com/download/dotnet/8.0
```

### 2. استنساخ المشروع
```bash
git clone https://github.com/your-repo/EduTrackForWin.git
cd EduTrackForWin
```

### 3. استعادة الحزم
```bash
dotnet restore
```

### 4. بناء المشروع
```bash
dotnet build
```

### 5. تشغيل التطبيق
```bash
dotnet run
```

## هيكل المشروع

```
EduTrackForWin/
├── Models/              # نماذج البيانات
├── Data/               # سياق قاعدة البيانات
├── Services/           # خدمات الأعمال
├── Views/              # واجهات المستخدم
├── Helpers/            # أدوات مساعدة
├── Resources/          # الموارد والصور
└── App.xaml           # تطبيق WPF الرئيسي
```

## الاستخدام

### البدء السريع
1. شغل التطبيق
2. ستظهر لوحة التحكم الرئيسية
3. ابدأ بإضافة المعلمين من قائمة "المعلمين"
4. أضف الطلاب من قائمة "الطلاب"
5. أنشئ المجموعات وربطها بالمعلمين
6. ابدأ جدولة الحصص وتسجيل الحضور

### إدارة البيانات
- جميع البيانات تُحفظ محلياً في قاعدة بيانات SQLite
- يتم إنشاء قاعدة البيانات تلقائياً عند التشغيل الأول
- يمكن عمل نسخ احتياطية من ملف قاعدة البيانات

## المساهمة

نرحب بالمساهمات! يرجى:
1. عمل Fork للمشروع
2. إنشاء فرع جديد للميزة
3. تطبيق التغييرات
4. إرسال Pull Request

## الترخيص

هذا المشروع مرخص تحت رخصة MIT - راجع ملف [LICENSE](LICENSE) للتفاصيل.

## الدعم

للحصول على الدعم أو الإبلاغ عن مشاكل:
- افتح Issue جديد في GitHub
- راسلنا على البريد الإلكتروني: <EMAIL>

## خارطة الطريق

### الإصدار القادم (v2.0)
- [ ] نظام النسخ الاحتياطي التلقائي
- [ ] تصدير التقارير إلى PDF/Excel
- [ ] نظام الإشعارات المتقدم
- [ ] دعم قواعد البيانات السحابية
- [ ] تطبيق الهاتف المحمول

### المميزات المستقبلية
- [ ] نظام إدارة المحتوى التعليمي
- [ ] منصة التعلم الإلكتروني
- [ ] تكامل مع أنظمة الدفع الإلكتروني
- [ ] تحليلات متقدمة بالذكاء الاصطناعي

---

**تم تطوير هذا النظام بعناية لخدمة المراكز التعليمية في الوطن العربي** 🇸🇦🇪🇬🇦🇪🇯🇴🇱🇧🇸🇾🇮🇶🇰🇼🇶🇦🇧🇭🇴🇲🇾🇪🇱🇾🇹🇳🇩🇿🇲🇦🇸🇩🇲🇷🇩🇯🇸🇴🇰🇲
