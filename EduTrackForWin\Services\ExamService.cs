using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using EduTrackForWin.Data;
using EduTrackForWin.Models;
using Microsoft.EntityFrameworkCore;

namespace EduTrackForWin.Services
{
    public class ExamService
    {
        private readonly EduTrackDbContext _context;

        public ExamService()
        {
            _context = new EduTrackDbContext();
        }

        // Get all exams
        public async Task<List<Exam>> GetAllExamsAsync()
        {
            return await _context.Exams
                .Include(e => e.Group)
                .Include(e => e.ExamResults)
                .ThenInclude(er => er.Student)
                .OrderByDescending(e => e.ExamDate)
                .ToListAsync();
        }

        // Get exam by ID
        public async Task<Exam?> GetExamByIdAsync(int id)
        {
            return await _context.Exams
                .Include(e => e.Group)
                .Include(e => e.ExamResults)
                .ThenInclude(er => er.Student)
                .FirstOrDefaultAsync(e => e.Id == id);
        }

        // Get exams by group
        public async Task<List<Exam>> GetExamsByGroupAsync(int groupId)
        {
            return await _context.Exams
                .Include(e => e.Group)
                .Include(e => e.ExamResults)
                .ThenInclude(er => er.Student)
                .Where(e => e.GroupId == groupId)
                .OrderByDescending(e => e.ExamDate)
                .ToListAsync();
        }

        // Get upcoming exams
        public async Task<List<Exam>> GetUpcomingExamsAsync(int days = 7)
        {
            var startDate = DateTime.Today;
            var endDate = startDate.AddDays(days);

            return await _context.Exams
                .Include(e => e.Group)
                .Include(e => e.ExamResults)
                .Where(e => e.ExamDate >= startDate && e.ExamDate <= endDate && e.Status == ExamStatus.Scheduled)
                .OrderBy(e => e.ExamDate)
                .ThenBy(e => e.StartTime)
                .ToListAsync();
        }

        // Get exams by date range
        public async Task<List<Exam>> GetExamsByDateRangeAsync(DateTime startDate, DateTime endDate)
        {
            return await _context.Exams
                .Include(e => e.Group)
                .Include(e => e.ExamResults)
                .ThenInclude(er => er.Student)
                .Where(e => e.ExamDate >= startDate && e.ExamDate <= endDate)
                .OrderBy(e => e.ExamDate)
                .ThenBy(e => e.StartTime)
                .ToListAsync();
        }

        // Add new exam
        public async Task<Exam> AddExamAsync(Exam exam)
        {
            try
            {
                exam.CreatedAt = DateTime.Now;
                _context.Exams.Add(exam);
                await _context.SaveChangesAsync();

                // Auto-create exam results for all students in the group
                await CreateExamResultsForGroupAsync(exam.Id, exam.GroupId);

                return exam;
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في إضافة الاختبار: {ex.Message}");
            }
        }

        // Update exam
        public async Task<Exam> UpdateExamAsync(Exam exam)
        {
            try
            {
                var existingExam = await _context.Exams.FindAsync(exam.Id);
                if (existingExam == null)
                {
                    throw new Exception("الاختبار غير موجود");
                }

                existingExam.Title = exam.Title;
                existingExam.Description = exam.Description;
                existingExam.ExamDate = exam.ExamDate;
                existingExam.StartTime = exam.StartTime;
                existingExam.EndTime = exam.EndTime;
                existingExam.TotalMarks = exam.TotalMarks;
                existingExam.PassingMarks = exam.PassingMarks;
                existingExam.Type = exam.Type;
                existingExam.Status = exam.Status;
                existingExam.Instructions = exam.Instructions;
                existingExam.Location = exam.Location;
                existingExam.IsActive = exam.IsActive;
                existingExam.UpdatedAt = DateTime.Now;

                await _context.SaveChangesAsync();
                return existingExam;
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في تحديث الاختبار: {ex.Message}");
            }
        }

        // Delete exam
        public async Task DeleteExamAsync(int examId)
        {
            try
            {
                var exam = await _context.Exams.FindAsync(examId);
                if (exam == null)
                {
                    throw new Exception("الاختبار غير موجود");
                }

                _context.Exams.Remove(exam);
                await _context.SaveChangesAsync();
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في حذف الاختبار: {ex.Message}");
            }
        }

        // Create exam results for all students in a group
        private async Task CreateExamResultsForGroupAsync(int examId, int groupId)
        {
            try
            {
                var students = await _context.Students
                    .Where(s => s.GroupId == groupId)
                    .ToListAsync();

                foreach (var student in students)
                {
                    var examResult = new ExamResult
                    {
                        ExamId = examId,
                        StudentId = student.Id,
                        Marks = 0,
                        Status = ExamResultStatus.Present,
                        CreatedAt = DateTime.Now
                    };

                    _context.ExamResults.Add(examResult);
                }

                await _context.SaveChangesAsync();
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في إنشاء نتائج الاختبار: {ex.Message}");
            }
        }

        // Get exam statistics
        public async Task<ExamStatistics> GetExamStatisticsAsync(int examId)
        {
            try
            {
                var exam = await GetExamByIdAsync(examId);
                if (exam == null)
                {
                    throw new Exception("الاختبار غير موجود");
                }

                var results = exam.ExamResults.Where(r => !r.IsAbsent).ToList();
                
                var stats = new ExamStatistics
                {
                    ExamId = examId,
                    TotalStudents = exam.ExamResults.Count,
                    PresentStudents = results.Count,
                    AbsentStudents = exam.ExamResults.Count(r => r.IsAbsent),
                    PassedStudents = results.Count(r => r.IsPassed),
                    FailedStudents = results.Count(r => !r.IsPassed),
                    AverageMarks = results.Any() ? results.Average(r => r.TotalMarks) : 0,
                    HighestMarks = results.Any() ? results.Max(r => r.TotalMarks) : 0,
                    LowestMarks = results.Any() ? results.Min(r => r.TotalMarks) : 0,
                    PassingPercentage = results.Any() ? (results.Count(r => r.IsPassed) * 100m / results.Count) : 0
                };

                return stats;
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في حساب إحصائيات الاختبار: {ex.Message}");
            }
        }

        // Get group exam statistics
        public async Task<List<GroupExamStatistics>> GetGroupExamStatisticsAsync()
        {
            try
            {
                var groups = await _context.Groups
                    .Include(g => g.Students)
                    .ToListAsync();

                var statistics = new List<GroupExamStatistics>();

                foreach (var group in groups)
                {
                    var exams = await GetExamsByGroupAsync(group.Id);
                    var totalExams = exams.Count;
                    var completedExams = exams.Count(e => e.Status == ExamStatus.Completed);
                    var upcomingExams = exams.Count(e => e.Status == ExamStatus.Scheduled && e.ExamDate >= DateTime.Today);

                    var groupStats = new GroupExamStatistics
                    {
                        GroupId = group.Id,
                        GroupName = group.Name,
                        TotalStudents = group.Students.Count,
                        TotalExams = totalExams,
                        CompletedExams = completedExams,
                        UpcomingExams = upcomingExams,
                        AveragePassingRate = completedExams > 0 ? exams.Where(e => e.Status == ExamStatus.Completed).Average(e => e.PassingPercentage) : 0
                    };

                    statistics.Add(groupStats);
                }

                return statistics;
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في حساب إحصائيات المجموعات: {ex.Message}");
            }
        }

        public void Dispose()
        {
            _context?.Dispose();
        }
    }

    // Helper classes for statistics
    public class ExamStatistics
    {
        public int ExamId { get; set; }
        public int TotalStudents { get; set; }
        public int PresentStudents { get; set; }
        public int AbsentStudents { get; set; }
        public int PassedStudents { get; set; }
        public int FailedStudents { get; set; }
        public decimal AverageMarks { get; set; }
        public decimal HighestMarks { get; set; }
        public decimal LowestMarks { get; set; }
        public decimal PassingPercentage { get; set; }
    }

    public class GroupExamStatistics
    {
        public int GroupId { get; set; }
        public string GroupName { get; set; } = string.Empty;
        public int TotalStudents { get; set; }
        public int TotalExams { get; set; }
        public int CompletedExams { get; set; }
        public int UpcomingExams { get; set; }
        public decimal AveragePassingRate { get; set; }
    }
}
