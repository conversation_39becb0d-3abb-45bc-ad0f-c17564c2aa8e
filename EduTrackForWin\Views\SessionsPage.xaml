<Page x:Class="EduTrackForWin.Views.SessionsPage"
      xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
      xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
      xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
      xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
      xmlns:local="clr-namespace:EduTrackForWin.Views"
      mc:Ignorable="d"
      d:DesignHeight="450" d:DesignWidth="800"
      Title="الجدول الزمني">

    <Page.Resources>
        <!-- Card Style -->
        <Style x:Key="CardStyle" TargetType="Border" BasedOn="{StaticResource ModernCardStyle}">
            <Setter Property="Background" Value="{StaticResource SurfaceColor}"/>
            <Setter Property="CornerRadius" Value="8"/>
            <Setter Property="Padding" Value="20"/>
            <Setter Property="Margin" Value="10"/>
        </Style>

        <!-- Button Styles -->
        <Style x:Key="PrimaryButtonStyle" TargetType="Button">
            <Setter Property="Background" Value="{StaticResource AccentColor}"/>
            <Setter Property="Foreground" Value="White"/>
        </Style>

        <Style x:Key="SecondaryButtonStyle" TargetType="Button">
            <Setter Property="Background" Value="{StaticResource PrimaryColor}"/>
            <Setter Property="Foreground" Value="White"/>
        </Style>
    </Page.Resources>

    <Grid Background="#F5F5F5" Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <Border Grid.Row="0" Style="{StaticResource CardStyle}" Background="#F3E5F5">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <StackPanel Grid.Column="0">
                    <TextBlock Text="📅 الجدول الزمني" FontSize="24" FontWeight="Bold" Foreground="#7B1FA2"/>
                    <TextBlock Text="جدولة وإدارة الجداول الزمنية والحصص الدراسية" FontSize="14" Foreground="#424242" Margin="0,5,0,0"/>
                </StackPanel>

                <StackPanel Grid.Column="1" Orientation="Horizontal">
                    <Button Name="BtnAddSession" Content="+ إضافة حصة جديدة"
                          Style="{StaticResource PrimaryButtonStyle}"
                          FontSize="14" Padding="15,10" Margin="0,0,10,0" Click="BtnAddSession_Click"/>
                    <Button Name="BtnAddSchedule" Content="+ إضافة جدول زمني"
                          Style="{StaticResource PrimaryButtonStyle}"
                          FontSize="14" Padding="15,10" Click="BtnAddSchedule_Click"/>
                </StackPanel>
            </Grid>
        </Border>

        <!-- Filter and Calendar Controls -->
        <Border Grid.Row="1" Style="{StaticResource CardStyle}">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="200"/>
                    <ColumnDefinition Width="200"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <!-- Group Filter -->
                <ComboBox Grid.Column="0" Name="CmbGroupFilter"
                        Padding="10" FontSize="14" Margin="0,0,10,0"
                        BorderBrush="#E0E0E0" BorderThickness="1"
                        SelectionChanged="CmbGroupFilter_SelectionChanged">
                    <ComboBoxItem Content="جميع المجموعات" IsSelected="True"/>
                </ComboBox>

                <!-- Date Filter -->
                <DatePicker Grid.Column="1" Name="DpDateFilter"
                          Padding="10" FontSize="14" Margin="0,0,10,0"
                          BorderBrush="#E0E0E0" BorderThickness="1"
                          SelectedDateChanged="DpDateFilter_SelectedDateChanged"/>

                <!-- View Toggle -->
                <StackPanel Grid.Column="2" Orientation="Horizontal" VerticalAlignment="Center" Margin="10,0">
                    <RadioButton Name="RbListView" Content="عرض قائمة" IsChecked="True"
                               Margin="0,0,20,0" Checked="ViewMode_Changed"/>
                    <RadioButton Name="RbCalendarView" Content="عرض تقويم"
                               Checked="ViewMode_Changed"/>
                </StackPanel>

                <!-- Refresh Button -->
                <Button Grid.Column="3" Name="BtnRefresh" Content="🔄 تحديث"
                      Style="{StaticResource SecondaryButtonStyle}"
                      Click="BtnRefresh_Click"/>
            </Grid>
        </Border>

        <!-- Main Content -->
        <Grid Grid.Row="2">
            <!-- List View -->
            <Border Name="ListViewContainer" Style="{StaticResource CardStyle}">
                <StackPanel>
                    <TextBlock Text="قائمة الجدول الزمني" FontSize="18" FontWeight="SemiBold"
                             Margin="0,0,0,15" Foreground="#212121"/>

                    <DataGrid Name="DgSessions"
                            AutoGenerateColumns="False"
                            CanUserAddRows="False"
                            CanUserDeleteRows="False"
                            IsReadOnly="True"
                            GridLinesVisibility="Horizontal"
                            HeadersVisibility="Column"
                            BorderThickness="0"
                            Background="Transparent"
                            RowBackground="White"
                            AlternatingRowBackground="#F5F5F5"
                            SelectionMode="Single">

                        <DataGrid.Columns>
                            <DataGridTextColumn Header="التاريخ" Binding="{Binding Date, StringFormat='{}{0:yyyy/MM/dd}'}" Width="100"/>
                            <DataGridTextColumn Header="الوقت" Binding="{Binding SessionTime}" Width="120"/>
                            <DataGridTextColumn Header="المجموعة" Binding="{Binding Group.Name}" Width="150"/>
                            <DataGridTextColumn Header="المعلم" Binding="{Binding Teacher.Name}" Width="150"/>
                            <DataGridTextColumn Header="القاعة" Binding="{Binding Room}" Width="100"/>
                            <DataGridTextColumn Header="الموضوع" Binding="{Binding Topic}" Width="*"/>

                            <DataGridTemplateColumn Header="الحالة" Width="80">
                                <DataGridTemplateColumn.CellTemplate>
                                    <DataTemplate>
                                        <Border CornerRadius="10" Padding="8,4">
                                            <Border.Style>
                                                <Style TargetType="Border">
                                                    <Style.Triggers>
                                                        <DataTrigger Binding="{Binding IsCompleted}" Value="True">
                                                            <Setter Property="Background" Value="#4CAF50"/>
                                                        </DataTrigger>
                                                        <DataTrigger Binding="{Binding IsCompleted}" Value="False">
                                                            <Setter Property="Background" Value="#FF9800"/>
                                                        </DataTrigger>
                                                    </Style.Triggers>
                                                </Style>
                                            </Border.Style>
                                            <TextBlock Text="{Binding IsCompleted, Converter={StaticResource BoolToStatusConverter}}"
                                                     Foreground="White" FontSize="10" HorizontalAlignment="Center"/>
                                        </Border>
                                    </DataTemplate>
                                </DataGridTemplateColumn.CellTemplate>
                            </DataGridTemplateColumn>

                            <DataGridTemplateColumn Header="الإجراءات" Width="200">
                                <DataGridTemplateColumn.CellTemplate>
                                    <DataTemplate>
                                        <StackPanel Orientation="Horizontal">
                                            <Button Content="عرض" Background="#2196F3" Foreground="White"
                                                  Padding="8,4" Margin="2" BorderThickness="0"
                                                  Click="BtnViewSession_Click" Tag="{Binding Id}"/>
                                            <Button Content="تعديل" Background="#4CAF50" Foreground="White"
                                                  Padding="8,4" Margin="2" BorderThickness="0"
                                                  Click="BtnEditSession_Click" Tag="{Binding Id}"/>
                                            <Button Content="حذف" Background="#F44336" Foreground="White"
                                                  Padding="8,4" Margin="2" BorderThickness="0"
                                                  Click="BtnDeleteSession_Click" Tag="{Binding Id}"/>
                                        </StackPanel>
                                    </DataTemplate>
                                </DataGridTemplateColumn.CellTemplate>
                            </DataGridTemplateColumn>
                        </DataGrid.Columns>
                    </DataGrid>
                </StackPanel>
            </Border>

            <!-- Calendar View -->
            <Border Name="CalendarViewContainer" Style="{StaticResource CardStyle}" Visibility="Collapsed">
                <StackPanel>
                    <TextBlock Text="التقويم الأسبوعي" FontSize="18" FontWeight="SemiBold"
                             Margin="0,0,0,15" Foreground="#212121"/>

                    <Calendar Name="SessionCalendar" HorizontalAlignment="Center"
                            SelectedDatesChanged="SessionCalendar_SelectedDatesChanged"/>

                    <TextBlock Text="الحصص المحددة:" FontSize="14" FontWeight="SemiBold"
                             Margin="0,20,0,10" Foreground="#212121"/>

                    <ListBox Name="LbSelectedDateSessions" Height="200"
                           BorderThickness="1" BorderBrush="#E0E0E0">
                        <ListBox.ItemTemplate>
                            <DataTemplate>
                                <StackPanel Margin="5">
                                    <TextBlock Text="{Binding SessionTime}" FontWeight="Bold"/>
                                    <TextBlock Text="{Binding Group.Name}" FontSize="12" Foreground="#757575"/>
                                    <TextBlock Text="{Binding Topic}" FontSize="12"/>
                                </StackPanel>
                            </DataTemplate>
                        </ListBox.ItemTemplate>
                    </ListBox>
                </StackPanel>
            </Border>
        </Grid>
    </Grid>
</Page>
