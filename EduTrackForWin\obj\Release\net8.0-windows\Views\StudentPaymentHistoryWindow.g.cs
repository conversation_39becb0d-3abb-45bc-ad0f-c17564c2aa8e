﻿#pragma checksum "..\..\..\..\Views\StudentPaymentHistoryWindow.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "2218A44B6FD5116FA50C9775DACE6C815DC896AF"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace EduTrackForWin.Views {
    
    
    /// <summary>
    /// StudentPaymentHistoryWindow
    /// </summary>
    public partial class StudentPaymentHistoryWindow : System.Windows.Window, System.Windows.Markup.IComponentConnector, System.Windows.Markup.IStyleConnector {
        
        
        #line 80 "..\..\..\..\Views\StudentPaymentHistoryWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TxtStudentName;
        
        #line default
        #line hidden
        
        
        #line 81 "..\..\..\..\Views\StudentPaymentHistoryWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TxtStudentInfo;
        
        #line default
        #line hidden
        
        
        #line 85 "..\..\..\..\Views\StudentPaymentHistoryWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BtnAddPayment;
        
        #line default
        #line hidden
        
        
        #line 87 "..\..\..\..\Views\StudentPaymentHistoryWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BtnPrintReport;
        
        #line default
        #line hidden
        
        
        #line 115 "..\..\..\..\Views\StudentPaymentHistoryWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TxtTotalPaid;
        
        #line default
        #line hidden
        
        
        #line 126 "..\..\..\..\Views\StudentPaymentHistoryWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TxtTotalDue;
        
        #line default
        #line hidden
        
        
        #line 137 "..\..\..\..\Views\StudentPaymentHistoryWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TxtPaymentCount;
        
        #line default
        #line hidden
        
        
        #line 148 "..\..\..\..\Views\StudentPaymentHistoryWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TxtLastPayment;
        
        #line default
        #line hidden
        
        
        #line 161 "..\..\..\..\Views\StudentPaymentHistoryWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DataGrid DgPayments;
        
        #line default
        #line hidden
        
        
        #line 233 "..\..\..\..\Views\StudentPaymentHistoryWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BtnClose;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/EduTrackForWin;component/views/studentpaymenthistorywindow.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\Views\StudentPaymentHistoryWindow.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.TxtStudentName = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 2:
            this.TxtStudentInfo = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 3:
            this.BtnAddPayment = ((System.Windows.Controls.Button)(target));
            
            #line 86 "..\..\..\..\Views\StudentPaymentHistoryWindow.xaml"
            this.BtnAddPayment.Click += new System.Windows.RoutedEventHandler(this.BtnAddPayment_Click);
            
            #line default
            #line hidden
            return;
            case 4:
            this.BtnPrintReport = ((System.Windows.Controls.Button)(target));
            
            #line 90 "..\..\..\..\Views\StudentPaymentHistoryWindow.xaml"
            this.BtnPrintReport.Click += new System.Windows.RoutedEventHandler(this.BtnPrintReport_Click);
            
            #line default
            #line hidden
            return;
            case 5:
            this.TxtTotalPaid = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 6:
            this.TxtTotalDue = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 7:
            this.TxtPaymentCount = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 8:
            this.TxtLastPayment = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 9:
            this.DgPayments = ((System.Windows.Controls.DataGrid)(target));
            return;
            case 12:
            this.BtnClose = ((System.Windows.Controls.Button)(target));
            
            #line 236 "..\..\..\..\Views\StudentPaymentHistoryWindow.xaml"
            this.BtnClose.Click += new System.Windows.RoutedEventHandler(this.BtnClose_Click);
            
            #line default
            #line hidden
            return;
            }
            this._contentLoaded = true;
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        void System.Windows.Markup.IStyleConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 10:
            
            #line 209 "..\..\..\..\Views\StudentPaymentHistoryWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.BtnEditPayment_Click);
            
            #line default
            #line hidden
            break;
            case 11:
            
            #line 217 "..\..\..\..\Views\StudentPaymentHistoryWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.BtnDeletePayment_Click);
            
            #line default
            #line hidden
            break;
            }
        }
    }
}

