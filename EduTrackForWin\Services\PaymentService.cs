using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using EduTrackForWin.Data;
using EduTrackForWin.Models;
using Microsoft.EntityFrameworkCore;

namespace EduTrackForWin.Services
{
    public class PaymentService
    {
        private readonly EduTrackDbContext _context;

        public PaymentService()
        {
            _context = new EduTrackDbContext();
        }

        // Get all payments
        public async Task<List<Payment>> GetAllPaymentsAsync()
        {
            var payments = await _context.Payments
                .Include(p => p.Student)
                .OrderByDescending(p => p.PaymentDate)
                .ToListAsync();

            return payments;
        }

        // Get payment by ID
        public async Task<Payment?> GetPaymentByIdAsync(int id)
        {
            return await _context.Payments
                .Include(p => p.Student)
                .FirstOrDefaultAsync(p => p.Id == id);
        }

        // Get payments by student
        public async Task<List<Payment>> GetPaymentsByStudentAsync(int studentId)
        {
            var payments = await _context.Payments
                .Include(p => p.Student)
                .Where(p => p.StudentId == studentId)
                .OrderByDescending(p => p.PaymentDate)
                .ToListAsync();

            return payments;
        }

        // Get payments by date range
        public async Task<List<Payment>> GetPaymentsByDateRangeAsync(DateTime startDate, DateTime endDate)
        {
            var payments = await _context.Payments
                .Include(p => p.Student)
                .Where(p => p.PaymentDate >= startDate && p.PaymentDate <= endDate)
                .OrderByDescending(p => p.PaymentDate)
                .ToListAsync();

            return payments;
        }

        // Get payments by type
        public async Task<List<Payment>> GetPaymentsByTypeAsync(PaymentType paymentType)
        {
            var payments = await _context.Payments
                .Include(p => p.Student)
                .Where(p => p.Type == paymentType)
                .OrderByDescending(p => p.PaymentDate)
                .ToListAsync();

            return payments;
        }

        // Get today's payments
        public async Task<List<Payment>> GetTodayPaymentsAsync()
        {
            var today = DateTime.Today;
            var payments = await _context.Payments
                .Include(p => p.Student)
                .Where(p => p.PaymentDate.Date == today)
                .OrderByDescending(p => p.PaymentDate)
                .ToListAsync();

            return payments;
        }

        // Get monthly payments
        public async Task<List<Payment>> GetMonthlyPaymentsAsync(int year, int month)
        {
            var startDate = new DateTime(year, month, 1);
            var endDate = startDate.AddMonths(1).AddDays(-1);

            var payments = await _context.Payments
                .Include(p => p.Student)
                .Where(p => p.PaymentDate >= startDate && p.PaymentDate <= endDate)
                .OrderByDescending(p => p.PaymentDate)
                .ToListAsync();

            return payments;
        }

        // Add new payment
        public async Task<Payment> AddPaymentAsync(Payment payment)
        {
            try
            {
                payment.CreatedAt = DateTime.Now;
                _context.Payments.Add(payment);
                await _context.SaveChangesAsync();

                // Update student's total due
                await UpdateStudentTotalDue(payment.StudentId);

                return payment;
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في إضافة الدفعة: {ex.Message}");
            }
        }

        // Update payment
        public async Task<Payment> UpdatePaymentAsync(Payment payment)
        {
            try
            {
                var existingPayment = await _context.Payments.FindAsync(payment.Id);
                if (existingPayment == null)
                {
                    throw new Exception("الدفعة غير موجودة");
                }

                var oldStudentId = existingPayment.StudentId;

                existingPayment.StudentId = payment.StudentId;
                existingPayment.Type = payment.Type;
                existingPayment.Amount = payment.Amount;
                existingPayment.PaymentDate = payment.PaymentDate;
                existingPayment.PaymentMethod = payment.PaymentMethod;
                existingPayment.Notes = payment.Notes;

                await _context.SaveChangesAsync();

                // Update total due for both old and new students if changed
                if (oldStudentId != payment.StudentId)
                {
                    await UpdateStudentTotalDue(oldStudentId);
                }
                await UpdateStudentTotalDue(payment.StudentId);

                return existingPayment;
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في تحديث الدفعة: {ex.Message}");
            }
        }

        // Delete payment
        public async Task DeletePaymentAsync(int paymentId)
        {
            try
            {
                var payment = await _context.Payments.FindAsync(paymentId);
                if (payment == null)
                {
                    throw new Exception("الدفعة غير موجودة");
                }

                var studentId = payment.StudentId;
                _context.Payments.Remove(payment);
                await _context.SaveChangesAsync();

                // Update student's total due
                await UpdateStudentTotalDue(studentId);
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في حذف الدفعة: {ex.Message}");
            }
        }

        // Calculate total revenue
        public async Task<decimal> GetTotalRevenueAsync()
        {
            return await _context.Payments.SumAsync(p => p.Amount);
        }

        // Calculate monthly revenue
        public async Task<decimal> GetMonthlyRevenueAsync(int year, int month)
        {
            var startDate = new DateTime(year, month, 1);
            var endDate = startDate.AddMonths(1).AddDays(-1);

            return await _context.Payments
                .Where(p => p.PaymentDate >= startDate && p.PaymentDate <= endDate)
                .SumAsync(p => p.Amount);
        }

        // Calculate today's revenue
        public async Task<decimal> GetTodayRevenueAsync()
        {
            var today = DateTime.Today;
            return await _context.Payments
                .Where(p => p.PaymentDate.Date == today)
                .SumAsync(p => p.Amount);
        }

        // Get payment statistics
        public async Task<PaymentStatistics> GetPaymentStatisticsAsync()
        {
            var today = DateTime.Today;
            var thisMonth = new DateTime(today.Year, today.Month, 1);

            var stats = new PaymentStatistics
            {
                TotalRevenue = await GetTotalRevenueAsync(),
                MonthlyRevenue = await _context.Payments
                    .Where(p => p.PaymentDate >= thisMonth)
                    .SumAsync(p => p.Amount),
                TodayRevenue = await GetTodayRevenueAsync(),
                TotalPayments = await _context.Payments.CountAsync(),
                MonthlyPayments = await _context.Payments
                    .Where(p => p.PaymentDate >= thisMonth)
                    .CountAsync(),
                TodayPayments = await _context.Payments
                    .Where(p => p.PaymentDate.Date == today)
                    .CountAsync()
            };

            return stats;
        }

        // Update student's total due (private helper method)
        private async Task UpdateStudentTotalDue(int studentId)
        {
            try
            {
                var student = await _context.Students.FindAsync(studentId);
                if (student != null)
                {
                    // Calculate total payments for this student
                    var totalPayments = await _context.Payments
                        .Where(p => p.StudentId == studentId)
                        .SumAsync(p => p.Amount);

                    // Calculate total fees (this would depend on your business logic)
                    // For now, we'll keep the existing TotalDue and subtract payments
                    // You might want to implement a more sophisticated calculation
                    
                    // Update the student record
                    await _context.SaveChangesAsync();
                }
            }
            catch (Exception ex)
            {
                // Log error but don't throw to avoid breaking the main operation
                System.Diagnostics.Debug.WriteLine($"Error updating student total due: {ex.Message}");
            }
        }

        public void Dispose()
        {
            _context?.Dispose();
        }
    }

    // Helper class for payment statistics
    public class PaymentStatistics
    {
        public decimal TotalRevenue { get; set; }
        public decimal MonthlyRevenue { get; set; }
        public decimal TodayRevenue { get; set; }
        public int TotalPayments { get; set; }
        public int MonthlyPayments { get; set; }
        public int TodayPayments { get; set; }
    }
}
