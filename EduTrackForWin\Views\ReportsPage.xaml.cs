using System.Windows;
using System.Windows.Controls;
using System.Windows.Input;

namespace EduTrackForWin.Views
{
    public partial class ReportsPage : Page
    {
        public ReportsPage()
        {
            InitializeComponent();
        }

        private void FinancialReport_Click(object sender, MouseButtonEventArgs e)
        {
            if (sender is Border border && border.Tag is string reportType)
            {
                var message = reportType == "Monthly" ? 
                    "إنشاء التقرير المالي الشهري" : 
                    "إنشاء التقرير المالي السنوي";
                
                MessageBox.Show(message, "التقرير المالي", 
                              MessageBoxButton.OK, MessageBoxImage.Information);
            }
        }

        private void DuesReport_Click(object sender, MouseButtonEventArgs e)
        {
            MessageBox.Show("عرض تقرير الديون المستحقة", "تقرير الديون", 
                          MessageBoxButton.OK, MessageBoxImage.Information);
        }

        private void AttendanceReport_Click(object sender, MouseButtonEventArgs e)
        {
            MessageBox.Show("إنشاء تقرير الحضور والغياب", "تقرير الحضور", 
                          MessageBoxButton.OK, MessageBoxImage.Information);
        }

        private void StudentsReport_Click(object sender, MouseButtonEventArgs e)
        {
            MessageBox.Show("إنشاء تقرير الطلاب", "تقرير الطلاب", 
                          MessageBoxButton.OK, MessageBoxImage.Information);
        }

        private void TeachersReport_Click(object sender, MouseButtonEventArgs e)
        {
            MessageBox.Show("إنشاء تقرير المعلمين", "تقرير المعلمين", 
                          MessageBoxButton.OK, MessageBoxImage.Information);
        }

        private void GroupsReport_Click(object sender, MouseButtonEventArgs e)
        {
            MessageBox.Show("إنشاء تقرير المجموعات", "تقرير المجموعات", 
                          MessageBoxButton.OK, MessageBoxImage.Information);
        }

        private void SessionsReport_Click(object sender, MouseButtonEventArgs e)
        {
            MessageBox.Show("إنشاء تقرير الحصص", "تقرير الحصص", 
                          MessageBoxButton.OK, MessageBoxImage.Information);
        }

        private void CustomReport_Click(object sender, MouseButtonEventArgs e)
        {
            MessageBox.Show("إنشاء تقرير مخصص", "تقرير مخصص", 
                          MessageBoxButton.OK, MessageBoxImage.Information);
        }
    }
}
