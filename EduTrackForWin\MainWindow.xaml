﻿<Window x:Class="EduTrackForWin.MainWindow"
        xmlns:sys="clr-namespace:System;assembly=mscorlib"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:EduTrackForWin"
        mc:Ignorable="d"
        Title="EduTrack for Windows - نظام إدارة المركز التعليمي"
        Height="800" Width="1200"
        MinHeight="600" MinWidth="1000"
        WindowStartupLocation="CenterScreen"
        WindowState="Maximized">

    <Window.Resources>
        <!-- Define Colors -->
        <SolidColorBrush x:Key="PrimaryBrush" Color="#2196F3"/>
        <SolidColorBrush x:Key="SecondaryBrush" Color="#FFC107"/>
        <SolidColorBrush x:Key="SidebarBrush" Color="#263238"/>
        <SolidColorBrush x:Key="ContentBrush" Color="#FAFAFA"/>
        <SolidColorBrush x:Key="TextPrimaryBrush" Color="#212121"/>
        <SolidColorBrush x:Key="TextSecondaryBrush" Color="#757575"/>

        <!-- Button Styles -->
        <Style x:Key="SidebarButtonStyle" TargetType="Button">
            <Setter Property="Background" Value="Transparent"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Padding" Value="20,15"/>
            <Setter Property="HorizontalContentAlignment" Value="Left"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="FontFamily" Value="Segoe UI"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}"
                                BorderBrush="{TemplateBinding BorderBrush}"
                                BorderThickness="{TemplateBinding BorderThickness}">
                            <ContentPresenter HorizontalAlignment="{TemplateBinding HorizontalContentAlignment}"
                                            VerticalAlignment="{TemplateBinding VerticalContentAlignment}"
                                            Margin="{TemplateBinding Padding}"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="Background" Value="#37474F"/>
                            </Trigger>
                            <Trigger Property="IsPressed" Value="True">
                                <Setter Property="Background" Value="#455A64"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>
        
        <Style x:Key="ModernSidebarButtonStyle" TargetType="Button" BasedOn="{StaticResource SidebarButtonStyle}">
            <Setter Property="Background" Value="{StaticResource SurfaceColor}"/>
            <Setter Property="Foreground" Value="{StaticResource TextPrimaryColor}"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="BorderBrush" Value="{StaticResource BorderColor}"/>
            <Setter Property="Padding" Value="16,12"/>
            <Setter Property="HorizontalContentAlignment" Value="Center"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="FontWeight" Value="SemiBold"/>
            <Setter Property="FontFamily" Value="Segoe UI"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}"
                                BorderBrush="{TemplateBinding BorderBrush}"
                                BorderThickness="{TemplateBinding BorderThickness}"
                                CornerRadius="8">
                            <ContentPresenter HorizontalAlignment="{TemplateBinding HorizontalContentAlignment}"
                                            VerticalAlignment="{TemplateBinding VerticalContentAlignment}"
                                            Margin="{TemplateBinding Padding}"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="Background" Value="{StaticResource PrimaryLightColor}"/>
                                <Setter Property="Foreground" Value="White"/>
                            </Trigger>
                            <Trigger Property="IsPressed" Value="True">
                                <Setter Property="Background" Value="{StaticResource PrimaryDarkColor}"/>
                                <Setter Property="Foreground" Value="White"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>
    </Window.Resources>

    <Grid Background="{StaticResource BackgroundColor}">
        <Grid.ColumnDefinitions>
            <ColumnDefinition Width="280"/>
            <ColumnDefinition Width="*"/>
        </Grid.ColumnDefinitions>

        <!-- Page Transition Animation -->
        <Grid.RenderTransform>
            <TranslateTransform/>
        </Grid.RenderTransform>

        <Grid.Triggers>
            <EventTrigger RoutedEvent="Loaded">
                <BeginStoryboard Storyboard="{StaticResource FadeInAnimation}"/>
            </EventTrigger>
        </Grid.Triggers>

        <!-- Modern Sidebar -->
        <Border Grid.Column="0" Background="{StaticResource SurfaceColor}">
            <Border.Effect>
                <DropShadowEffect Color="#000000" Direction="0" ShadowDepth="4"
                                Opacity="0.1" BlurRadius="12"/>
            </Border.Effect>
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="*"/>
                    <RowDefinition Height="Auto"/>
                </Grid.RowDefinitions>

                <!-- Modern Header -->
                <Border Grid.Row="0" Background="{StaticResource PrimaryGradient}"
                      Padding="24" CornerRadius="0,0,16,16" Margin="0,0,0,20">
                    <StackPanel>
                        <TextBlock Text="📚 EduTrack" FontSize="28" FontWeight="Bold"
                                 Foreground="White" HorizontalAlignment="Center"/>
                        <TextBlock Text="نظام إدارة المركز التعليمي" FontSize="14"
                                 Foreground="White" HorizontalAlignment="Center"
                                 Margin="0,8,0,0" Opacity="0.9"/>
                    </StackPanel>
                </Border>

                <!-- Modern Navigation Menu -->
                <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto"
                            HorizontalScrollBarVisibility="Disabled">
                    <StackPanel Margin="16,0,16,0">
                        <!-- Dashboard Section -->
                        <TextBlock Text="الرئيسية" Style="{StaticResource CaptionStyle}"
                                 Margin="16,0,0,8" Foreground="{StaticResource TextSecondaryColor}"/>

                        <Button Name="BtnDashboard" Content="🏠 لوحة المعلومات"
                              Style="{StaticResource ModernSidebarButtonStyle}" Click="BtnDashboard_Click"/>

                        <!-- Management Section -->
                        <TextBlock Text="الإدارة" Style="{StaticResource CaptionStyle}"
                                 Margin="16,24,0,8" Foreground="{StaticResource TextSecondaryColor}"/>

                        <Button Name="BtnStudents" Content="👥 إدارة الطلاب"
                              Style="{StaticResource ModernSidebarButtonStyle}" Click="BtnStudents_Click"/>

                        <Button Name="BtnTeachers" Content="👨‍🏫 إدارة المعلمين"
                              Style="{StaticResource ModernSidebarButtonStyle}" Click="BtnTeachers_Click"/>

                        <Button Name="BtnGroups" Content="📚 إدارة المجموعات"
                              Style="{StaticResource ModernSidebarButtonStyle}" Click="BtnGroups_Click"/>

                        <Button Name="BtnSessions" Content="📅 إدارة الحصص"
                              Style="{StaticResource ModernSidebarButtonStyle}" Click="BtnSessions_Click"/>

                        <!-- Academic Section -->
                        <TextBlock Text="الأكاديمية" Style="{StaticResource CaptionStyle}"
                                 Margin="16,24,0,8" Foreground="{StaticResource TextSecondaryColor}"/>

                        <Button Name="BtnAttendance" Content="✅ الحضور والغياب"
                              Style="{StaticResource ModernSidebarButtonStyle}" Click="BtnAttendance_Click"/>

                        <Button Name="BtnExams" Content="📝 إدارة الاختبارات"
                              Style="{StaticResource ModernSidebarButtonStyle}" Click="BtnExams_Click"/>

                        <!-- Financial Section -->
                        <TextBlock Text="المالية" Style="{StaticResource CaptionStyle}"
                                 Margin="16,24,0,8" Foreground="{StaticResource TextSecondaryColor}"/>

                        <Button Name="BtnFinance" Content="💰 النظام المالي"
                              Style="{StaticResource ModernSidebarButtonStyle}" Click="BtnFinance_Click"/>

                        <Button Name="BtnStudentPayments" Content="💳 تسجيل دفع الطلاب"
                              Style="{StaticResource ModernSidebarButtonStyle}" Click="BtnStudentPayments_Click"/>

                        <Button Name="BtnOverdueManagement" Content="⚠️ إدارة المتأخرات"
                              Style="{StaticResource ModernSidebarButtonStyle}" Click="BtnOverdueManagement_Click"/>

                        <!-- Reports & Settings -->
                        <TextBlock Text="التقارير والإعدادات" Style="{StaticResource CaptionStyle}"
                                 Margin="16,24,0,8" Foreground="{StaticResource TextSecondaryColor}"/>

                        <Button Name="BtnReports" Content="📊 التقارير"
                              Style="{StaticResource ModernSidebarButtonStyle}" Click="BtnReports_Click"/>

                        <Button Name="BtnSettings" Content="⚙️ الإعدادات"
                              Style="{StaticResource ModernSidebarButtonStyle}" Click="BtnSettings_Click"/>
                    </StackPanel>
                </ScrollViewer>

                <!-- Footer -->
                <Border Grid.Row="2" Background="{StaticResource BackgroundColor}"
                      Padding="16" CornerRadius="16,16,0,0" Margin="0,20,0,0">
                    <StackPanel>
                        <TextBlock Text="الإصدار 2.0" Style="{StaticResource CaptionStyle}"
                                 HorizontalAlignment="Center"/>
                        <TextBlock Text="© 2024 EduTrack" Style="{StaticResource CaptionStyle}"
                                 HorizontalAlignment="Center" Margin="0,4,0,0"/>
                    </StackPanel>
                </Border>
            </Grid>
        </Border>

        <!-- Main Content Area -->
        <Grid Grid.Column="1" Background="{StaticResource ContentBrush}">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
            </Grid.RowDefinitions>

            <!-- Top Bar -->
            <Border Grid.Row="0" Background="White" BorderBrush="#E0E0E0" BorderThickness="0,0,0,1" Padding="20,15">
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>

                    <TextBlock Name="TxtPageTitle" Text="لوحة المعلومات"
                             FontSize="20" FontWeight="SemiBold"
                             Foreground="{StaticResource TextPrimaryBrush}"
                             VerticalAlignment="Center"/>

                    <StackPanel Grid.Column="1" Orientation="Horizontal">
                        <TextBlock Name="TxtCurrentDate" Text="{Binding Source={x:Static sys:DateTime.Now}, StringFormat='{}{0:yyyy/MM/dd}'}"
                                 FontSize="14" Foreground="{StaticResource TextSecondaryBrush}"
                                 VerticalAlignment="Center" Margin="0,0,20,0"
                                 xmlns:sys="clr-namespace:System;assembly=mscorlib"/>

                        <Button Name="BtnMinimize" Content="🗕" Width="30" Height="30"
                              Background="Transparent" BorderThickness="0"
                              Click="BtnMinimize_Click" ToolTip="تصغير"/>

                        <Button Name="BtnClose" Content="✕" Width="30" Height="30"
                              Background="Transparent" BorderThickness="0"
                              Foreground="Red" Click="BtnClose_Click" ToolTip="إغلاق"/>
                    </StackPanel>
                </Grid>
            </Border>

            <!-- Content Frame -->
            <Frame Name="MainFrame" Grid.Row="1" NavigationUIVisibility="Hidden"
                   Margin="20" Background="Transparent"/>
        </Grid>
    </Grid>
</Window>
