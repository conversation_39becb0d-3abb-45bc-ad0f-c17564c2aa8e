﻿#pragma checksum "..\..\..\..\Views\GroupsPage.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "9CE0B8EC0F79789369A7ABE3AC62A5C222B26CDA"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using EduTrackForWin.Views;
using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace EduTrackForWin.Views {
    
    
    /// <summary>
    /// GroupsPage
    /// </summary>
    public partial class GroupsPage : System.Windows.Controls.Page, System.Windows.Markup.IComponentConnector, System.Windows.Markup.IStyleConnector {
        
        
        #line 77 "..\..\..\..\Views\GroupsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BtnAddGroup;
        
        #line default
        #line hidden
        
        
        #line 98 "..\..\..\..\Views\GroupsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox TxtSearch;
        
        #line default
        #line hidden
        
        
        #line 141 "..\..\..\..\Views\GroupsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox CmbTeacherFilter;
        
        #line default
        #line hidden
        
        
        #line 149 "..\..\..\..\Views\GroupsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox CmbStatusFilter;
        
        #line default
        #line hidden
        
        
        #line 159 "..\..\..\..\Views\GroupsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BtnRefresh;
        
        #line default
        #line hidden
        
        
        #line 183 "..\..\..\..\Views\GroupsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DataGrid DgGroups;
        
        #line default
        #line hidden
        
        
        #line 248 "..\..\..\..\Views\GroupsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TxtNoData;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/EduTrackForWin;component/views/groupspage.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\Views\GroupsPage.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.BtnAddGroup = ((System.Windows.Controls.Button)(target));
            
            #line 79 "..\..\..\..\Views\GroupsPage.xaml"
            this.BtnAddGroup.Click += new System.Windows.RoutedEventHandler(this.BtnAddGroup_Click);
            
            #line default
            #line hidden
            return;
            case 2:
            this.TxtSearch = ((System.Windows.Controls.TextBox)(target));
            
            #line 101 "..\..\..\..\Views\GroupsPage.xaml"
            this.TxtSearch.TextChanged += new System.Windows.Controls.TextChangedEventHandler(this.TxtSearch_TextChanged);
            
            #line default
            #line hidden
            return;
            case 3:
            this.CmbTeacherFilter = ((System.Windows.Controls.ComboBox)(target));
            
            #line 144 "..\..\..\..\Views\GroupsPage.xaml"
            this.CmbTeacherFilter.SelectionChanged += new System.Windows.Controls.SelectionChangedEventHandler(this.CmbTeacherFilter_SelectionChanged);
            
            #line default
            #line hidden
            return;
            case 4:
            this.CmbStatusFilter = ((System.Windows.Controls.ComboBox)(target));
            
            #line 152 "..\..\..\..\Views\GroupsPage.xaml"
            this.CmbStatusFilter.SelectionChanged += new System.Windows.Controls.SelectionChangedEventHandler(this.CmbStatusFilter_SelectionChanged);
            
            #line default
            #line hidden
            return;
            case 5:
            this.BtnRefresh = ((System.Windows.Controls.Button)(target));
            
            #line 162 "..\..\..\..\Views\GroupsPage.xaml"
            this.BtnRefresh.Click += new System.Windows.RoutedEventHandler(this.BtnRefresh_Click);
            
            #line default
            #line hidden
            return;
            case 6:
            this.DgGroups = ((System.Windows.Controls.DataGrid)(target));
            return;
            case 11:
            this.TxtNoData = ((System.Windows.Controls.TextBlock)(target));
            return;
            }
            this._contentLoaded = true;
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        void System.Windows.Markup.IStyleConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 7:
            
            #line 233 "..\..\..\..\Views\GroupsPage.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.BtnViewGroup_Click);
            
            #line default
            #line hidden
            break;
            case 8:
            
            #line 235 "..\..\..\..\Views\GroupsPage.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.BtnEditGroup_Click);
            
            #line default
            #line hidden
            break;
            case 9:
            
            #line 237 "..\..\..\..\Views\GroupsPage.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.BtnManageStudents_Click);
            
            #line default
            #line hidden
            break;
            case 10:
            
            #line 239 "..\..\..\..\Views\GroupsPage.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.BtnDeleteGroup_Click);
            
            #line default
            #line hidden
            break;
            }
        }
    }
}

