using System.Windows;
using EduTrackForWin.Models;
using EduTrackForWin.Data;

namespace EduTrackForWin.Views
{
    public partial class AddEditTeacherWindow : Window
    {
        private readonly EduTrackDbContext _context;
        private Teacher? _currentTeacher;
        private bool _isEditMode;

        public AddEditTeacherWindow(Teacher? teacher = null)
        {
            try
            {
                InitializeComponent();
                _context = new EduTrackDbContext();
                _currentTeacher = teacher;
                _isEditMode = teacher != null;

                InitializeWindow();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تهيئة نافذة المعلم: {ex.Message}\n\nتفاصيل الخطأ: {ex.StackTrace}",
                              "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
                throw;
            }
        }

        private void InitializeWindow()
        {
            try
            {
                if (_isEditMode && _currentTeacher != null)
                {
                    if (TxtTitle != null) TxtTitle.Text = "تعديل بيانات المعلم";
                    Title = "تعديل بيانات المعلم";
                    if (BtnSave != null) BtnSave.Content = "تحديث";
                    LoadTeacherData();
                }
                else
                {
                    if (TxtTitle != null) TxtTitle.Text = "إضافة معلم جديد";
                    Title = "إضافة معلم جديد";
                    if (BtnSave != null) BtnSave.Content = "حفظ";
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تهيئة النافذة: {ex.Message}", "خطأ",
                              MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void LoadTeacherData()
        {
            try
            {
                if (_currentTeacher == null) return;

                if (TxtName != null) TxtName.Text = _currentTeacher.Name ?? "";
                if (TxtPhone != null) TxtPhone.Text = _currentTeacher.Phone ?? "";
                if (TxtEmail != null) TxtEmail.Text = _currentTeacher.Email ?? "";
                if (TxtNotes != null) TxtNotes.Text = _currentTeacher.Notes ?? "";

                // Set specialization
                if (!string.IsNullOrEmpty(_currentTeacher.Specialization) && CmbSpecialization != null)
                {
                    CmbSpecialization.Text = _currentTeacher.Specialization;
                }

                // Set salary type and values
                if (_currentTeacher.IsFixedSalary)
                {
                    if (RbFixedSalary != null) RbFixedSalary.IsChecked = true;
                    if (TxtMonthlySalary != null) TxtMonthlySalary.Text = _currentTeacher.MonthlySalary.ToString();
                }
                else
                {
                    if (RbPerSession != null) RbPerSession.IsChecked = true;
                    if (TxtSalaryPerSession != null) TxtSalaryPerSession.Text = _currentTeacher.SalaryPerSession.ToString();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل بيانات المعلم: {ex.Message}", "خطأ",
                              MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void SalaryType_Changed(object sender, RoutedEventArgs e)
        {
            try
            {
                if (RbFixedSalary?.IsChecked == true)
                {
                    if (PnlMonthlySalary != null) PnlMonthlySalary.Visibility = Visibility.Visible;
                    if (PnlPerSessionSalary != null) PnlPerSessionSalary.Visibility = Visibility.Collapsed;
                }
                else
                {
                    if (PnlMonthlySalary != null) PnlMonthlySalary.Visibility = Visibility.Collapsed;
                    if (PnlPerSessionSalary != null) PnlPerSessionSalary.Visibility = Visibility.Visible;
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تغيير نوع الراتب: {ex.Message}", "خطأ",
                              MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async void BtnSave_Click(object sender, RoutedEventArgs e)
        {
            if (!ValidateInput())
                return;

            try
            {
                var teacher = _currentTeacher ?? new Teacher();

                // Update teacher properties
                teacher.Name = TxtName?.Text?.Trim() ?? "";
                teacher.Phone = TxtPhone?.Text?.Trim() ?? "";
                teacher.Email = TxtEmail?.Text?.Trim() ?? "";
                teacher.Notes = TxtNotes?.Text?.Trim() ?? "";
                teacher.Specialization = CmbSpecialization?.Text?.Trim() ?? "";

                // Set salary information
                teacher.IsFixedSalary = RbFixedSalary?.IsChecked == true;

                if (teacher.IsFixedSalary)
                {
                    if (TxtMonthlySalary != null && decimal.TryParse(TxtMonthlySalary.Text, out var monthlySalary))
                    {
                        teacher.MonthlySalary = monthlySalary;
                    }
                    teacher.SalaryPerSession = 0;
                }
                else
                {
                    if (TxtSalaryPerSession != null && decimal.TryParse(TxtSalaryPerSession.Text, out var salaryPerSession))
                    {
                        teacher.SalaryPerSession = salaryPerSession;
                    }
                    teacher.MonthlySalary = 0;
                }

                if (_isEditMode)
                {
                    teacher.UpdatedAt = DateTime.Now;
                    _context.Teachers.Update(teacher);
                    await _context.SaveChangesAsync();
                    MessageBox.Show("تم تحديث بيانات المعلم بنجاح", "نجح", 
                                  MessageBoxButton.OK, MessageBoxImage.Information);
                }
                else
                {
                    teacher.CreatedAt = DateTime.Now;
                    _context.Teachers.Add(teacher);
                    await _context.SaveChangesAsync();
                    MessageBox.Show("تم إضافة المعلم بنجاح", "نجح", 
                                  MessageBoxButton.OK, MessageBoxImage.Information);
                }

                DialogResult = true;
                Close();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في حفظ بيانات المعلم: {ex.Message}", "خطأ", 
                              MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private bool ValidateInput()
        {
            if (TxtName == null || string.IsNullOrWhiteSpace(TxtName.Text))
            {
                MessageBox.Show("يرجى إدخال اسم المعلم", "خطأ في البيانات",
                              MessageBoxButton.OK, MessageBoxImage.Warning);
                TxtName?.Focus();
                return false;
            }

            if (TxtName.Text.Trim().Length < 2)
            {
                MessageBox.Show("يجب أن يكون اسم المعلم أكثر من حرفين", "خطأ في البيانات", 
                              MessageBoxButton.OK, MessageBoxImage.Warning);
                TxtName.Focus();
                return false;
            }

            // Validate email format if provided
            if (TxtEmail != null && !string.IsNullOrWhiteSpace(TxtEmail.Text))
            {
                var email = TxtEmail.Text.Trim();
                if (!email.Contains("@") || !email.Contains("."))
                {
                    MessageBox.Show("يرجى إدخال بريد إلكتروني صحيح", "خطأ في البيانات",
                                  MessageBoxButton.OK, MessageBoxImage.Warning);
                    TxtEmail.Focus();
                    return false;
                }
            }

            // Validate salary values
            if (RbFixedSalary?.IsChecked == true)
            {
                if (TxtMonthlySalary == null || !decimal.TryParse(TxtMonthlySalary.Text, out var monthlySalary) || monthlySalary < 0)
                {
                    MessageBox.Show("يرجى إدخال راتب شهري صحيح", "خطأ في البيانات",
                                  MessageBoxButton.OK, MessageBoxImage.Warning);
                    TxtMonthlySalary?.Focus();
                    return false;
                }
            }
            else
            {
                if (TxtSalaryPerSession == null || !decimal.TryParse(TxtSalaryPerSession.Text, out var salaryPerSession) || salaryPerSession < 0)
                {
                    MessageBox.Show("يرجى إدخال راتب لكل حصة صحيح", "خطأ في البيانات",
                                  MessageBoxButton.OK, MessageBoxImage.Warning);
                    TxtSalaryPerSession?.Focus();
                    return false;
                }
            }

            return true;
        }

        private void BtnCancel_Click(object sender, RoutedEventArgs e)
        {
            DialogResult = false;
            Close();
        }

        protected override void OnClosed(EventArgs e)
        {
            _context?.Dispose();
            base.OnClosed(e);
        }
    }
}
