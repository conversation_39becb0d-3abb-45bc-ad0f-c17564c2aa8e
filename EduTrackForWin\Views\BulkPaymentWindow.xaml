<Window x:Class="EduTrackForWin.Views.BulkPaymentWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        mc:Ignorable="d"
        Title="دفع جماعي" Height="700" Width="900"
        WindowStartupLocation="CenterScreen">

    <Window.Resources>
        <!-- Styles -->
        <Style x:Key="HeaderStyle" TargetType="TextBlock">
            <Setter Property="FontSize" Value="20"/>
            <Setter Property="FontWeight" Value="Bold"/>
            <Setter Property="Foreground" Value="#2E7D32"/>
            <Setter Property="Margin" Value="0,0,0,15"/>
        </Style>

        <Style x:Key="LabelStyle" TargetType="TextBlock">
            <Setter Property="FontWeight" Value="SemiBold"/>
            <Setter Property="Margin" Value="0,10,0,5"/>
            <Setter Property="Foreground" Value="#424242"/>
        </Style>

        <Style x:Key="ComboBoxStyle" TargetType="ComboBox">
            <Setter Property="Padding" Value="10"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="BorderBrush" Value="#E0E0E0"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="Margin" Value="0,5,0,15"/>
        </Style>

        <Style x:Key="InputFieldStyle" TargetType="TextBox">
            <Setter Property="Padding" Value="10"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="BorderBrush" Value="#E0E0E0"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="Margin" Value="0,5,0,15"/>
        </Style>

        <Style x:Key="PrimaryButtonStyle" TargetType="Button">
            <Setter Property="Background" Value="#4CAF50"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Padding" Value="15,8"/>
            <Setter Property="Margin" Value="5"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}" 
                              CornerRadius="5">
                            <ContentPresenter HorizontalAlignment="Center" 
                                            VerticalAlignment="Center"
                                            Margin="{TemplateBinding Padding}"/>
                        </Border>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <Style x:Key="SecondaryButtonStyle" TargetType="Button">
            <Setter Property="Background" Value="#2196F3"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Padding" Value="15,8"/>
            <Setter Property="Margin" Value="5"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}" 
                              CornerRadius="5">
                            <ContentPresenter HorizontalAlignment="Center" 
                                            VerticalAlignment="Center"
                                            Margin="{TemplateBinding Padding}"/>
                        </Border>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>
    </Window.Resources>

    <Grid Background="#F5F5F5">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <Border Grid.Row="0" Background="White" Padding="20" Margin="20,20,20,10">
            <Border.Effect>
                <DropShadowEffect Color="Gray" Direction="270" ShadowDepth="1" Opacity="0.2" BlurRadius="5"/>
            </Border.Effect>
            
            <TextBlock Text="👥 دفع جماعي للطلاب" Style="{StaticResource HeaderStyle}"/>
        </Border>

        <!-- Payment Settings -->
        <Border Grid.Row="1" Background="White" Padding="20" Margin="20,10,20,10">
            <Border.Effect>
                <DropShadowEffect Color="Gray" Direction="270" ShadowDepth="1" Opacity="0.2" BlurRadius="5"/>
            </Border.Effect>
            
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>

                <!-- Group Filter -->
                <StackPanel Grid.Column="0" Margin="0,0,10,0">
                    <TextBlock Text="المجموعة *" Style="{StaticResource LabelStyle}"/>
                    <ComboBox Name="CmbGroup" Style="{StaticResource ComboBoxStyle}" 
                            DisplayMemberPath="Name" SelectedValuePath="Id"
                            SelectionChanged="CmbGroup_SelectionChanged"/>
                </StackPanel>

                <!-- Payment Type -->
                <StackPanel Grid.Column="1" Margin="10,0,10,0">
                    <TextBlock Text="نوع الدفعة *" Style="{StaticResource LabelStyle}"/>
                    <ComboBox Name="CmbPaymentType" Style="{StaticResource ComboBoxStyle}">
                        <ComboBoxItem Content="اشتراك شهري" IsSelected="True"/>
                        <ComboBoxItem Content="رسوم تسجيل"/>
                        <ComboBoxItem Content="رسوم إضافية"/>
                    </ComboBox>
                </StackPanel>

                <!-- Amount -->
                <StackPanel Grid.Column="2" Margin="10,0,10,0">
                    <TextBlock Text="المبلغ *" Style="{StaticResource LabelStyle}"/>
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                        </Grid.ColumnDefinitions>
                        
                        <TextBox Name="TxtAmount" Grid.Column="0" Style="{StaticResource InputFieldStyle}"/>
                        <Button Name="BtnUseGroupFee" Grid.Column="1" Content="رسوم المجموعة" 
                              Background="#FF9800" Foreground="White" BorderThickness="0" 
                              Padding="8" Margin="5,5,0,15" Click="BtnUseGroupFee_Click"/>
                    </Grid>
                </StackPanel>

                <!-- Payment Method -->
                <StackPanel Grid.Column="3" Margin="10,0,0,0">
                    <TextBlock Text="طريقة الدفع *" Style="{StaticResource LabelStyle}"/>
                    <ComboBox Name="CmbPaymentMethod" Style="{StaticResource ComboBoxStyle}">
                        <ComboBoxItem Content="نقداً" IsSelected="True"/>
                        <ComboBoxItem Content="بطاقة ائتمان"/>
                        <ComboBoxItem Content="تحويل بنكي"/>
                        <ComboBoxItem Content="شيك"/>
                        <ComboBoxItem Content="محفظة إلكترونية"/>
                    </ComboBox>
                </StackPanel>
            </Grid>
        </Border>

        <!-- Students List -->
        <Border Grid.Row="2" Background="White" Padding="20" Margin="20,10,20,10">
            <Border.Effect>
                <DropShadowEffect Color="Gray" Direction="270" ShadowDepth="1" Opacity="0.2" BlurRadius="5"/>
            </Border.Effect>
            
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="*"/>
                </Grid.RowDefinitions>

                <!-- Controls -->
                <StackPanel Grid.Row="0" Orientation="Horizontal" Margin="0,0,0,15">
                    <CheckBox Name="ChkSelectAll" Content="تحديد الكل" 
                            Checked="ChkSelectAll_Checked" Unchecked="ChkSelectAll_Unchecked"/>
                    <TextBlock Name="TxtSelectedCount" Text="لم يتم تحديد أي طالب" 
                             Margin="20,0,0,0" FontWeight="SemiBold"/>
                    <TextBlock Name="TxtTotalAmount" Text="المجموع: 0 ج.م" 
                             Margin="20,0,0,0" FontWeight="Bold" Foreground="Green"/>
                </StackPanel>

                <!-- Students DataGrid -->
                <DataGrid Name="DgStudents" Grid.Row="1"
                        AutoGenerateColumns="False" 
                        CanUserAddRows="False"
                        CanUserDeleteRows="False"
                        GridLinesVisibility="Horizontal"
                        HeadersVisibility="Column"
                        AlternatingRowBackground="#F9F9F9"
                        RowBackground="White"
                        BorderBrush="#E0E0E0"
                        BorderThickness="1">
                    
                    <DataGrid.Columns>
                        <!-- Select -->
                        <DataGridTemplateColumn Header="تحديد" Width="60">
                            <DataGridTemplateColumn.CellTemplate>
                                <DataTemplate>
                                    <CheckBox IsChecked="{Binding IsSelected, UpdateSourceTrigger=PropertyChanged}" 
                                            Checked="StudentCheckBox_Changed" Unchecked="StudentCheckBox_Changed"/>
                                </DataTemplate>
                            </DataGridTemplateColumn.CellTemplate>
                        </DataGridTemplateColumn>
                        
                        <!-- Student Name -->
                        <DataGridTextColumn Header="اسم الطالب" Binding="{Binding Name}" Width="200"/>
                        
                        <!-- Phone -->
                        <DataGridTextColumn Header="الهاتف" Binding="{Binding Phone}" Width="120"/>
                        
                        <!-- Total Due -->
                        <DataGridTextColumn Header="المستحق" Width="100">
                            <DataGridTextColumn.Binding>
                                <Binding Path="TotalDue" StringFormat="{}{0:C}"/>
                            </DataGridTextColumn.Binding>
                        </DataGridTextColumn>
                        
                        <!-- Payment Status -->
                        <DataGridTextColumn Header="حالة الدفع" Binding="{Binding PaymentStatus}" Width="100"/>
                        
                        <!-- Custom Amount -->
                        <DataGridTemplateColumn Header="مبلغ مخصص" Width="120">
                            <DataGridTemplateColumn.CellTemplate>
                                <DataTemplate>
                                    <TextBox Text="{Binding CustomAmount, UpdateSourceTrigger=PropertyChanged}" 
                                           TextChanged="CustomAmount_TextChanged"/>
                                </DataTemplate>
                            </DataGridTemplateColumn.CellTemplate>
                        </DataGridTemplateColumn>
                    </DataGrid.Columns>
                </DataGrid>
            </Grid>
        </Border>

        <!-- Footer -->
        <Border Grid.Row="3" Background="White" Padding="20" Margin="20,10,20,20">
            <Border.Effect>
                <DropShadowEffect Color="Gray" Direction="270" ShadowDepth="1" Opacity="0.2" BlurRadius="5"/>
            </Border.Effect>
            
            <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                <Button Name="BtnProcessPayments" Content="معالجة المدفوعات" 
                      Style="{StaticResource PrimaryButtonStyle}" 
                      Click="BtnProcessPayments_Click"/>
                <Button Name="BtnCancel" Content="إلغاء" 
                      Style="{StaticResource SecondaryButtonStyle}" 
                      Click="BtnCancel_Click"/>
            </StackPanel>
        </Border>
    </Grid>
</Window>
