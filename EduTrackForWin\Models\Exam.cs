using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace EduTrackForWin.Models
{
    public class Exam
    {
        [Key]
        public int Id { get; set; }

        [Required]
        [StringLength(200)]
        public string Title { get; set; } = string.Empty;

        [StringLength(1000)]
        public string Description { get; set; } = string.Empty;

        [Required]
        public int GroupId { get; set; }

        [ForeignKey("GroupId")]
        public Group Group { get; set; } = null!;

        [Required]
        public DateTime ExamDate { get; set; }

        [Required]
        public TimeSpan StartTime { get; set; }

        [Required]
        public TimeSpan EndTime { get; set; }

        [Column(TypeName = "decimal(5,2)")]
        public decimal TotalMarks { get; set; }

        [Column(TypeName = "decimal(5,2)")]
        public decimal PassingMarks { get; set; }

        public ExamType Type { get; set; } = ExamType.Written;

        public ExamStatus Status { get; set; } = ExamStatus.Scheduled;

        [StringLength(500)]
        public string Instructions { get; set; } = string.Empty;

        [StringLength(200)]
        public string Location { get; set; } = string.Empty;

        public bool IsActive { get; set; } = true;

        // Navigation Properties
        public ICollection<ExamResult> ExamResults { get; set; } = new List<ExamResult>();

        // Computed Properties
        [NotMapped]
        public int Duration => (int)(EndTime - StartTime).TotalMinutes;

        [NotMapped]
        public int StudentsCount => ExamResults?.Count ?? 0;

        [NotMapped]
        public int PassedStudents => ExamResults?.Count(r => r.Marks >= PassingMarks) ?? 0;

        [NotMapped]
        public int FailedStudents => ExamResults?.Count(r => r.Marks < PassingMarks) ?? 0;

        [NotMapped]
        public decimal AverageMarks => ExamResults?.Any() == true ? ExamResults.Average(r => r.Marks) : 0;

        [NotMapped]
        public decimal PassingPercentage => StudentsCount > 0 ? (PassedStudents * 100m / StudentsCount) : 0;

        [NotMapped]
        public string StatusName => Status switch
        {
            ExamStatus.Scheduled => "مجدول",
            ExamStatus.InProgress => "جاري",
            ExamStatus.Completed => "مكتمل",
            ExamStatus.Cancelled => "ملغي",
            _ => "غير محدد"
        };

        [NotMapped]
        public string TypeName => Type switch
        {
            ExamType.Written => "كتابي",
            ExamType.Oral => "شفهي",
            ExamType.Practical => "عملي",
            ExamType.Online => "إلكتروني",
            _ => "غير محدد"
        };

        public DateTime CreatedAt { get; set; } = DateTime.Now;
        public DateTime? UpdatedAt { get; set; }
    }

    public enum ExamType
    {
        Written = 1,    // كتابي
        Oral = 2,       // شفهي
        Practical = 3,  // عملي
        Online = 4      // إلكتروني
    }

    public enum ExamStatus
    {
        Scheduled = 1,  // مجدول
        InProgress = 2, // جاري
        Completed = 3,  // مكتمل
        Cancelled = 4   // ملغي
    }
}
