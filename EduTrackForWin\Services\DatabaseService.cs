using Microsoft.EntityFrameworkCore;
using EduTrackForWin.Data;
using System.IO;
using EduTrackForWin.Models;

namespace EduTrackForWin.Services
{
    public class DatabaseService
    {
        private readonly EduTrackDbContext _context;

        public DatabaseService()
        {
            _context = new EduTrackDbContext();
        }

        public async Task InitializeDatabaseAsync()
        {
            try
            {
                await _context.Database.EnsureCreatedAsync();

                // Update database schema if needed
                await UpdateDatabaseSchemaAsync();

                // Check if settings exist, if not create default
                if (!await _context.Settings.AnyAsync())
                {
                    var defaultSettings = new Settings
                    {
                        CenterName = "مركز EduTrack التعليمي",
                        CenterAddress = "العنوان",
                        CenterPhone = "123456789",
                        CenterEmail = "<EMAIL>",
                        DefaultMonthlyFee = 100,
                        DarkMode = false,
                        AutoBackup = true,
                        BackupIntervalDays = 1,
                        MaxAbsenceWarning = 3,
                        ReportHeader = "تقرير مركز EduTrack التعليمي",
                        ReportFooter = "شكراً لثقتكم بنا"
                    };

                    _context.Settings.Add(defaultSettings);
                    await _context.SaveChangesAsync();
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في تهيئة قاعدة البيانات: {ex.Message}");
            }
        }

        private async Task UpdateDatabaseSchemaAsync()
        {
            try
            {
                // Check if BillingDay column exists in Groups table
                var connection = _context.Database.GetDbConnection();
                await connection.OpenAsync();

                var command = connection.CreateCommand();
                command.CommandText = "PRAGMA table_info(Groups)";

                var reader = await command.ExecuteReaderAsync();
                bool billingDayExists = false;

                while (await reader.ReadAsync())
                {
                    var columnName = reader.GetString(1); // Column name is at index 1
                    if (columnName == "BillingDay")
                    {
                        billingDayExists = true;
                        break;
                    }
                }

                await reader.CloseAsync();

                // Add BillingDay column if it doesn't exist
                if (!billingDayExists)
                {
                    command.CommandText = "ALTER TABLE Groups ADD COLUMN BillingDay INTEGER DEFAULT 1";
                    await command.ExecuteNonQueryAsync();
                }

                await connection.CloseAsync();
            }
            catch (Exception ex)
            {
                // Log error but don't throw to avoid breaking initialization
                System.Diagnostics.Debug.WriteLine($"Error updating database schema: {ex.Message}");
            }
        }

        public async Task<bool> BackupDatabaseAsync(string backupPath)
        {
            try
            {
                var sourceFile = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData), 
                                            "EduTrackForWin", "edutrack.db");
                
                if (!File.Exists(sourceFile))
                    return false;

                var backupFileName = $"edutrack_backup_{DateTime.Now:yyyyMMdd_HHmmss}.db";
                var fullBackupPath = Path.Combine(backupPath, backupFileName);
                
                // Ensure backup directory exists
                Directory.CreateDirectory(backupPath);
                
                File.Copy(sourceFile, fullBackupPath, true);
                return true;
            }
            catch
            {
                return false;
            }
        }

        public async Task<bool> RestoreDatabaseAsync(string backupFilePath)
        {
            try
            {
                var targetFile = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData), 
                                            "EduTrackForWin", "edutrack.db");
                
                if (!File.Exists(backupFilePath))
                    return false;

                // Close current context
                await _context.DisposeAsync();
                
                File.Copy(backupFilePath, targetFile, true);
                return true;
            }
            catch
            {
                return false;
            }
        }

        public void Dispose()
        {
            _context?.Dispose();
        }
    }
}
