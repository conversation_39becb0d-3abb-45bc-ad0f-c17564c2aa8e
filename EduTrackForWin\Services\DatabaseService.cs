using Microsoft.EntityFrameworkCore;
using EduTrackForWin.Data;
using System.IO;
using EduTrackForWin.Models;

namespace EduTrackForWin.Services
{
    public class DatabaseService
    {
        private readonly EduTrackDbContext _context;

        public DatabaseService()
        {
            _context = new EduTrackDbContext();
        }

        public async Task InitializeDatabaseAsync()
        {
            try
            {
                await _context.Database.EnsureCreatedAsync();

                // Update database schema if needed
                await UpdateDatabaseSchemaAsync();

                // Check if settings exist, if not create default
                if (!await _context.Settings.AnyAsync())
                {
                    var defaultSettings = new Settings
                    {
                        CenterName = "مركز EduTrack التعليمي",
                        CenterAddress = "العنوان",
                        CenterPhone = "123456789",
                        CenterEmail = "<EMAIL>",
                        DefaultMonthlyFee = 100,
                        DarkMode = false,
                        AutoBackup = true,
                        BackupIntervalDays = 1,
                        MaxAbsenceWarning = 3,
                        ReportHeader = "تقرير مركز EduTrack التعليمي",
                        ReportFooter = "شكراً لثقتكم بنا"
                    };

                    _context.Settings.Add(defaultSettings);
                    await _context.SaveChangesAsync();
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في تهيئة قاعدة البيانات: {ex.Message}");
            }
        }

        private async Task UpdateDatabaseSchemaAsync()
        {
            try
            {
                var connection = _context.Database.GetDbConnection();
                await connection.OpenAsync();

                // Check if BillingDay column exists in Groups table
                var command = connection.CreateCommand();
                command.CommandText = "PRAGMA table_info(Groups)";

                var reader = await command.ExecuteReaderAsync();
                bool billingDayExists = false;

                while (await reader.ReadAsync())
                {
                    var columnName = reader.GetString(1); // Column name is at index 1
                    if (columnName == "BillingDay")
                    {
                        billingDayExists = true;
                        break;
                    }
                }

                await reader.CloseAsync();

                // Add BillingDay column if it doesn't exist
                if (!billingDayExists)
                {
                    command.CommandText = "ALTER TABLE Groups ADD COLUMN BillingDay INTEGER DEFAULT 1";
                    await command.ExecuteNonQueryAsync();
                }

                // Check and fix Sessions table column name issue
                await FixSessionsTableColumnAsync(connection);

                await connection.CloseAsync();
            }
            catch (Exception ex)
            {
                // Log error but don't throw to avoid breaking initialization
                System.Diagnostics.Debug.WriteLine($"Error updating database schema: {ex.Message}");
            }
        }

        private async Task FixSessionsTableColumnAsync(System.Data.Common.DbConnection connection)
        {
            try
            {
                // Check if Sessions table has the incorrect "Scheduleld" column
                var command = connection.CreateCommand();
                command.CommandText = "PRAGMA table_info(Sessions)";

                var reader = await command.ExecuteReaderAsync();
                bool hasScheduleldColumn = false;
                bool hasScheduleIdColumn = false;

                while (await reader.ReadAsync())
                {
                    var columnName = reader.GetString(1); // Column name is at index 1
                    if (columnName == "Scheduleld")
                    {
                        hasScheduleldColumn = true;
                    }
                    else if (columnName == "ScheduleId")
                    {
                        hasScheduleIdColumn = true;
                    }
                }

                await reader.CloseAsync();

                // If we have the incorrect column name, fix it
                if (hasScheduleldColumn && !hasScheduleIdColumn)
                {
                    // SQLite doesn't support renaming columns directly, so we need to recreate the table
                    await RecreateSessionsTableAsync(connection);
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error fixing Sessions table column: {ex.Message}");
            }
        }

        private async Task RecreateSessionsTableAsync(System.Data.Common.DbConnection connection)
        {
            try
            {
                var command = connection.CreateCommand();

                // Create a temporary table with the correct schema
                command.CommandText = @"
                    CREATE TABLE Sessions_temp (
                        Id INTEGER PRIMARY KEY AUTOINCREMENT,
                        Date TEXT NOT NULL,
                        StartTime TEXT NOT NULL,
                        EndTime TEXT NOT NULL,
                        Room TEXT,
                        Notes TEXT,
                        Topic TEXT,
                        IsCompleted INTEGER NOT NULL DEFAULT 0,
                        TeacherPresent INTEGER NOT NULL DEFAULT 1,
                        GroupId INTEGER NOT NULL,
                        TeacherId INTEGER NOT NULL,
                        ScheduleId INTEGER,
                        CreatedAt TEXT NOT NULL,
                        UpdatedAt TEXT,
                        IsRecurring INTEGER NOT NULL DEFAULT 0,
                        RecurrenceType TEXT,
                        RecurrenceEndDate TEXT,
                        FOREIGN KEY (GroupId) REFERENCES Groups (Id) ON DELETE CASCADE,
                        FOREIGN KEY (TeacherId) REFERENCES Teachers (Id) ON DELETE RESTRICT,
                        FOREIGN KEY (ScheduleId) REFERENCES Schedules (ScheduleId) ON DELETE SET NULL
                    )";
                await command.ExecuteNonQueryAsync();

                // Copy data from the old table to the new one, mapping Scheduleld to ScheduleId
                command.CommandText = @"
                    INSERT INTO Sessions_temp (
                        Id, Date, StartTime, EndTime, Room, Notes, Topic, IsCompleted,
                        TeacherPresent, GroupId, TeacherId, ScheduleId, CreatedAt, UpdatedAt,
                        IsRecurring, RecurrenceType, RecurrenceEndDate
                    )
                    SELECT
                        Id, Date, StartTime, EndTime, Room, Notes, Topic, IsCompleted,
                        TeacherPresent, GroupId, TeacherId, Scheduleld, CreatedAt, UpdatedAt,
                        IsRecurring, RecurrenceType, RecurrenceEndDate
                    FROM Sessions";
                await command.ExecuteNonQueryAsync();

                // Drop the old table
                command.CommandText = "DROP TABLE Sessions";
                await command.ExecuteNonQueryAsync();

                // Rename the temporary table
                command.CommandText = "ALTER TABLE Sessions_temp RENAME TO Sessions";
                await command.ExecuteNonQueryAsync();

                // Recreate indexes
                command.CommandText = "CREATE INDEX IX_Sessions_Date ON Sessions (Date)";
                await command.ExecuteNonQueryAsync();

                System.Diagnostics.Debug.WriteLine("Successfully fixed Sessions table column name from 'Scheduleld' to 'ScheduleId'");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error recreating Sessions table: {ex.Message}");
                throw;
            }
        }

        public async Task<bool> BackupDatabaseAsync(string backupPath)
        {
            try
            {
                var sourceFile = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData), 
                                            "EduTrackForWin", "edutrack.db");
                
                if (!File.Exists(sourceFile))
                    return false;

                var backupFileName = $"edutrack_backup_{DateTime.Now:yyyyMMdd_HHmmss}.db";
                var fullBackupPath = Path.Combine(backupPath, backupFileName);
                
                // Ensure backup directory exists
                Directory.CreateDirectory(backupPath);
                
                File.Copy(sourceFile, fullBackupPath, true);
                return true;
            }
            catch
            {
                return false;
            }
        }

        public async Task<bool> RestoreDatabaseAsync(string backupFilePath)
        {
            try
            {
                var targetFile = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData), 
                                            "EduTrackForWin", "edutrack.db");
                
                if (!File.Exists(backupFilePath))
                    return false;

                // Close current context
                await _context.DisposeAsync();
                
                File.Copy(backupFilePath, targetFile, true);
                return true;
            }
            catch
            {
                return false;
            }
        }

        public void Dispose()
        {
            _context?.Dispose();
        }
    }
}
