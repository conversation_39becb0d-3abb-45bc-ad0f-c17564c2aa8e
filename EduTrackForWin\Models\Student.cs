using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace EduTrackForWin.Models
{
    public class Student
    {
        [Key]
        public int Id { get; set; }

        [Required]
        [StringLength(100)]
        public string Name { get; set; } = string.Empty;

        [StringLength(50)]
        public string Grade { get; set; } = string.Empty;

        [StringLength(20)]
        public string Phone { get; set; } = string.Empty;

        [StringLength(200)]
        public string Address { get; set; } = string.Empty;

        [StringLength(500)]
        public string Notes { get; set; } = string.Empty;

        // Foreign Key
        public int? GroupId { get; set; }

        // Navigation Properties
        [ForeignKey("GroupId")]
        public virtual Group? Group { get; set; }

        public virtual ICollection<Attendance> Attendances { get; set; } = new List<Attendance>();
        public virtual ICollection<Payment> Payments { get; set; } = new List<Payment>();

        // Computed Properties
        [NotMapped]
        public decimal TotalPaid => Payments?.Sum(p => p.Amount) ?? 0;

        [NotMapped]
        public decimal TotalDue => (Group?.MonthlyFee ?? 0) - TotalPaid;

        [NotMapped]
        public string PaymentStatus
        {
            get
            {
                if (TotalDue <= 0) return "مدفوع";

                // Check if payment is overdue based on billing day
                if (Group?.BillingDay.HasValue == true)
                {
                    try
                    {
                        var today = DateTime.Today;
                        var billingDay = Group.BillingDay.Value;
                        var currentMonthDue = new DateTime(today.Year, today.Month, billingDay);

                        if (today > currentMonthDue)
                        {
                            var overdueDays = (today - currentMonthDue).Days;
                            if (overdueDays > 15) return "متأخر";
                            if (overdueDays > 0) return "جزئي";
                        }
                    }
                    catch
                    {
                        // If there's an error with billing day calculation, fall back to simple check
                    }
                }

                return TotalDue > 0 ? "مستحق" : "مدفوع";
            }
        }

        public DateTime CreatedAt { get; set; } = DateTime.Now;
        public DateTime? UpdatedAt { get; set; }
    }
}
