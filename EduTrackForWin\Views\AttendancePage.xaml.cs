using System.Windows;
using System.Windows.Controls;

namespace EduTrackForWin.Views
{
    public partial class AttendancePage : Page
    {
        public AttendancePage()
        {
            InitializeComponent();
            LoadAttendanceData();
        }

        private void LoadAttendanceData()
        {
            // Load sample data for demonstration
            // In a real implementation, this would load from the database
        }

        private void BtnTakeAttendance_Click(object sender, RoutedEventArgs e)
        {
            MessageBox.Show("سيتم فتح نافذة تسجيل الحضور", "تسجيل الحضور",
                          MessageBoxButton.OK, MessageBoxImage.Information);
        }

        private void BtnFilter_Click(object sender, RoutedEventArgs e)
        {
            MessageBox.Show("سيتم تطبيق التصفية حسب التاريخ المحدد", "تصفية",
                          MessageBoxButton.OK, MessageBoxImage.Information);
        }

        private void BtnTodaySessions_Click(object sender, RoutedEventArgs e)
        {
            MessageBox.Show("عرض حصص اليوم", "حصص اليوم",
                          MessageBoxButton.OK, MessageBoxImage.Information);
        }

        private void BtnAbsentStudents_Click(object sender, RoutedEventArgs e)
        {
            MessageBox.Show("عرض الطلاب الغائبون اليوم", "الطلاب الغائبون",
                          MessageBoxButton.OK, MessageBoxImage.Information);
        }

        private void BtnAttendanceReport_Click(object sender, RoutedEventArgs e)
        {
            MessageBox.Show("إنشاء تقرير الحضور", "تقرير الحضور",
                          MessageBoxButton.OK, MessageBoxImage.Information);
        }

        private void BtnLateStudents_Click(object sender, RoutedEventArgs e)
        {
            MessageBox.Show("عرض الطلاب المتأخرون في الحضور", "الطلاب المتأخرون",
                          MessageBoxButton.OK, MessageBoxImage.Information);
        }
    }
}
