<Window x:Class="EduTrackForWin.Views.AddEditSessionWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:EduTrackForWin.Views"
        mc:Ignorable="d"
        Title="إضافة/تعديل حصة" Height="550" Width="500"
        WindowStartupLocation="CenterOwner"
        ResizeMode="NoResize">
    <!-- Minor change to force re-generation of code-behind -->

    <Window.Resources>
        <!-- Input Field Style -->
        <Style x:Key="InputFieldStyle" TargetType="TextBox">
            <Setter Property="Padding" Value="10"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="BorderBrush" Value="#E0E0E0"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="Margin" Value="0,5,0,15"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="TextBox">
                        <Border Background="{TemplateBinding Background}" 
                              BorderBrush="{TemplateBinding BorderBrush}" 
                              BorderThickness="{TemplateBinding BorderThickness}"
                              CornerRadius="5">
                            <ScrollViewer x:Name="PART_ContentHost" 
                                        VerticalAlignment="Center" 
                                        Margin="{TemplateBinding Padding}"/>
                        </Border>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <!-- ComboBox Style -->
        <Style x:Key="ComboBoxStyle" TargetType="ComboBox">
            <Setter Property="Padding" Value="10"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="BorderBrush" Value="#E0E0E0"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="Margin" Value="0,5,0,15"/>
        </Style>

        <!-- DatePicker Style -->
        <Style x:Key="DatePickerStyle" TargetType="DatePicker">
            <Setter Property="Padding" Value="10"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="BorderBrush" Value="#E0E0E0"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="Margin" Value="0,5,0,15"/>
            <Setter Property="Height" Value="40"/>
        </Style>

        <!-- Label Style -->
        <Style x:Key="LabelStyle" TargetType="TextBlock">
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="FontWeight" Value="SemiBold"/>
            <Setter Property="Foreground" Value="#212121"/>
            <Setter Property="Margin" Value="0,10,0,5"/>
        </Style>

        <!-- Button Styles -->
        <Style x:Key="PrimaryButtonStyle" TargetType="Button">
            <Setter Property="Background" Value="#9C27B0"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Padding" Value="20,10"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}" 
                              CornerRadius="5">
                            <ContentPresenter HorizontalAlignment="Center" 
                                            VerticalAlignment="Center"
                                            Margin="{TemplateBinding Padding}"/>
                        </Border>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <Style x:Key="SecondaryButtonStyle" TargetType="Button">
            <Setter Property="Background" Value="#757575"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Padding" Value="20,10"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}" 
                              CornerRadius="5">
                            <ContentPresenter HorizontalAlignment="Center" 
                                            VerticalAlignment="Center"
                                            Margin="{TemplateBinding Padding}"/>
                        </Border>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>
    </Window.Resources>

    <Grid Margin="30">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <StackPanel Grid.Row="0" Margin="0,0,0,20">
            <TextBlock Name="TxtTitle" Text="إضافة حصة جديدة" FontSize="24" FontWeight="Bold" 
                     Foreground="#212121" HorizontalAlignment="Center"/>
            <TextBlock Text="املأ البيانات التالية لإضافة حصة جديدة" FontSize="14" 
                     Foreground="#757575" HorizontalAlignment="Center" Margin="0,5,0,0"/>
        </StackPanel>

        <!-- Form -->
        <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto">
            <StackPanel>
                <!-- Date -->
                <TextBlock Text="تاريخ الحصة *" Style="{StaticResource LabelStyle}"/>
                <DatePicker Name="DpDate" Style="{StaticResource DatePickerStyle}"/>

                <!-- Time -->
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="20"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>

                    <StackPanel Grid.Column="0">
                        <TextBlock Text="وقت البداية *" Style="{StaticResource LabelStyle}"/>
                        <TextBox Name="TxtStartTime" Style="{StaticResource InputFieldStyle}" 
                               Text="09:00" MaxLength="5"/>
                    </StackPanel>

                    <TextBlock Grid.Column="1" Text="إلى" VerticalAlignment="Center" 
                             HorizontalAlignment="Center" Margin="0,30,0,0"/>

                    <StackPanel Grid.Column="2">
                        <TextBlock Text="وقت النهاية *" Style="{StaticResource LabelStyle}"/>
                        <TextBox Name="TxtEndTime" Style="{StaticResource InputFieldStyle}" 
                               Text="10:00" MaxLength="5"/>
                    </StackPanel>
                </Grid>

                <!-- Group -->
                <TextBlock Text="المجموعة *" Style="{StaticResource LabelStyle}"/>
                <ComboBox Name="CmbGroup" Style="{StaticResource ComboBoxStyle}" 
                        DisplayMemberPath="Name" SelectedValuePath="Id"
                        SelectionChanged="CmbGroup_SelectionChanged"/>

                <!-- Teacher -->
                <TextBlock Text="المعلم" Style="{StaticResource LabelStyle}"/>
                <ComboBox Name="CmbTeacher" Style="{StaticResource ComboBoxStyle}" 
                        DisplayMemberPath="Name" SelectedValuePath="Id"/>

                <!-- Room -->
                <TextBlock Text="القاعة" Style="{StaticResource LabelStyle}"/>
                <TextBox Name="TxtRoom" Style="{StaticResource InputFieldStyle}" 
                       MaxLength="100"/>

                <!-- Topic -->
                <TextBlock Text="موضوع الحصة" Style="{StaticResource LabelStyle}"/>
                <TextBox Name="TxtTopic" Style="{StaticResource InputFieldStyle}" 
                       MaxLength="200"/>

                <!-- Notes -->
                <TextBlock Text="ملاحظات" Style="{StaticResource LabelStyle}"/>
                <TextBox Name="TxtNotes" Style="{StaticResource InputFieldStyle}" 
                       MaxLength="500" Height="80" TextWrapping="Wrap" 
                       VerticalScrollBarVisibility="Auto"/>

                <!-- Recurrence -->
                <CheckBox Name="ChkIsRecurring" Content="حصة متكررة" Margin="0,10,0,5"
                          Checked="ChkIsRecurring_CheckedChanged" Unchecked="ChkIsRecurring_CheckedChanged"/>
                <StackPanel Name="RecurrencePanel" Visibility="Collapsed">
                    <TextBlock Text="تاريخ انتهاء التكرار" Style="{StaticResource LabelStyle}"/>
                    <DatePicker Name="DpRecurrenceEndDate" Style="{StaticResource DatePickerStyle}"/>
                </StackPanel>
            </StackPanel>
        </ScrollViewer>

        <!-- Buttons -->
        <StackPanel Grid.Row="2" Orientation="Horizontal" HorizontalAlignment="Center" 
                  Margin="0,20,0,0">
            <Button Name="BtnSave" Content="حفظ" Style="{StaticResource PrimaryButtonStyle}" 
                  Margin="0,0,10,0" Click="BtnSave_Click"/>
            <Button Name="BtnCancel" Content="إلغاء" Style="{StaticResource SecondaryButtonStyle}" 
                  Margin="10,0,0,0" Click="BtnCancel_Click"/>
        </StackPanel>
    </Grid>
</Window>
