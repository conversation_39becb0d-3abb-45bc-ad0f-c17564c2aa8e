<Window x:Class="EduTrackForWin.Views.SessionDetailsWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:EduTrackForWin.Views"
        mc:Ignorable="d"
        Title="تفاصيل الحصة" Height="600" Width="700"
        WindowStartupLocation="CenterOwner">

    <Window.Resources>
        <!-- Card Style -->
        <Style x:Key="CardStyle" TargetType="Border">
            <Setter Property="Background" Value="White"/>
            <Setter Property="CornerRadius" Value="8"/>
            <Setter Property="Padding" Value="20"/>
            <Setter Property="Margin" Value="10"/>
            <Setter Property="Effect">
                <Setter.Value>
                    <DropShadowEffect Color="Gray" Direction="270" ShadowDepth="2" Opacity="0.3" BlurRadius="5"/>
                </Setter.Value>
            </Setter>
        </Style>

        <!-- Info Label Style -->
        <Style x:Key="InfoLabelStyle" TargetType="TextBlock">
            <Setter Property="FontWeight" Value="SemiBold"/>
            <Setter Property="Foreground" Value="#424242"/>
            <Setter Property="Margin" Value="0,5,10,5"/>
        </Style>

        <!-- Info Value Style -->
        <Style x:Key="InfoValueStyle" TargetType="TextBlock">
            <Setter Property="Foreground" Value="#212121"/>
            <Setter Property="Margin" Value="0,5"/>
        </Style>

        <!-- Section Header Style -->
        <Style x:Key="SectionHeaderStyle" TargetType="TextBlock">
            <Setter Property="FontSize" Value="18"/>
            <Setter Property="FontWeight" Value="Bold"/>
            <Setter Property="Foreground" Value="#9C27B0"/>
            <Setter Property="Margin" Value="0,0,0,15"/>
        </Style>
    </Window.Resources>

    <Grid Background="#F5F5F5">
        <ScrollViewer VerticalScrollBarVisibility="Auto" Margin="20">
            <StackPanel>
                <!-- Header -->
                <Border Style="{StaticResource CardStyle}" Background="#F3E5F5">
                    <StackPanel>
                        <TextBlock Name="TxtSessionTitle" Text="تفاصيل الحصة" FontSize="24" FontWeight="Bold" 
                                 HorizontalAlignment="Center" Foreground="#7B1FA2"/>
                        <TextBlock Name="TxtSessionDate" Text="التاريخ والوقت" FontSize="16" 
                                 HorizontalAlignment="Center" Foreground="#424242" Margin="0,5,0,0"/>
                    </StackPanel>
                </Border>

                <!-- Basic Information -->
                <Border Style="{StaticResource CardStyle}">
                    <StackPanel>
                        <TextBlock Text="معلومات الحصة" Style="{StaticResource SectionHeaderStyle}"/>
                        
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            
                            <StackPanel Grid.Column="0">
                                <StackPanel Orientation="Horizontal">
                                    <TextBlock Text="التاريخ:" Style="{StaticResource InfoLabelStyle}"/>
                                    <TextBlock Name="TxtDate" Style="{StaticResource InfoValueStyle}"/>
                                </StackPanel>
                                
                                <StackPanel Orientation="Horizontal">
                                    <TextBlock Text="وقت البداية:" Style="{StaticResource InfoLabelStyle}"/>
                                    <TextBlock Name="TxtStartTime" Style="{StaticResource InfoValueStyle}"/>
                                </StackPanel>
                                
                                <StackPanel Orientation="Horizontal">
                                    <TextBlock Text="وقت النهاية:" Style="{StaticResource InfoLabelStyle}"/>
                                    <TextBlock Name="TxtEndTime" Style="{StaticResource InfoValueStyle}"/>
                                </StackPanel>
                                
                                <StackPanel Orientation="Horizontal">
                                    <TextBlock Text="المدة:" Style="{StaticResource InfoLabelStyle}"/>
                                    <TextBlock Name="TxtDuration" Style="{StaticResource InfoValueStyle}"/>
                                </StackPanel>
                            </StackPanel>
                            
                            <StackPanel Grid.Column="1">
                                <StackPanel Orientation="Horizontal">
                                    <TextBlock Text="المجموعة:" Style="{StaticResource InfoLabelStyle}"/>
                                    <TextBlock Name="TxtGroup" Style="{StaticResource InfoValueStyle}"/>
                                </StackPanel>
                                
                                <StackPanel Orientation="Horizontal">
                                    <TextBlock Text="المعلم:" Style="{StaticResource InfoLabelStyle}"/>
                                    <TextBlock Name="TxtTeacher" Style="{StaticResource InfoValueStyle}"/>
                                </StackPanel>
                                
                                <StackPanel Orientation="Horizontal">
                                    <TextBlock Text="القاعة:" Style="{StaticResource InfoLabelStyle}"/>
                                    <TextBlock Name="TxtRoom" Style="{StaticResource InfoValueStyle}"/>
                                </StackPanel>
                                
                                <StackPanel Orientation="Horizontal">
                                    <TextBlock Text="الحالة:" Style="{StaticResource InfoLabelStyle}"/>
                                    <TextBlock Name="TxtStatus" Style="{StaticResource InfoValueStyle}"/>
                                </StackPanel>
                            </StackPanel>
                        </Grid>
                        
                        <StackPanel Margin="0,10,0,0">
                            <TextBlock Text="موضوع الحصة:" Style="{StaticResource InfoLabelStyle}"/>
                            <TextBlock Name="TxtTopic" Style="{StaticResource InfoValueStyle}" TextWrapping="Wrap"/>
                        </StackPanel>
                        
                        <StackPanel Margin="0,10,0,0">
                            <TextBlock Text="ملاحظات:" Style="{StaticResource InfoLabelStyle}"/>
                            <TextBlock Name="TxtNotes" Style="{StaticResource InfoValueStyle}" TextWrapping="Wrap"/>
                        </StackPanel>
                    </StackPanel>
                </Border>

                <!-- Attendance Information -->
                <Border Style="{StaticResource CardStyle}">
                    <StackPanel>
                        <TextBlock Text="معلومات الحضور" Style="{StaticResource SectionHeaderStyle}"/>
                        
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            
                            <StackPanel Grid.Column="0">
                                <TextBlock Text="إجمالي الطلاب" Style="{StaticResource InfoLabelStyle}"/>
                                <TextBlock Name="TxtTotalStudents" Style="{StaticResource InfoValueStyle}" 
                                         FontSize="18" FontWeight="Bold" Foreground="#2196F3"/>
                            </StackPanel>
                            
                            <StackPanel Grid.Column="1">
                                <TextBlock Text="الحاضرون" Style="{StaticResource InfoLabelStyle}"/>
                                <TextBlock Name="TxtPresentStudents" Style="{StaticResource InfoValueStyle}" 
                                         FontSize="18" FontWeight="Bold" Foreground="#4CAF50"/>
                            </StackPanel>
                            
                            <StackPanel Grid.Column="2">
                                <TextBlock Text="نسبة الحضور" Style="{StaticResource InfoLabelStyle}"/>
                                <TextBlock Name="TxtAttendanceRate" Style="{StaticResource InfoValueStyle}" 
                                         FontSize="18" FontWeight="Bold" Foreground="#FF9800"/>
                            </StackPanel>
                        </Grid>
                    </StackPanel>
                </Border>

                <!-- Attendance List -->
                <Border Style="{StaticResource CardStyle}">
                    <StackPanel>
                        <TextBlock Text="قائمة الحضور" Style="{StaticResource SectionHeaderStyle}"/>
                        
                        <DataGrid Name="DgAttendance" 
                                AutoGenerateColumns="False" 
                                CanUserAddRows="False" 
                                CanUserDeleteRows="False"
                                IsReadOnly="True"
                                GridLinesVisibility="Horizontal"
                                HeadersVisibility="Column"
                                BorderThickness="0"
                                Background="Transparent"
                                RowBackground="White"
                                AlternatingRowBackground="#F5F5F5"
                                Height="200">
                            
                            <DataGrid.Columns>
                                <DataGridTextColumn Header="اسم الطالب" Binding="{Binding StudentName}" Width="200"/>
                                <DataGridTemplateColumn Header="الحالة" Width="100">
                                    <DataGridTemplateColumn.CellTemplate>
                                        <DataTemplate>
                                            <Border CornerRadius="10" Padding="8,4">
                                                <Border.Style>
                                                    <Style TargetType="Border">
                                                        <Style.Triggers>
                                                            <DataTrigger Binding="{Binding IsPresent}" Value="True">
                                                                <Setter Property="Background" Value="#4CAF50"/>
                                                            </DataTrigger>
                                                            <DataTrigger Binding="{Binding IsPresent}" Value="False">
                                                                <Setter Property="Background" Value="#F44336"/>
                                                            </DataTrigger>
                                                        </Style.Triggers>
                                                    </Style>
                                                </Border.Style>
                                                <TextBlock Text="{Binding Status}" 
                                                         Foreground="White" FontSize="12" HorizontalAlignment="Center"/>
                                            </Border>
                                        </DataTemplate>
                                    </DataGridTemplateColumn.CellTemplate>
                                </DataGridTemplateColumn>
                                <DataGridTextColumn Header="ملاحظات" Binding="{Binding Notes}" Width="*"/>
                            </DataGrid.Columns>
                        </DataGrid>
                    </StackPanel>
                </Border>

                <!-- Action Buttons -->
                <Border Style="{StaticResource CardStyle}">
                    <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                        <Button Name="BtnEdit" Content="تعديل الحصة"
                              Background="#9C27B0" Foreground="White"
                              Padding="20,10" Margin="10" BorderThickness="0"
                              Click="BtnEdit_Click">
                            <Button.Template>
                                <ControlTemplate TargetType="Button">
                                    <Border Background="{TemplateBinding Background}"
                                          CornerRadius="5">
                                        <ContentPresenter HorizontalAlignment="Center"
                                                        VerticalAlignment="Center"
                                                        Margin="{TemplateBinding Padding}"/>
                                    </Border>
                                </ControlTemplate>
                            </Button.Template>
                        </Button>

                        <Button Name="BtnTakeAttendance" Content="تسجيل الحضور"
                              Background="#4CAF50" Foreground="White"
                              Padding="20,10" Margin="10" BorderThickness="0"
                              Click="BtnTakeAttendance_Click">
                            <Button.Template>
                                <ControlTemplate TargetType="Button">
                                    <Border Background="{TemplateBinding Background}"
                                          CornerRadius="5">
                                        <ContentPresenter HorizontalAlignment="Center"
                                                        VerticalAlignment="Center"
                                                        Margin="{TemplateBinding Padding}"/>
                                    </Border>
                                </ControlTemplate>
                            </Button.Template>
                        </Button>

                        <Button Name="BtnMarkComplete" Content="تمييز كمكتملة"
                              Background="#FF9800" Foreground="White"
                              Padding="20,10" Margin="10" BorderThickness="0"
                              Click="BtnMarkComplete_Click">
                            <Button.Template>
                                <ControlTemplate TargetType="Button">
                                    <Border Background="{TemplateBinding Background}"
                                          CornerRadius="5">
                                        <ContentPresenter HorizontalAlignment="Center"
                                                        VerticalAlignment="Center"
                                                        Margin="{TemplateBinding Padding}"/>
                                    </Border>
                                </ControlTemplate>
                            </Button.Template>
                        </Button>

                        <Button Name="BtnClose" Content="إغلاق"
                              Background="#757575" Foreground="White"
                              Padding="20,10" Margin="10" BorderThickness="0"
                              Click="BtnClose_Click">
                            <Button.Template>
                                <ControlTemplate TargetType="Button">
                                    <Border Background="{TemplateBinding Background}"
                                          CornerRadius="5">
                                        <ContentPresenter HorizontalAlignment="Center"
                                                        VerticalAlignment="Center"
                                                        Margin="{TemplateBinding Padding}"/>
                                    </Border>
                                </ControlTemplate>
                            </Button.Template>
                        </Button>
                    </StackPanel>
                </Border>
            </StackPanel>
        </ScrollViewer>
    </Grid>
</Window>
