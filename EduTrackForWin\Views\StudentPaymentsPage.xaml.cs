using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Media;
using EduTrackForWin.Data;
using EduTrackForWin.Models;
using EduTrackForWin.Services;
using Microsoft.EntityFrameworkCore;

namespace EduTrackForWin.Views
{
    /// <summary>
    /// Interaction logic for StudentPaymentsPage.xaml
    /// </summary>
    public partial class StudentPaymentsPage : Page
    {
        private readonly EduTrackDbContext _context;
        private readonly StudentService _studentService;
        private readonly PaymentService _paymentService;
        private readonly GroupService _groupService;
        private List<Student> _allStudents;
        private List<Group> _groups;

        public StudentPaymentsPage()
        {
            try
            {
                InitializeComponent();
                _context = new EduTrackDbContext();
                _studentService = new StudentService();
                _paymentService = new PaymentService();
                _groupService = new GroupService();
                _allStudents = new List<Student>();
                _groups = new List<Group>();

                Loaded += StudentPaymentsPage_Loaded;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تهيئة صفحة دفع الطلاب: {ex.Message}", "خطأ", 
                              MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async void StudentPaymentsPage_Loaded(object sender, RoutedEventArgs e)
        {
            await LoadData();
        }

        private async Task LoadData()
        {
            try
            {
                await LoadGroups();
                await LoadStudents();
                LoadMonthFilter();
                RefreshStudentsList();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل البيانات: {ex.Message}", "خطأ", 
                              MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async Task LoadGroups()
        {
            try
            {
                _groups = await _groupService.GetAllGroupsAsync();
                
                if (CmbGroupFilter != null)
                {
                    CmbGroupFilter.Items.Clear();
                    CmbGroupFilter.Items.Add(new ComboBoxItem { Content = "جميع المجموعات", Tag = 0 });
                    
                    foreach (var group in _groups)
                    {
                        CmbGroupFilter.Items.Add(new ComboBoxItem { Content = group.Name, Tag = group.Id });
                    }
                    
                    CmbGroupFilter.SelectedIndex = 0;
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل المجموعات: {ex.Message}", "خطأ", 
                              MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async Task LoadStudents()
        {
            try
            {
                _allStudents = await _context.Students
                    .Include(s => s.Group)
                    .Include(s => s.Payments)
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل الطلاب: {ex.Message}", "خطأ", 
                              MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void LoadMonthFilter()
        {
            try
            {
                if (CmbMonthFilter != null)
                {
                    CmbMonthFilter.Items.Clear();
                    
                    var currentDate = DateTime.Now;
                    for (int i = 0; i < 12; i++)
                    {
                        var month = currentDate.AddMonths(-i);
                        var monthName = month.ToString("MMMM yyyy");
                        var item = new ComboBoxItem 
                        { 
                            Content = monthName, 
                            Tag = month,
                            IsSelected = i == 0
                        };
                        CmbMonthFilter.Items.Add(item);
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل فلتر الشهور: {ex.Message}", "خطأ", 
                              MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void RefreshStudentsList()
        {
            try
            {
                var filteredStudents = _allStudents.AsEnumerable();

                // Filter by group
                if (CmbGroupFilter?.SelectedItem is ComboBoxItem groupItem && 
                    groupItem.Tag is int groupId && groupId > 0)
                {
                    filteredStudents = filteredStudents.Where(s => s.GroupId == groupId);
                }

                // Filter by payment status
                if (CmbPaymentStatusFilter?.SelectedItem is ComboBoxItem statusItem && 
                    statusItem.Content.ToString() != "جميع الحالات")
                {
                    var status = statusItem.Content.ToString();
                    filteredStudents = filteredStudents.Where(s => s.PaymentStatus == status);
                }

                // Apply search filter
                if (TxtSearch != null && !string.IsNullOrWhiteSpace(TxtSearch.Text) && 
                    TxtSearch.Text != "البحث عن طالب...")
                {
                    var searchTerm = TxtSearch.Text.ToLower();
                    filteredStudents = filteredStudents.Where(s => 
                        s.Name?.ToLower().Contains(searchTerm) == true ||
                        s.Phone?.ToLower().Contains(searchTerm) == true ||
                        s.Group?.Name?.ToLower().Contains(searchTerm) == true);
                }

                // Create student cards
                CreateStudentCards(filteredStudents.ToList());
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تصفية الطلاب: {ex.Message}", "خطأ", 
                              MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void CreateStudentCards(List<Student> students)
        {
            try
            {
                if (StudentsItemsControl == null) return;

                StudentsItemsControl.Items.Clear();

                foreach (var student in students)
                {
                    var card = CreateStudentCard(student);
                    StudentsItemsControl.Items.Add(card);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في إنشاء بطاقات الطلاب: {ex.Message}", "خطأ", 
                              MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private Border CreateStudentCard(Student student)
        {
            var card = new Border
            {
                Style = (Style)FindResource("StudentCardStyle"),
                Tag = student
            };

            var grid = new Grid();
            grid.RowDefinitions.Add(new RowDefinition { Height = GridLength.Auto });
            grid.RowDefinitions.Add(new RowDefinition { Height = GridLength.Auto });
            grid.RowDefinitions.Add(new RowDefinition { Height = GridLength.Auto });
            grid.RowDefinitions.Add(new RowDefinition { Height = GridLength.Auto });

            // Student Name
            var nameText = new TextBlock
            {
                Text = student.Name,
                FontSize = 16,
                FontWeight = FontWeights.Bold,
                Foreground = new SolidColorBrush(Color.FromRgb(0x21, 0x21, 0x21)),
                Margin = new Thickness(0, 0, 0, 5)
            };
            Grid.SetRow(nameText, 0);
            grid.Children.Add(nameText);

            // Group and Phone
            var infoText = new TextBlock
            {
                Text = $"المجموعة: {student.Group?.Name ?? "غير محدد"} | الهاتف: {student.Phone}",
                FontSize = 12,
                Foreground = new SolidColorBrush(Color.FromRgb(0x75, 0x75, 0x75)),
                Margin = new Thickness(0, 0, 0, 10)
            };
            Grid.SetRow(infoText, 1);
            grid.Children.Add(infoText);

            // Payment Status
            var statusPanel = new StackPanel { Orientation = Orientation.Horizontal, Margin = new Thickness(0, 0, 0, 10) };
            
            var statusText = new TextBlock
            {
                Text = "الحالة: ",
                FontSize = 12,
                FontWeight = FontWeights.SemiBold
            };
            statusPanel.Children.Add(statusText);

            var statusValue = new TextBlock
            {
                Text = student.PaymentStatus,
                FontSize = 12,
                FontWeight = FontWeights.Bold,
                Foreground = GetStatusColor(student.PaymentStatus)
            };
            statusPanel.Children.Add(statusValue);

            var dueText = new TextBlock
            {
                Text = $" | المستحق: {student.TotalDue:C}",
                FontSize = 12,
                Foreground = student.TotalDue > 0 ? Brushes.Red : Brushes.Green
            };
            statusPanel.Children.Add(dueText);

            Grid.SetRow(statusPanel, 2);
            grid.Children.Add(statusPanel);

            // Action Buttons
            var buttonPanel = new StackPanel { Orientation = Orientation.Horizontal };
            
            var payButton = new Button
            {
                Content = "تسجيل دفعة",
                Style = (Style)FindResource("PrimaryButtonStyle"),
                Tag = student
            };
            payButton.Click += BtnPayStudent_Click;
            buttonPanel.Children.Add(payButton);

            var historyButton = new Button
            {
                Content = "سجل المدفوعات",
                Style = (Style)FindResource("SecondaryButtonStyle"),
                Tag = student
            };
            historyButton.Click += BtnViewHistory_Click;
            buttonPanel.Children.Add(historyButton);

            if (student.TotalDue > 0)
            {
                var reminderButton = new Button
                {
                    Content = "تذكير",
                    Style = (Style)FindResource("WarningButtonStyle"),
                    Tag = student
                };
                reminderButton.Click += BtnRemindStudent_Click;
                buttonPanel.Children.Add(reminderButton);
            }

            Grid.SetRow(buttonPanel, 3);
            grid.Children.Add(buttonPanel);

            card.Child = grid;
            return card;
        }

        private Brush GetStatusColor(string status)
        {
            return status switch
            {
                "مدفوع" => Brushes.Green,
                "متأخر" => Brushes.Red,
                "جزئي" => Brushes.Orange,
                _ => Brushes.Gray
            };
        }

        // Event Handlers
        private async void BtnPayStudent_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (sender is Button button && button.Tag is Student student)
                {
                    var paymentWindow = new AddEditPaymentWindow();

                    // Pre-select the student
                    paymentWindow.Loaded += (s, args) =>
                    {
                        if (paymentWindow.CmbStudent != null)
                        {
                            paymentWindow.CmbStudent.SelectedValue = student.Id;
                        }
                    };

                    if (paymentWindow.ShowDialog() == true)
                    {
                        await LoadStudents();
                        RefreshStudentsList();
                        MessageBox.Show("تم تسجيل الدفعة بنجاح", "نجح",
                                      MessageBoxButton.OK, MessageBoxImage.Information);
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تسجيل الدفعة: {ex.Message}", "خطأ",
                              MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async void BtnViewHistory_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (sender is Button button && button.Tag is Student student)
                {
                    var historyWindow = new StudentPaymentHistoryWindow(student);
                    historyWindow.ShowDialog();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في عرض سجل المدفوعات: {ex.Message}", "خطأ",
                              MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void BtnRemindStudent_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (sender is Button button && button.Tag is Student student)
                {
                    var message = $"تذكير للطالب: {student.Name}\n" +
                                $"المجموعة: {student.Group?.Name}\n" +
                                $"المبلغ المستحق: {student.TotalDue:C}\n" +
                                $"الهاتف: {student.Phone}";

                    MessageBox.Show(message, "تذكير بالدفع",
                                  MessageBoxButton.OK, MessageBoxImage.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في إرسال التذكير: {ex.Message}", "خطأ",
                              MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async void BtnQuickPayment_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var quickPaymentWindow = new QuickPaymentWindow();
                if (quickPaymentWindow.ShowDialog() == true)
                {
                    await LoadStudents();
                    RefreshStudentsList();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في الدفعة السريعة: {ex.Message}", "خطأ",
                              MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async void BtnBulkPayment_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var bulkPaymentWindow = new BulkPaymentWindow();
                if (bulkPaymentWindow.ShowDialog() == true)
                {
                    await LoadStudents();
                    RefreshStudentsList();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في الدفع الجماعي: {ex.Message}", "خطأ",
                              MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void BtnPaymentReminder_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var overdueStudents = _allStudents.Where(s => s.TotalDue > 0).ToList();

                if (!overdueStudents.Any())
                {
                    MessageBox.Show("لا يوجد طلاب متأخرين في الدفع", "تذكير",
                                  MessageBoxButton.OK, MessageBoxImage.Information);
                    return;
                }

                var message = $"الطلاب المتأخرين في الدفع ({overdueStudents.Count}):\n\n";
                foreach (var student in overdueStudents.Take(10))
                {
                    message += $"• {student.Name} - {student.TotalDue:C}\n";
                }

                if (overdueStudents.Count > 10)
                {
                    message += $"... و {overdueStudents.Count - 10} طلاب آخرين";
                }

                MessageBox.Show(message, "تذكير بالدفع",
                              MessageBoxButton.OK, MessageBoxImage.Warning);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تذكير الدفع: {ex.Message}", "خطأ",
                              MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void Filter_Changed(object sender, SelectionChangedEventArgs e)
        {
            if (_allStudents != null)
            {
                RefreshStudentsList();
            }
        }

        private void TxtSearch_TextChanged(object sender, TextChangedEventArgs e)
        {
            if (_allStudents != null)
            {
                RefreshStudentsList();
            }
        }

        private void TxtSearch_GotFocus(object sender, RoutedEventArgs e)
        {
            if (TxtSearch.Text == "البحث عن طالب...")
            {
                TxtSearch.Text = "";
                TxtSearch.Foreground = Brushes.Black;
            }
        }

        private void TxtSearch_LostFocus(object sender, RoutedEventArgs e)
        {
            if (string.IsNullOrWhiteSpace(TxtSearch.Text))
            {
                TxtSearch.Text = "البحث عن طالب...";
                TxtSearch.Foreground = Brushes.Gray;
            }
        }

        private async void BtnRefresh_Click(object sender, RoutedEventArgs e)
        {
            await LoadData();
        }
    }
}
