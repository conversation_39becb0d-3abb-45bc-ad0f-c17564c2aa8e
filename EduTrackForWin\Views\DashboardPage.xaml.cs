using System.Windows;
using System.Windows.Controls;
using EduTrackForWin.Data;
using Microsoft.EntityFrameworkCore;
using EduTrackForWin.Services;
using EduTrackForWin.Models;

namespace EduTrackForWin.Views
{
    public partial class DashboardPage : Page
    {
        private readonly StudentService _studentService;
        private readonly DatabaseService _databaseService;

        public DashboardPage()
        {
            InitializeComponent();
            _studentService = new StudentService();
            _databaseService = new DatabaseService();
            LoadDashboardData();
        }

        private async void LoadDashboardData()
        {
            try
            {
                // Load statistics
                await LoadStatistics();
                
                // Load recent activities
                LoadRecentActivities();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل بيانات لوحة المعلومات: {ex.Message}", "خطأ", 
                              MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async Task LoadStatistics()
        {
            try
            {
                using var context = new EduTrackDbContext();

                // Load real statistics from database
                var totalStudents = await context.Students.CountAsync();
                var totalTeachers = await context.Teachers.CountAsync();
                var totalGroups = await context.Groups.CountAsync();

                // Calculate today's sessions
                var today = DateTime.Today;
                var todaySessions = await context.Sessions
                    .CountAsync(s => s.Date.Date == today);

                // Calculate today's payments
                var todayPayments = await context.Payments
                    .Where(p => p.PaymentDate.Date == today)
                    .SumAsync(p => p.Amount);

                // Calculate outstanding dues
                var outstandingDues = await context.Students
                    .SumAsync(s => s.TotalDue);

                // Calculate attendance rate for this month
                var thisMonth = new DateTime(today.Year, today.Month, 1);
                var totalAttendances = await context.Attendances
                    .CountAsync(a => a.Date >= thisMonth);
                var presentAttendances = await context.Attendances
                    .CountAsync(a => a.Date >= thisMonth && a.IsPresent);

                var attendanceRate = totalAttendances > 0 ?
                    (double)presentAttendances / totalAttendances * 100 : 0;

                // Update UI
                TxtTotalStudents.Text = totalStudents.ToString();
                TxtTotalTeachers.Text = totalTeachers.ToString();
                TxtTotalGroups.Text = totalGroups.ToString();
                TxtTodaySessions.Text = todaySessions.ToString();
                TxtTodayPayments.Text = $"{todayPayments:C}";
                TxtOutstandingDues.Text = $"{outstandingDues:C}";
                TxtAttendanceRate.Text = $"{attendanceRate:F1}%";
            }
            catch (Exception ex)
            {
                // Set default values in case of error
                TxtTotalStudents.Text = "0";
                TxtTotalTeachers.Text = "0";
                TxtTotalGroups.Text = "0";
                TxtTodaySessions.Text = "0";
                TxtTodayPayments.Text = "0 ج.م";
                TxtOutstandingDues.Text = "0 ج.م";
                TxtAttendanceRate.Text = "0%";
            }
        }

        private async void LoadRecentActivities()
        {
            try
            {
                using var context = new EduTrackDbContext();
                var activities = new List<RecentActivity>();

                // Get recent students (last 3)
                var recentStudents = await context.Students
                    .OrderByDescending(s => s.CreatedAt)
                    .Take(2)
                    .ToListAsync();

                foreach (var student in recentStudents)
                {
                    activities.Add(new RecentActivity
                    {
                        Activity = $"تم تسجيل طالب جديد: {student.Name}",
                        Date = student.CreatedAt.ToString("yyyy/MM/dd HH:mm"),
                        Type = "طالب"
                    });
                }

                // Get recent payments (last 2)
                var recentPayments = await context.Payments
                    .Include(p => p.Student)
                    .OrderByDescending(p => p.PaymentDate)
                    .Take(2)
                    .ToListAsync();

                foreach (var payment in recentPayments)
                {
                    activities.Add(new RecentActivity
                    {
                        Activity = $"تم استلام دفعة من: {payment.Student?.Name} - {payment.Amount:C}",
                        Date = payment.PaymentDate.ToString("yyyy/MM/dd HH:mm"),
                        Type = "مالي"
                    });
                }

                // Get recent groups (last 1)
                var recentGroups = await context.Groups
                    .OrderByDescending(g => g.CreatedAt)
                    .Take(1)
                    .ToListAsync();

                foreach (var group in recentGroups)
                {
                    activities.Add(new RecentActivity
                    {
                        Activity = $"تم إنشاء مجموعة جديدة: {group.Name}",
                        Date = group.CreatedAt.ToString("yyyy/MM/dd HH:mm"),
                        Type = "مجموعة"
                    });
                }

                // If no activities, show welcome message
                if (!activities.Any())
                {
                    activities.Add(new RecentActivity
                    {
                        Activity = "مرحباً بك في نظام إدارة المراكز التعليمية",
                        Date = DateTime.Now.ToString("yyyy/MM/dd HH:mm"),
                        Type = "نظام"
                    });
                    activities.Add(new RecentActivity
                    {
                        Activity = "ابدأ بإضافة المعلمين والطلاب",
                        Date = DateTime.Now.ToString("yyyy/MM/dd HH:mm"),
                        Type = "نظام"
                    });
                    activities.Add(new RecentActivity
                    {
                        Activity = "أنشئ المجموعات وابدأ جدولة الحصص",
                        Date = DateTime.Now.ToString("yyyy/MM/dd HH:mm"),
                        Type = "نظام"
                    });
                }

                LstRecentActivities.ItemsSource = activities.OrderByDescending(a => a.Date).ToList();
            }
            catch (Exception ex)
            {
                var defaultActivities = new List<RecentActivity>
                {
                    new RecentActivity
                    {
                        Activity = "مرحباً بك في نظام إدارة المراكز التعليمية",
                        Date = DateTime.Now.ToString("yyyy/MM/dd HH:mm"),
                        Type = "نظام"
                    }
                };
                LstRecentActivities.ItemsSource = defaultActivities;
            }
        }

        private void BtnAddStudent_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var addStudentWindow = new AddEditStudentWindow();
                addStudentWindow.ShowDialog();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في فتح نافذة إضافة الطالب: {ex.Message}", "خطأ",
                              MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void BtnAddTeacher_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var addTeacherWindow = new AddEditTeacherWindow();
                addTeacherWindow.ShowDialog();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في فتح نافذة إضافة المعلم: {ex.Message}", "خطأ",
                              MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void BtnCreateGroup_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var addGroupWindow = new AddEditGroupWindow();
                addGroupWindow.ShowDialog();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في فتح نافذة إضافة المجموعة: {ex.Message}", "خطأ",
                              MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void BtnTakeAttendance_Click(object sender, RoutedEventArgs e)
        {
            // Navigate to attendance page
            var mainWindow = Application.Current.MainWindow as MainWindow;
            mainWindow?.BtnAttendance.RaiseEvent(new RoutedEventArgs(Button.ClickEvent));
        }
    }

    public class RecentActivity
    {
        public string Activity { get; set; } = string.Empty;
        public string Date { get; set; } = string.Empty;
        public string Type { get; set; } = string.Empty;
    }
}
