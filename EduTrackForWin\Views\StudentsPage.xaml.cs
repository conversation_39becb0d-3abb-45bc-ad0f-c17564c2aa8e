using System.Collections.ObjectModel;
using System.Windows;
using System.Windows.Controls;
using EduTrackForWin.Services;
using EduTrackForWin.Models;

namespace EduTrackForWin.Views
{
    public partial class StudentsPage : Page
    {
        private readonly StudentService _studentService;
        private ObservableCollection<Student> _students;
        private List<Student> _allStudents;

        public StudentsPage()
        {
            InitializeComponent();
            _studentService = new StudentService();
            _students = new ObservableCollection<Student>();
            _allStudents = new List<Student>();
            
            DgStudents.ItemsSource = _students;
            LoadStudents();
        }

        private async void LoadStudents()
        {
            try
            {
                _allStudents = await _studentService.GetAllStudentsAsync();
                RefreshStudentsList();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل بيانات الطلاب: {ex.Message}", "خطأ", 
                              MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void RefreshStudentsList()
        {
            _students.Clear();
            
            var filteredStudents = _allStudents.AsEnumerable();

            // Apply search filter
            if (!string.IsNullOrWhiteSpace(TxtSearch.Text))
            {
                var searchTerm = TxtSearch.Text.ToLower();
                filteredStudents = filteredStudents.Where(s => 
                    s.Name.ToLower().Contains(searchTerm) ||
                    s.Phone.Contains(searchTerm) ||
                    s.Grade.ToLower().Contains(searchTerm));
            }

            // Apply grade filter
            if (CmbGradeFilter.SelectedIndex > 0)
            {
                var selectedGrade = ((ComboBoxItem)CmbGradeFilter.SelectedItem).Content.ToString();
                filteredStudents = filteredStudents.Where(s => s.Grade == selectedGrade);
            }

            // Apply payment status filter
            if (CmbPaymentFilter.SelectedIndex > 0)
            {
                var selectedStatus = ((ComboBoxItem)CmbPaymentFilter.SelectedItem).Content.ToString();
                filteredStudents = filteredStudents.Where(s => s.PaymentStatus == selectedStatus);
            }

            foreach (var student in filteredStudents)
            {
                _students.Add(student);
            }

            // Show/hide no data message
            TxtNoData.Visibility = _students.Count == 0 ? Visibility.Visible : Visibility.Collapsed;
            DgStudents.Visibility = _students.Count == 0 ? Visibility.Collapsed : Visibility.Visible;
        }

        private void TxtSearch_TextChanged(object sender, TextChangedEventArgs e)
        {
            RefreshStudentsList();
        }

        private void CmbGradeFilter_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            if (IsLoaded)
                RefreshStudentsList();
        }

        private void CmbPaymentFilter_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            if (IsLoaded)
                RefreshStudentsList();
        }

        private void BtnRefresh_Click(object sender, RoutedEventArgs e)
        {
            LoadStudents();
        }

        private void BtnAddStudent_Click(object sender, RoutedEventArgs e)
        {
            var addStudentWindow = new AddEditStudentWindow();
            if (addStudentWindow.ShowDialog() == true)
            {
                LoadStudents(); // Refresh the list
            }
        }

        private void BtnViewStudent_Click(object sender, RoutedEventArgs e)
        {
            if (sender is Button button && button.Tag is int studentId)
            {
                var student = _allStudents.FirstOrDefault(s => s.Id == studentId);
                if (student != null)
                {
                    var viewWindow = new StudentDetailsWindow(student);
                    viewWindow.ShowDialog();
                }
            }
        }

        private void BtnEditStudent_Click(object sender, RoutedEventArgs e)
        {
            if (sender is Button button && button.Tag is int studentId)
            {
                var student = _allStudents.FirstOrDefault(s => s.Id == studentId);
                if (student != null)
                {
                    var editWindow = new AddEditStudentWindow(student);
                    if (editWindow.ShowDialog() == true)
                    {
                        LoadStudents(); // Refresh the list
                    }
                }
            }
        }

        private async void BtnDeleteStudent_Click(object sender, RoutedEventArgs e)
        {
            if (sender is Button button && button.Tag is int studentId)
            {
                var student = _allStudents.FirstOrDefault(s => s.Id == studentId);
                if (student != null)
                {
                    var result = MessageBox.Show(
                        $"هل أنت متأكد من حذف الطالب '{student.Name}'؟\nسيتم حذف جميع البيانات المرتبطة به.",
                        "تأكيد الحذف",
                        MessageBoxButton.YesNo,
                        MessageBoxImage.Warning);

                    if (result == MessageBoxResult.Yes)
                    {
                        try
                        {
                            await _studentService.DeleteStudentAsync(studentId);
                            LoadStudents(); // Refresh the list
                            MessageBox.Show("تم حذف الطالب بنجاح", "نجح", 
                                          MessageBoxButton.OK, MessageBoxImage.Information);
                        }
                        catch (Exception ex)
                        {
                            MessageBox.Show($"خطأ في حذف الطالب: {ex.Message}", "خطأ", 
                                          MessageBoxButton.OK, MessageBoxImage.Error);
                        }
                    }
                }
            }
        }

        private void DgStudents_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            // Handle selection change if needed
        }
    }
}
