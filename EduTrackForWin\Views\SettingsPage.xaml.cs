using System.Windows;
using System.Windows.Controls;

namespace EduTrackForWin.Views
{
    public partial class SettingsPage : Page
    {
        public SettingsPage()
        {
            InitializeComponent();
            LoadSettings();
        }

        private void LoadSettings()
        {
            // Load settings from configuration or database
            // This is a placeholder implementation
        }

        private void BtnSaveSettings_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // Validate input
                if (!ValidateSettings())
                    return;

                // Save settings to configuration or database
                SaveSettingsToStorage();

                MessageBox.Show("تم حفظ الإعدادات بنجاح", "حفظ الإعدادات", 
                              MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في حفظ الإعدادات: {ex.Message}", "خطأ", 
                              MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void BtnResetSettings_Click(object sender, RoutedEventArgs e)
        {
            var result = MessageBox.Show(
                "هل أنت متأكد من استعادة الإعدادات الافتراضية؟\nسيتم فقدان جميع الإعدادات الحالية.",
                "استعادة الإعدادات الافتراضية",
                MessageBoxButton.YesNo,
                MessageBoxImage.Warning);

            if (result == MessageBoxResult.Yes)
            {
                ResetToDefaultSettings();
                MessageBox.Show("تم استعادة الإعدادات الافتراضية", "استعادة الإعدادات", 
                              MessageBoxButton.OK, MessageBoxImage.Information);
            }
        }

        private bool ValidateSettings()
        {
            // Validate center name
            if (string.IsNullOrWhiteSpace(TxtCenterName.Text))
            {
                MessageBox.Show("يرجى إدخال اسم المركز", "خطأ في البيانات", 
                              MessageBoxButton.OK, MessageBoxImage.Warning);
                TxtCenterName.Focus();
                return false;
            }

            // Validate VAT rate
            if (!decimal.TryParse(TxtVATRate.Text, out var vatRate) || vatRate < 0 || vatRate > 100)
            {
                MessageBox.Show("يرجى إدخال نسبة ضريبة صحيحة (0-100)", "خطأ في البيانات", 
                              MessageBoxButton.OK, MessageBoxImage.Warning);
                TxtVATRate.Focus();
                return false;
            }

            // Validate session duration
            if (!int.TryParse(TxtDefaultSessionDuration.Text, out var duration) || duration <= 0)
            {
                MessageBox.Show("يرجى إدخال مدة حصة صحيحة", "خطأ في البيانات", 
                              MessageBoxButton.OK, MessageBoxImage.Warning);
                TxtDefaultSessionDuration.Focus();
                return false;
            }

            // Validate max students per group
            if (!int.TryParse(TxtMaxStudentsPerGroup.Text, out var maxStudents) || maxStudents <= 0)
            {
                MessageBox.Show("يرجى إدخال عدد طلاب صحيح", "خطأ في البيانات", 
                              MessageBoxButton.OK, MessageBoxImage.Warning);
                TxtMaxStudentsPerGroup.Focus();
                return false;
            }

            return true;
        }

        private void SaveSettingsToStorage()
        {
            // Implementation to save settings to configuration file or database
            // This would typically use a settings service or configuration manager
            
            // Example:
            // var settings = new CenterSettings
            // {
            //     CenterName = TxtCenterName.Text,
            //     CenterPhone = TxtCenterPhone.Text,
            //     CenterEmail = TxtCenterEmail.Text,
            //     CenterAddress = TxtCenterAddress.Text,
            //     CenterWebsite = TxtCenterWebsite.Text,
            //     Currency = ((ComboBoxItem)CmbCurrency.SelectedItem).Content.ToString(),
            //     VATRate = decimal.Parse(TxtVATRate.Text),
            //     LateFee = decimal.Parse(TxtLateFee.Text),
            //     EarlyPaymentDiscount = decimal.Parse(TxtEarlyPaymentDiscount.Text),
            //     DefaultSessionDuration = int.Parse(TxtDefaultSessionDuration.Text),
            //     MaxStudentsPerGroup = int.Parse(TxtMaxStudentsPerGroup.Text),
            //     AutoBackup = ChkAutoBackup.IsChecked == true,
            //     EmailNotifications = ChkEmailNotifications.IsChecked == true,
            //     SMSNotifications = ChkSMSNotifications.IsChecked == true,
            //     Language = ((ComboBoxItem)CmbLanguage.SelectedItem).Content.ToString(),
            //     TimeZone = ((ComboBoxItem)CmbTimeZone.SelectedItem).Content.ToString()
            // };
            
            // _settingsService.SaveSettings(settings);
        }

        private void ResetToDefaultSettings()
        {
            // Reset all controls to default values
            TxtCenterName.Text = "مركز النور التعليمي";
            TxtCenterPhone.Text = "01234567890";
            TxtCenterEmail.Text = "<EMAIL>";
            TxtCenterAddress.Text = "شارع الجامعة، المنصورة، الدقهلية";
            TxtCenterWebsite.Text = "www.alnoor-center.com";
            
            CmbCurrency.SelectedIndex = 0;
            TxtVATRate.Text = "14";
            TxtLateFee.Text = "10";
            TxtEarlyPaymentDiscount.Text = "5";
            
            TxtDefaultSessionDuration.Text = "60";
            TxtMaxStudentsPerGroup.Text = "15";
            TxtWorkStartTime.Text = "08:00";
            TxtWorkEndTime.Text = "22:00";
            
            ChkAutoBackup.IsChecked = true;
            ChkEmailNotifications.IsChecked = true;
            ChkSMSNotifications.IsChecked = false;
            
            CmbLanguage.SelectedIndex = 0;
            CmbTimeZone.SelectedIndex = 0;
        }
    }
}
