using System.Windows;
using System.Windows.Controls;
using EduTrackForWin.Models;

namespace EduTrackForWin.Views
{
    public partial class AddEditScheduleWindow : Window
    {
        public AddEditScheduleWindow(Schedule schedule = null)
        {
            InitializeComponent();
            // Initialize with existing schedule if provided
            if (schedule != null)
            {
                // Set up form with schedule data
            }
        }

        private void CmbGroup_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            // Handle group selection change
        }

        private void DpDate_SelectedDateChanged(object sender, SelectionChangedEventArgs e)
        {
            // Handle date selection change
        }

        private void Day_CheckedChanged(object sender, RoutedEventArgs e)
        {
            // Handle day checkbox changes
        }

        private void Time_TextChanged(object sender, TextChangedEventArgs e)
        {
            // Handle time text changes
        }

        private void BtnSave_Click(object sender, RoutedEventArgs e)
        {
            // Handle save button click
        }

        private void BtnCancel_Click(object sender, RoutedEventArgs e)
        {
            DialogResult = false;
            Close();
        }
    }
}