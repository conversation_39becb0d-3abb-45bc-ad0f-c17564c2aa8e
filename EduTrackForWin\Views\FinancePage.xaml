<Page x:Class="EduTrackForWin.Views.FinancePage"
      xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
      xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
      xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
      xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
      xmlns:local="clr-namespace:EduTrackForWin.Views"
      mc:Ignorable="d"
      d:DesignHeight="800" d:DesignWidth="1200"
      Title="FinancePage">

    <Page.Resources>
        <!-- Styles -->
        <Style x:Key="HeaderStyle" TargetType="TextBlock" BasedOn="{StaticResource HeadingStyle}">
            <Setter Property="Foreground" Value="{StaticResource PrimaryColor}"/>
        </Style>

        <Style x:Key="SubHeaderStyle" TargetType="TextBlock" BasedOn="{StaticResource SubHeadingStyle}">
            <Setter Property="Foreground" Value="{StaticResource TextSecondaryColor}"/>
        </Style>

        <Style x:Key="StatCardStyle" TargetType="Border" BasedOn="{StaticResource ModernCardStyle}">
            <Setter Property="Padding" Value="20"/>
            <Setter Property="Margin" Value="10"/>
        </Style>

        <Style x:Key="PrimaryButtonStyle" TargetType="Button">
            <Setter Property="Background" Value="{StaticResource SuccessColor}"/>
            <Setter Property="Foreground" Value="White"/>
        </Style>

        <Style x:Key="SecondaryButtonStyle" TargetType="Button">
            <Setter Property="Background" Value="{StaticResource PrimaryColor}"/>
            <Setter Property="Foreground" Value="White"/>
        </Style>

        <Style x:Key="FilterComboBoxStyle" TargetType="ComboBox">
            <Setter Property="Padding" Value="8"/>
            <Setter Property="Margin" Value="5"/>
            <Setter Property="MinWidth" Value="150"/>
        </Style>

        <Style x:Key="SearchTextBoxStyle" TargetType="TextBox">
            <Setter Property="Padding" Value="8"/>
            <Setter Property="Margin" Value="5"/>
            <Setter Property="MinWidth" Value="200"/>
        </Style>
    </Page.Resources>

    <Grid Background="#F5F5F5">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <Border Grid.Row="0" Background="White" Padding="20" Margin="20,20,20,10">
            <Border.Effect>
                <DropShadowEffect Color="Gray" Direction="270" ShadowDepth="1" Opacity="0.2" BlurRadius="5"/>
            </Border.Effect>

            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <StackPanel Grid.Column="0">
                    <TextBlock Text="💰 النظام المالي" Style="{StaticResource HeaderStyle}"/>
                    <TextBlock Text="إدارة المدفوعات والمستحقات المالية"
                             Style="{StaticResource SubHeaderStyle}"/>
                </StackPanel>

                <StackPanel Grid.Column="1" Orientation="Horizontal">
                    <Button Name="BtnAddPayment" Content="تسجيل دفعة جديدة"
                          Style="{StaticResource PrimaryButtonStyle}" Click="BtnAddPayment_Click"/>
                    <Button Name="BtnGenerateReport" Content="تقرير مالي"
                          Style="{StaticResource SecondaryButtonStyle}" Click="BtnGenerateReport_Click"/>
                </StackPanel>
            </Grid>
        </Border>

        <!-- Financial Statistics -->
        <Border Grid.Row="1" Background="White" Padding="20" Margin="20,10,20,10">
            <Border.Effect>
                <DropShadowEffect Color="Gray" Direction="270" ShadowDepth="1" Opacity="0.2" BlurRadius="5"/>
            </Border.Effect>

            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>

                <!-- Total Revenue -->
                <Border Grid.Column="0" Style="{StaticResource StatCardStyle}" Background="#E8F5E8">
                    <StackPanel>
                        <TextBlock Text="💰" FontSize="32" HorizontalAlignment="Center" Margin="0,0,0,10"/>
                        <TextBlock Text="إجمالي الإيرادات" FontWeight="Bold" HorizontalAlignment="Center"
                                 Foreground="#2E7D32" FontSize="14"/>
                        <TextBlock Name="TxtTotalRevenue" Text="0 ج.م" FontWeight="Bold"
                                 HorizontalAlignment="Center" Foreground="#1B5E20" FontSize="18" Margin="0,5,0,0"/>
                    </StackPanel>
                </Border>

                <!-- Monthly Revenue -->
                <Border Grid.Column="1" Style="{StaticResource StatCardStyle}" Background="#E3F2FD">
                    <StackPanel>
                        <TextBlock Text="📊" FontSize="32" HorizontalAlignment="Center" Margin="0,0,0,10"/>
                        <TextBlock Text="إيرادات الشهر" FontWeight="Bold" HorizontalAlignment="Center"
                                 Foreground="#1976D2" FontSize="14"/>
                        <TextBlock Name="TxtMonthlyRevenue" Text="0 ج.م" FontWeight="Bold"
                                 HorizontalAlignment="Center" Foreground="#0D47A1" FontSize="18" Margin="0,5,0,0"/>
                    </StackPanel>
                </Border>

                <!-- Outstanding Dues -->
                <Border Grid.Column="2" Style="{StaticResource StatCardStyle}" Background="#FFF3E0">
                    <StackPanel>
                        <TextBlock Text="⚠️" FontSize="32" HorizontalAlignment="Center" Margin="0,0,0,10"/>
                        <TextBlock Text="المبالغ المستحقة" FontWeight="Bold" HorizontalAlignment="Center"
                                 Foreground="#F57C00" FontSize="14"/>
                        <TextBlock Name="TxtOutstandingDues" Text="0 ج.م" FontWeight="Bold"
                                 HorizontalAlignment="Center" Foreground="#E65100" FontSize="18" Margin="0,5,0,0"/>
                    </StackPanel>
                </Border>

                <!-- Today's Payments -->
                <Border Grid.Column="3" Style="{StaticResource StatCardStyle}" Background="#F3E5F5">
                    <StackPanel>
                        <TextBlock Text="💳" FontSize="32" HorizontalAlignment="Center" Margin="0,0,0,10"/>
                        <TextBlock Text="مدفوعات اليوم" FontWeight="Bold" HorizontalAlignment="Center"
                                 Foreground="#7B1FA2" FontSize="14"/>
                        <TextBlock Name="TxtTodayPayments" Text="0 ج.م" FontWeight="Bold"
                                 HorizontalAlignment="Center" Foreground="#4A148C" FontSize="18" Margin="0,5,0,0"/>
                    </StackPanel>
                </Border>
            </Grid>
        </Border>

        <!-- Main Content -->
        <Border Grid.Row="2" Background="White" Padding="20" Margin="20,10,20,20">
            <Border.Effect>
                <DropShadowEffect Color="Gray" Direction="270" ShadowDepth="1" Opacity="0.2" BlurRadius="5"/>
            </Border.Effect>

            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="*"/>
                </Grid.RowDefinitions>

                <!-- Filters and Search -->
                <Grid Grid.Row="0" Margin="0,0,0,20">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="Auto"/>
                        <ColumnDefinition Width="Auto"/>
                        <ColumnDefinition Width="Auto"/>
                        <ColumnDefinition Width="Auto"/>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>

                    <TextBlock Grid.Column="0" Text="فلترة:" VerticalAlignment="Center"
                             FontWeight="SemiBold" Margin="0,0,10,0"/>

                    <ComboBox Name="CmbStudentFilter" Grid.Column="1"
                            Style="{StaticResource FilterComboBoxStyle}"
                            SelectionChanged="Filter_Changed">
                        <ComboBoxItem Content="جميع الطلاب" IsSelected="True"/>
                    </ComboBox>

                    <ComboBox Name="CmbPaymentTypeFilter" Grid.Column="2"
                            Style="{StaticResource FilterComboBoxStyle}"
                            SelectionChanged="Filter_Changed">
                        <ComboBoxItem Content="جميع الأنواع" IsSelected="True"/>
                        <ComboBoxItem Content="اشتراك شهري"/>
                        <ComboBoxItem Content="رسوم تسجيل"/>
                        <ComboBoxItem Content="رسوم إضافية"/>
                        <ComboBoxItem Content="خصم"/>
                        <ComboBoxItem Content="غرامة تأخير"/>
                    </ComboBox>

                    <DatePicker Name="DpDateFilter" Grid.Column="3"
                              SelectedDateChanged="Filter_Changed"
                              Margin="5"/>

                    <TextBox Name="TxtSearch" Grid.Column="4"
                           Style="{StaticResource SearchTextBoxStyle}"
                           TextChanged="TxtSearch_TextChanged"
                           Text="البحث في المدفوعات..."/>

                    <Button Name="BtnRefresh" Grid.Column="5" Content="تحديث"
                          Style="{StaticResource SecondaryButtonStyle}" Click="BtnRefresh_Click"/>
                </Grid>

                <!-- Payments DataGrid -->
                <DataGrid Name="DgPayments" Grid.Row="1"
                        AutoGenerateColumns="False"
                        CanUserAddRows="False"
                        CanUserDeleteRows="False"
                        IsReadOnly="True"
                        GridLinesVisibility="Horizontal"
                        HeadersVisibility="Column"
                        AlternatingRowBackground="#F9F9F9"
                        RowBackground="White"
                        BorderBrush="#E0E0E0"
                        BorderThickness="1">

                    <DataGrid.Columns>
                        <!-- Payment ID -->
                        <DataGridTextColumn Header="رقم الدفعة" Binding="{Binding Id}" Width="80"/>

                        <!-- Student Name -->
                        <DataGridTextColumn Header="اسم الطالب" Binding="{Binding Student.Name}" Width="150"/>

                        <!-- Payment Type -->
                        <DataGridTextColumn Header="نوع الدفعة" Binding="{Binding TypeName}" Width="120"/>

                        <!-- Amount -->
                        <DataGridTextColumn Header="المبلغ" Width="100">
                            <DataGridTextColumn.Binding>
                                <Binding Path="Amount" StringFormat="{}{0:C}"/>
                            </DataGridTextColumn.Binding>
                        </DataGridTextColumn>

                        <!-- Payment Date -->
                        <DataGridTextColumn Header="تاريخ الدفع" Width="120">
                            <DataGridTextColumn.Binding>
                                <Binding Path="PaymentDate" StringFormat="{}{0:yyyy/MM/dd}"/>
                            </DataGridTextColumn.Binding>
                        </DataGridTextColumn>

                        <!-- Payment Method -->
                        <DataGridTextColumn Header="طريقة الدفع" Binding="{Binding PaymentMethod}" Width="100"/>

                        <!-- Notes -->
                        <DataGridTextColumn Header="ملاحظات" Binding="{Binding Notes}" Width="*"/>

                        <!-- Actions -->
                        <DataGridTemplateColumn Header="الإجراءات" Width="150">
                            <DataGridTemplateColumn.CellTemplate>
                                <DataTemplate>
                                    <StackPanel Orientation="Horizontal">
                                        <Button Content="عرض"
                                              Background="#2196F3"
                                              Foreground="White"
                                              BorderThickness="0"
                                              Padding="8,4"
                                              Margin="2"
                                              Cursor="Hand"
                                              Click="BtnViewPayment_Click"/>
                                        <Button Content="تعديل"
                                              Background="#FF9800"
                                              Foreground="White"
                                              BorderThickness="0"
                                              Padding="8,4"
                                              Margin="2"
                                              Cursor="Hand"
                                              Click="BtnEditPayment_Click"/>
                                        <Button Content="حذف"
                                              Background="#F44336"
                                              Foreground="White"
                                              BorderThickness="0"
                                              Padding="8,4"
                                              Margin="2"
                                              Cursor="Hand"
                                              Click="BtnDeletePayment_Click"/>
                                    </StackPanel>
                                </DataTemplate>
                            </DataGridTemplateColumn.CellTemplate>
                        </DataGridTemplateColumn>
                    </DataGrid.Columns>
                </DataGrid>
            </Grid>
        </Border>
    </Grid>
</Page>
