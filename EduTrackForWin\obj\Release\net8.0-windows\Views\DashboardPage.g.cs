﻿#pragma checksum "..\..\..\..\Views\DashboardPage.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "D03584ECC0E3CBB8E06DB22732F17EBEAD64C85A"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using EduTrackForWin.Views;
using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace EduTrackForWin.Views {
    
    
    /// <summary>
    /// DashboardPage
    /// </summary>
    public partial class DashboardPage : System.Windows.Controls.Page, System.Windows.Markup.IComponentConnector {
        
        
        #line 66 "..\..\..\..\Views\DashboardPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TxtTotalStudents;
        
        #line default
        #line hidden
        
        
        #line 76 "..\..\..\..\Views\DashboardPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TxtTotalTeachers;
        
        #line default
        #line hidden
        
        
        #line 86 "..\..\..\..\Views\DashboardPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TxtTotalGroups;
        
        #line default
        #line hidden
        
        
        #line 96 "..\..\..\..\Views\DashboardPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TxtTodaySessions;
        
        #line default
        #line hidden
        
        
        #line 115 "..\..\..\..\Views\DashboardPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TxtTodayPayments;
        
        #line default
        #line hidden
        
        
        #line 125 "..\..\..\..\Views\DashboardPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TxtOutstandingDues;
        
        #line default
        #line hidden
        
        
        #line 135 "..\..\..\..\Views\DashboardPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TxtAttendanceRate;
        
        #line default
        #line hidden
        
        
        #line 148 "..\..\..\..\Views\DashboardPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ListView LstRecentActivities;
        
        #line default
        #line hidden
        
        
        #line 167 "..\..\..\..\Views\DashboardPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BtnAddStudent;
        
        #line default
        #line hidden
        
        
        #line 183 "..\..\..\..\Views\DashboardPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BtnAddTeacher;
        
        #line default
        #line hidden
        
        
        #line 199 "..\..\..\..\Views\DashboardPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BtnCreateGroup;
        
        #line default
        #line hidden
        
        
        #line 215 "..\..\..\..\Views\DashboardPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BtnTakeAttendance;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/EduTrackForWin;component/views/dashboardpage.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\Views\DashboardPage.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.TxtTotalStudents = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 2:
            this.TxtTotalTeachers = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 3:
            this.TxtTotalGroups = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 4:
            this.TxtTodaySessions = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 5:
            this.TxtTodayPayments = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 6:
            this.TxtOutstandingDues = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 7:
            this.TxtAttendanceRate = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 8:
            this.LstRecentActivities = ((System.Windows.Controls.ListView)(target));
            return;
            case 9:
            this.BtnAddStudent = ((System.Windows.Controls.Button)(target));
            
            #line 170 "..\..\..\..\Views\DashboardPage.xaml"
            this.BtnAddStudent.Click += new System.Windows.RoutedEventHandler(this.BtnAddStudent_Click);
            
            #line default
            #line hidden
            return;
            case 10:
            this.BtnAddTeacher = ((System.Windows.Controls.Button)(target));
            
            #line 186 "..\..\..\..\Views\DashboardPage.xaml"
            this.BtnAddTeacher.Click += new System.Windows.RoutedEventHandler(this.BtnAddTeacher_Click);
            
            #line default
            #line hidden
            return;
            case 11:
            this.BtnCreateGroup = ((System.Windows.Controls.Button)(target));
            
            #line 202 "..\..\..\..\Views\DashboardPage.xaml"
            this.BtnCreateGroup.Click += new System.Windows.RoutedEventHandler(this.BtnCreateGroup_Click);
            
            #line default
            #line hidden
            return;
            case 12:
            this.BtnTakeAttendance = ((System.Windows.Controls.Button)(target));
            
            #line 218 "..\..\..\..\Views\DashboardPage.xaml"
            this.BtnTakeAttendance.Click += new System.Windows.RoutedEventHandler(this.BtnTakeAttendance_Click);
            
            #line default
            #line hidden
            return;
            }
            this._contentLoaded = true;
        }
    }
}

