<Window x:Class="EduTrackForWin.Views.GroupDetailsWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:EduTrackForWin.Views"
        mc:Ignorable="d"
        Title="تفاصيل المجموعة" Height="700" Width="900"
        WindowStartupLocation="CenterOwner">

    <Window.Resources>
        <!-- Card Style -->
        <Style x:Key="CardStyle" TargetType="Border">
            <Setter Property="Background" Value="White"/>
            <Setter Property="CornerRadius" Value="8"/>
            <Setter Property="Padding" Value="20"/>
            <Setter Property="Margin" Value="10"/>
            <Setter Property="Effect">
                <Setter.Value>
                    <DropShadowEffect Color="Gray" Direction="270" ShadowDepth="2" Opacity="0.3" BlurRadius="5"/>
                </Setter.Value>
            </Setter>
        </Style>

        <!-- Info Label Style -->
        <Style x:Key="InfoLabelStyle" TargetType="TextBlock">
            <Setter Property="FontWeight" Value="SemiBold"/>
            <Setter Property="Foreground" Value="#424242"/>
            <Setter Property="Margin" Value="0,5,10,5"/>
        </Style>

        <!-- Info Value Style -->
        <Style x:Key="InfoValueStyle" TargetType="TextBlock">
            <Setter Property="Foreground" Value="#212121"/>
            <Setter Property="Margin" Value="0,5"/>
        </Style>

        <!-- Section Header Style -->
        <Style x:Key="SectionHeaderStyle" TargetType="TextBlock">
            <Setter Property="FontSize" Value="18"/>
            <Setter Property="FontWeight" Value="Bold"/>
            <Setter Property="Foreground" Value="#FF9800"/>
            <Setter Property="Margin" Value="0,0,0,15"/>
        </Style>
    </Window.Resources>

    <Grid Background="#F5F5F5">
        <ScrollViewer VerticalScrollBarVisibility="Auto" Margin="20">
            <StackPanel>
                <!-- Header -->
                <Border Style="{StaticResource CardStyle}" Background="#FFF3E0">
                    <StackPanel>
                        <TextBlock Name="TxtGroupName" Text="اسم المجموعة" FontSize="24" FontWeight="Bold" 
                                 HorizontalAlignment="Center" Foreground="#E65100"/>
                        <TextBlock Text="تفاصيل المجموعة الكاملة" FontSize="14" 
                                 HorizontalAlignment="Center" Foreground="#424242" Margin="0,5,0,0"/>
                    </StackPanel>
                </Border>

                <!-- Basic Information -->
                <Border Style="{StaticResource CardStyle}">
                    <StackPanel>
                        <TextBlock Text="معلومات المجموعة" Style="{StaticResource SectionHeaderStyle}"/>
                        
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            
                            <StackPanel Grid.Column="0">
                                <StackPanel Orientation="Horizontal">
                                    <TextBlock Text="اسم المجموعة:" Style="{StaticResource InfoLabelStyle}"/>
                                    <TextBlock Name="TxtName" Style="{StaticResource InfoValueStyle}"/>
                                </StackPanel>
                                
                                <StackPanel Orientation="Horizontal">
                                    <TextBlock Text="المادة:" Style="{StaticResource InfoLabelStyle}"/>
                                    <TextBlock Name="TxtSubject" Style="{StaticResource InfoValueStyle}"/>
                                </StackPanel>
                                
                                <StackPanel Orientation="Horizontal">
                                    <TextBlock Text="الصف:" Style="{StaticResource InfoLabelStyle}"/>
                                    <TextBlock Name="TxtGrade" Style="{StaticResource InfoValueStyle}"/>
                                </StackPanel>
                                
                                <StackPanel Orientation="Horizontal">
                                    <TextBlock Text="القاعة:" Style="{StaticResource InfoLabelStyle}"/>
                                    <TextBlock Name="TxtRoom" Style="{StaticResource InfoValueStyle}"/>
                                </StackPanel>
                            </StackPanel>
                            
                            <StackPanel Grid.Column="1">
                                <StackPanel Orientation="Horizontal">
                                    <TextBlock Text="المعلم المسؤول:" Style="{StaticResource InfoLabelStyle}"/>
                                    <TextBlock Name="TxtTeacher" Style="{StaticResource InfoValueStyle}"/>
                                </StackPanel>
                                
                                <StackPanel Orientation="Horizontal">
                                    <TextBlock Text="الرسوم الشهرية:" Style="{StaticResource InfoLabelStyle}"/>
                                    <TextBlock Name="TxtMonthlyFee" Style="{StaticResource InfoValueStyle}"/>
                                </StackPanel>
                                
                                <StackPanel Orientation="Horizontal">
                                    <TextBlock Text="عدد الطلاب:" Style="{StaticResource InfoLabelStyle}"/>
                                    <TextBlock Name="TxtStudentCount" Style="{StaticResource InfoValueStyle}"/>
                                </StackPanel>
                                
                                <StackPanel Orientation="Horizontal">
                                    <TextBlock Text="الحالة:" Style="{StaticResource InfoLabelStyle}"/>
                                    <TextBlock Name="TxtStatus" Style="{StaticResource InfoValueStyle}"/>
                                </StackPanel>
                            </StackPanel>
                        </Grid>
                        
                        <StackPanel Margin="0,10,0,0">
                            <TextBlock Text="وصف المجموعة:" Style="{StaticResource InfoLabelStyle}"/>
                            <TextBlock Name="TxtDescription" Style="{StaticResource InfoValueStyle}" TextWrapping="Wrap"/>
                        </StackPanel>
                    </StackPanel>
                </Border>

                <!-- Financial Summary -->
                <Border Style="{StaticResource CardStyle}">
                    <StackPanel>
                        <TextBlock Text="الملخص المالي" Style="{StaticResource SectionHeaderStyle}"/>
                        
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            
                            <StackPanel Grid.Column="0">
                                <TextBlock Text="الإيرادات الشهرية المتوقعة" Style="{StaticResource InfoLabelStyle}"/>
                                <TextBlock Name="TxtExpectedRevenue" Style="{StaticResource InfoValueStyle}" 
                                         FontSize="18" FontWeight="Bold" Foreground="#4CAF50"/>
                            </StackPanel>
                            
                            <StackPanel Grid.Column="1">
                                <TextBlock Text="إجمالي المدفوعات" Style="{StaticResource InfoLabelStyle}"/>
                                <TextBlock Name="TxtTotalPayments" Style="{StaticResource InfoValueStyle}" 
                                         FontSize="18" FontWeight="Bold" Foreground="#2196F3"/>
                            </StackPanel>
                            
                            <StackPanel Grid.Column="2">
                                <TextBlock Text="المبالغ المستحقة" Style="{StaticResource InfoLabelStyle}"/>
                                <TextBlock Name="TxtOutstandingAmount" Style="{StaticResource InfoValueStyle}" 
                                         FontSize="18" FontWeight="Bold" Foreground="#F44336"/>
                            </StackPanel>
                        </Grid>
                    </StackPanel>
                </Border>

                <!-- Students List -->
                <Border Style="{StaticResource CardStyle}">
                    <StackPanel>
                        <Grid Margin="0,0,0,15">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="Auto"/>
                            </Grid.ColumnDefinitions>
                            
                            <TextBlock Grid.Column="0" Text="قائمة الطلاب" Style="{StaticResource SectionHeaderStyle}"/>
                            
                            <Button Grid.Column="1" Name="BtnManageStudents" Content="إدارة الطلاب"
                                  Background="#FF9800" Foreground="White"
                                  Padding="15,8" BorderThickness="0"
                                  Click="BtnManageStudents_Click">
                                <Button.Template>
                                    <ControlTemplate TargetType="Button">
                                        <Border Background="{TemplateBinding Background}"
                                              CornerRadius="5">
                                            <ContentPresenter HorizontalAlignment="Center"
                                                            VerticalAlignment="Center"
                                                            Margin="{TemplateBinding Padding}"/>
                                        </Border>
                                    </ControlTemplate>
                                </Button.Template>
                            </Button>
                        </Grid>
                        
                        <DataGrid Name="DgStudents" 
                                AutoGenerateColumns="False" 
                                CanUserAddRows="False" 
                                CanUserDeleteRows="False"
                                IsReadOnly="True"
                                GridLinesVisibility="Horizontal"
                                HeadersVisibility="Column"
                                BorderThickness="0"
                                Background="Transparent"
                                RowBackground="White"
                                AlternatingRowBackground="#F5F5F5"
                                Height="200">
                            
                            <DataGrid.Columns>
                                <DataGridTextColumn Header="اسم الطالب" Binding="{Binding Name}" Width="200"/>
                                <DataGridTextColumn Header="الصف" Binding="{Binding Grade}" Width="100"/>
                                <DataGridTextColumn Header="رقم الهاتف" Binding="{Binding Phone}" Width="150"/>
                                <DataGridTextColumn Header="حالة الدفع" Binding="{Binding PaymentStatus}" Width="100"/>
                                <DataGridTextColumn Header="المبلغ المستحق" Binding="{Binding TotalDue, StringFormat='{}{0:C}'}" Width="120"/>
                                <DataGridTextColumn Header="إجمالي المدفوع" Binding="{Binding TotalPaid, StringFormat='{}{0:C}'}" Width="*"/>
                            </DataGrid.Columns>
                        </DataGrid>
                    </StackPanel>
                </Border>

                <!-- Recent Sessions -->
                <Border Style="{StaticResource CardStyle}">
                    <StackPanel>
                        <Grid Margin="0,0,0,15">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="Auto"/>
                            </Grid.ColumnDefinitions>
                            
                            <TextBlock Grid.Column="0" Text="آخر الحصص" Style="{StaticResource SectionHeaderStyle}"/>
                            
                            <Button Grid.Column="1" Name="BtnAddSession" Content="إضافة حصة"
                                  Background="#9C27B0" Foreground="White"
                                  Padding="15,8" BorderThickness="0"
                                  Click="BtnAddSession_Click">
                                <Button.Template>
                                    <ControlTemplate TargetType="Button">
                                        <Border Background="{TemplateBinding Background}"
                                              CornerRadius="5">
                                            <ContentPresenter HorizontalAlignment="Center"
                                                            VerticalAlignment="Center"
                                                            Margin="{TemplateBinding Padding}"/>
                                        </Border>
                                    </ControlTemplate>
                                </Button.Template>
                            </Button>
                        </Grid>
                        
                        <DataGrid Name="DgSessions" 
                                AutoGenerateColumns="False" 
                                CanUserAddRows="False" 
                                CanUserDeleteRows="False"
                                IsReadOnly="True"
                                GridLinesVisibility="Horizontal"
                                HeadersVisibility="Column"
                                BorderThickness="0"
                                Background="Transparent"
                                RowBackground="White"
                                AlternatingRowBackground="#F5F5F5"
                                Height="200">
                            
                            <DataGrid.Columns>
                                <DataGridTextColumn Header="التاريخ" Binding="{Binding Date, StringFormat='{}{0:yyyy/MM/dd}'}" Width="100"/>
                                <DataGridTextColumn Header="الوقت" Binding="{Binding SessionTime}" Width="120"/>
                                <DataGridTextColumn Header="الموضوع" Binding="{Binding Topic}" Width="*"/>
                                <DataGridTextColumn Header="القاعة" Binding="{Binding Room}" Width="100"/>
                                <DataGridTemplateColumn Header="الحالة" Width="80">
                                    <DataGridTemplateColumn.CellTemplate>
                                        <DataTemplate>
                                            <Border CornerRadius="10" Padding="8,4">
                                                <Border.Style>
                                                    <Style TargetType="Border">
                                                        <Style.Triggers>
                                                            <DataTrigger Binding="{Binding IsCompleted}" Value="True">
                                                                <Setter Property="Background" Value="#4CAF50"/>
                                                            </DataTrigger>
                                                            <DataTrigger Binding="{Binding IsCompleted}" Value="False">
                                                                <Setter Property="Background" Value="#FF9800"/>
                                                            </DataTrigger>
                                                        </Style.Triggers>
                                                    </Style>
                                                </Border.Style>
                                                <TextBlock Text="{Binding IsCompleted, Converter={StaticResource BoolToStatusConverter}}" 
                                                         Foreground="White" FontSize="10" HorizontalAlignment="Center"/>
                                            </Border>
                                        </DataTemplate>
                                    </DataGridTemplateColumn.CellTemplate>
                                </DataGridTemplateColumn>
                            </DataGrid.Columns>
                        </DataGrid>
                    </StackPanel>
                </Border>

                <!-- Action Buttons -->
                <Border Style="{StaticResource CardStyle}">
                    <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                        <Button Name="BtnEdit" Content="تعديل المجموعة"
                              Background="#FF9800" Foreground="White"
                              Padding="20,10" Margin="10" BorderThickness="0"
                              Click="BtnEdit_Click">
                            <Button.Template>
                                <ControlTemplate TargetType="Button">
                                    <Border Background="{TemplateBinding Background}"
                                          CornerRadius="5">
                                        <ContentPresenter HorizontalAlignment="Center"
                                                        VerticalAlignment="Center"
                                                        Margin="{TemplateBinding Padding}"/>
                                    </Border>
                                </ControlTemplate>
                            </Button.Template>
                        </Button>

                        <Button Name="BtnViewSchedule" Content="عرض الجدول"
                              Background="#9C27B0" Foreground="White"
                              Padding="20,10" Margin="10" BorderThickness="0"
                              Click="BtnViewSchedule_Click">
                            <Button.Template>
                                <ControlTemplate TargetType="Button">
                                    <Border Background="{TemplateBinding Background}"
                                          CornerRadius="5">
                                        <ContentPresenter HorizontalAlignment="Center"
                                                        VerticalAlignment="Center"
                                                        Margin="{TemplateBinding Padding}"/>
                                    </Border>
                                </ControlTemplate>
                            </Button.Template>
                        </Button>

                        <Button Name="BtnFinancialReport" Content="التقرير المالي"
                              Background="#4CAF50" Foreground="White"
                              Padding="20,10" Margin="10" BorderThickness="0"
                              Click="BtnFinancialReport_Click">
                            <Button.Template>
                                <ControlTemplate TargetType="Button">
                                    <Border Background="{TemplateBinding Background}"
                                          CornerRadius="5">
                                        <ContentPresenter HorizontalAlignment="Center"
                                                        VerticalAlignment="Center"
                                                        Margin="{TemplateBinding Padding}"/>
                                    </Border>
                                </ControlTemplate>
                            </Button.Template>
                        </Button>

                        <Button Name="BtnClose" Content="إغلاق"
                              Background="#757575" Foreground="White"
                              Padding="20,10" Margin="10" BorderThickness="0"
                              Click="BtnClose_Click">
                            <Button.Template>
                                <ControlTemplate TargetType="Button">
                                    <Border Background="{TemplateBinding Background}"
                                          CornerRadius="5">
                                        <ContentPresenter HorizontalAlignment="Center"
                                                        VerticalAlignment="Center"
                                                        Margin="{TemplateBinding Padding}"/>
                                    </Border>
                                </ControlTemplate>
                            </Button.Template>
                        </Button>
                    </StackPanel>
                </Border>
            </StackPanel>
        </ScrollViewer>
    </Grid>
</Window>
