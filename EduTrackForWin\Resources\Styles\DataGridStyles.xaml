<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">

    <!-- Modern DataGrid Style -->
    <Style x:Key="ModernDataGridStyle" TargetType="DataGrid">
        <Setter Property="Background" Value="{StaticResource SurfaceColor}"/>
        <Setter Property="Foreground" Value="{StaticResource TextPrimaryColor}"/>
        <Setter Property="BorderBrush" Value="{StaticResource BorderColor}"/>
        <Setter Property="BorderThickness" Value="1"/>
        <Setter Property="RowDetailsVisibilityMode" Value="VisibleWhenSelected"/>
        <Setter Property="ScrollViewer.CanContentScroll" Value="true"/>
        <Setter Property="ScrollViewer.PanningMode" Value="Both"/>
        <Setter Property="Stylus.IsFlicksEnabled" Value="False"/>
        <Setter Property="AutoGenerateColumns" Value="False"/>
        <Setter Property="CanUserAddRows" Value="False"/>
        <Setter Property="CanUserDeleteRows" Value="False"/>
        <Setter Property="CanUserReorderColumns" Value="True"/>
        <Setter Property="CanUserResizeColumns" Value="True"/>
        <Setter Property="CanUserResizeRows" Value="False"/>
        <Setter Property="CanUserSortColumns" Value="True"/>
        <Setter Property="IsReadOnly" Value="True"/>
        <Setter Property="SelectionMode" Value="Single"/>
        <Setter Property="SelectionUnit" Value="FullRow"/>
        <Setter Property="GridLinesVisibility" Value="None"/>
        <Setter Property="HeadersVisibility" Value="Column"/>
        <Setter Property="RowBackground" Value="Transparent"/>
        <Setter Property="AlternatingRowBackground" Value="#FAFAFA"/>
        <Setter Property="FontFamily" Value="Segoe UI"/>
        <Setter Property="FontSize" Value="14"/>
        <Setter Property="RowHeight" Value="56"/>
        <Setter Property="ColumnHeaderHeight" Value="48"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="DataGrid">
                    <Border Background="{TemplateBinding Background}"
                          BorderBrush="{TemplateBinding BorderBrush}"
                          BorderThickness="{TemplateBinding BorderThickness}"
                          CornerRadius="12"
                          Padding="0">
                        <ScrollViewer Name="DG_ScrollViewer" Focusable="false">
                            <ScrollViewer.Template>
                                <ControlTemplate TargetType="ScrollViewer">
                                    <Grid>
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="Auto"/>
                                            <ColumnDefinition Width="*"/>
                                            <ColumnDefinition Width="Auto"/>
                                        </Grid.ColumnDefinitions>
                                        <Grid.RowDefinitions>
                                            <RowDefinition Height="Auto"/>
                                            <RowDefinition Height="*"/>
                                            <RowDefinition Height="Auto"/>
                                        </Grid.RowDefinitions>

                                        <Button Command="{x:Static DataGrid.SelectAllCommand}"
                                              Focusable="false"
                                              Style="{DynamicResource {ComponentResourceKey ResourceId=DataGridSelectAllButtonStyle, TypeInTargetAssembly={x:Type DataGrid}}}"
                                              Visibility="{Binding HeadersVisibility, ConverterParameter={x:Static DataGridHeadersVisibility.All}, Converter={x:Static DataGrid.HeadersVisibilityConverter}, RelativeSource={RelativeSource AncestorType={x:Type DataGrid}}}"
                                              Width="{Binding CellsPanelHorizontalOffset, RelativeSource={RelativeSource AncestorType={x:Type DataGrid}}}"/>
                                        
                                        <DataGridColumnHeadersPresenter Name="PART_ColumnHeadersPresenter"
                                                                       Grid.Column="1"
                                                                       Visibility="{Binding HeadersVisibility, ConverterParameter={x:Static DataGridHeadersVisibility.Column}, Converter={x:Static DataGrid.HeadersVisibilityConverter}, RelativeSource={RelativeSource AncestorType={x:Type DataGrid}}}"/>

                                        <ScrollContentPresenter Name="PART_ScrollContentPresenter"
                                                              Grid.ColumnSpan="2"
                                                              Grid.Row="1"
                                                              CanContentScroll="{TemplateBinding CanContentScroll}"/>

                                        <ScrollBar Name="PART_VerticalScrollBar"
                                                 Grid.Column="2"
                                                 Grid.Row="1"
                                                 Orientation="Vertical"
                                                 ViewportSize="{TemplateBinding ViewportHeight}"
                                                 Maximum="{TemplateBinding ScrollableHeight}"
                                                 Visibility="{TemplateBinding ComputedVerticalScrollBarVisibility}"
                                                 Value="{Binding VerticalOffset, Mode=OneWay, RelativeSource={RelativeSource TemplatedParent}}"/>

                                        <Grid Grid.Column="1" Grid.Row="2">
                                            <Grid.ColumnDefinitions>
                                                <ColumnDefinition Width="{Binding NonFrozenColumnsViewportHorizontalOffset, RelativeSource={RelativeSource AncestorType={x:Type DataGrid}}}"/>
                                                <ColumnDefinition Width="*"/>
                                            </Grid.ColumnDefinitions>
                                            <ScrollBar Name="PART_HorizontalScrollBar"
                                                     Grid.Column="1"
                                                     Orientation="Horizontal"
                                                     ViewportSize="{TemplateBinding ViewportWidth}"
                                                     Maximum="{TemplateBinding ScrollableWidth}"
                                                     Visibility="{TemplateBinding ComputedHorizontalScrollBarVisibility}"
                                                     Value="{Binding HorizontalOffset, Mode=OneWay, RelativeSource={RelativeSource TemplatedParent}}"/>
                                        </Grid>
                                    </Grid>
                                </ControlTemplate>
                            </ScrollViewer.Template>
                            <ItemsPresenter SnapsToDevicePixels="{TemplateBinding SnapsToDevicePixels}"/>
                        </ScrollViewer>
                    </Border>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <!-- Modern DataGrid Column Header Style -->
    <Style x:Key="ModernDataGridColumnHeaderStyle" TargetType="DataGridColumnHeader">
        <Setter Property="Background" Value="{StaticResource BackgroundColor}"/>
        <Setter Property="Foreground" Value="{StaticResource TextPrimaryColor}"/>
        <Setter Property="BorderBrush" Value="{StaticResource DividerColor}"/>
        <Setter Property="BorderThickness" Value="0,0,0,1"/>
        <Setter Property="Padding" Value="16,12"/>
        <Setter Property="FontWeight" Value="SemiBold"/>
        <Setter Property="FontSize" Value="14"/>
        <Setter Property="HorizontalContentAlignment" Value="Left"/>
        <Setter Property="VerticalContentAlignment" Value="Center"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="DataGridColumnHeader">
                    <Grid>
                        <Border Name="BackgroundBorder"
                              Background="{TemplateBinding Background}"
                              BorderBrush="{TemplateBinding BorderBrush}"
                              BorderThickness="{TemplateBinding BorderThickness}"/>
                        <ContentPresenter Margin="{TemplateBinding Padding}"
                                        VerticalAlignment="{TemplateBinding VerticalContentAlignment}"
                                        HorizontalAlignment="{TemplateBinding HorizontalContentAlignment}"/>
                        <Thumb Name="PART_LeftHeaderGripper"
                             HorizontalAlignment="Left"
                             Style="{StaticResource ColumnHeaderGripperStyle}"/>
                        <Thumb Name="PART_RightHeaderGripper"
                             HorizontalAlignment="Right"
                             Style="{StaticResource ColumnHeaderGripperStyle}"/>
                    </Grid>
                    <ControlTemplate.Triggers>
                        <Trigger Property="IsMouseOver" Value="True">
                            <Setter TargetName="BackgroundBorder" Property="Background" Value="#F0F0F0"/>
                        </Trigger>
                        <Trigger Property="IsPressed" Value="True">
                            <Setter TargetName="BackgroundBorder" Property="Background" Value="#E0E0E0"/>
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <!-- Column Header Gripper Style -->
    <Style x:Key="ColumnHeaderGripperStyle" TargetType="Thumb">
        <Setter Property="Width" Value="8"/>
        <Setter Property="Background" Value="Transparent"/>
        <Setter Property="Cursor" Value="SizeWE"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="Thumb">
                    <Border Background="{TemplateBinding Background}"/>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <!-- Modern DataGrid Row Style -->
    <Style x:Key="ModernDataGridRowStyle" TargetType="DataGridRow">
        <Setter Property="Background" Value="Transparent"/>
        <Setter Property="SnapsToDevicePixels" Value="true"/>
        <Setter Property="Validation.ErrorTemplate" Value="{x:Null}"/>
        <Setter Property="ValidationErrorTemplate">
            <Setter.Value>
                <ControlTemplate>
                    <TextBlock Foreground="Red" Margin="2,0,0,0" Text="!" VerticalAlignment="Center"/>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="DataGridRow">
                    <Border Name="DGR_Border"
                          Background="{TemplateBinding Background}"
                          BorderBrush="{TemplateBinding BorderBrush}"
                          BorderThickness="{TemplateBinding BorderThickness}"
                          SnapsToDevicePixels="True">
                        <Border.RenderTransform>
                            <TranslateTransform/>
                        </Border.RenderTransform>
                        <SelectiveScrollingGrid>
                            <SelectiveScrollingGrid.ColumnDefinitions>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="*"/>
                            </SelectiveScrollingGrid.ColumnDefinitions>
                            <SelectiveScrollingGrid.RowDefinitions>
                                <RowDefinition Height="*"/>
                                <RowDefinition Height="Auto"/>
                            </SelectiveScrollingGrid.RowDefinitions>
                            <DataGridCellsPresenter Grid.Column="1"
                                                  ItemsPanel="{TemplateBinding ItemsPanel}"
                                                  SnapsToDevicePixels="{TemplateBinding SnapsToDevicePixels}"/>
                            <DataGridDetailsPresenter Grid.Column="1"
                                                    Grid.Row="1"
                                                    SelectiveScrollingGrid.SelectiveScrollingOrientation="{Binding AreRowDetailsFrozen, ConverterParameter={x:Static SelectiveScrollingOrientation.Vertical}, Converter={x:Static DataGrid.RowDetailsScrollingConverter}, RelativeSource={RelativeSource AncestorType={x:Type DataGrid}}}"
                                                    Visibility="{TemplateBinding DetailsVisibility}"/>
                            <DataGridRowHeader Grid.RowSpan="2"
                                             SelectiveScrollingGrid.SelectiveScrollingOrientation="Vertical"
                                             Visibility="{Binding HeadersVisibility, ConverterParameter={x:Static DataGridHeadersVisibility.Row}, Converter={x:Static DataGrid.HeadersVisibilityConverter}, RelativeSource={RelativeSource AncestorType={x:Type DataGrid}}}"/>
                        </SelectiveScrollingGrid>
                    </Border>
                    <ControlTemplate.Triggers>
                        <Trigger Property="IsMouseOver" Value="True">
                            <Setter TargetName="DGR_Border" Property="Background" Value="#F8F9FA"/>
                            <Trigger.EnterActions>
                                <BeginStoryboard>
                                    <Storyboard>
                                        <DoubleAnimation Storyboard.TargetName="DGR_Border"
                                                       Storyboard.TargetProperty="(Border.RenderTransform).(TranslateTransform.X)"
                                                       To="4" Duration="0:0:0.2">
                                            <DoubleAnimation.EasingFunction>
                                                <CubicEase EasingMode="EaseOut"/>
                                            </DoubleAnimation.EasingFunction>
                                        </DoubleAnimation>
                                    </Storyboard>
                                </BeginStoryboard>
                            </Trigger.EnterActions>
                            <Trigger.ExitActions>
                                <BeginStoryboard>
                                    <Storyboard>
                                        <DoubleAnimation Storyboard.TargetName="DGR_Border"
                                                       Storyboard.TargetProperty="(Border.RenderTransform).(TranslateTransform.X)"
                                                       To="0" Duration="0:0:0.2">
                                            <DoubleAnimation.EasingFunction>
                                                <CubicEase EasingMode="EaseOut"/>
                                            </DoubleAnimation.EasingFunction>
                                        </DoubleAnimation>
                                    </Storyboard>
                                </BeginStoryboard>
                            </Trigger.ExitActions>
                        </Trigger>
                        <Trigger Property="IsSelected" Value="True">
                            <Setter TargetName="DGR_Border" Property="Background" Value="#E3F2FD"/>
                            <Setter TargetName="DGR_Border" Property="BorderBrush" Value="{StaticResource PrimaryColor}"/>
                            <Setter TargetName="DGR_Border" Property="BorderThickness" Value="0,0,4,0"/>
                        </Trigger>
                        <MultiTrigger>
                            <MultiTrigger.Conditions>
                                <Condition Property="IsSelected" Value="True"/>
                                <Condition Property="IsMouseOver" Value="True"/>
                            </MultiTrigger.Conditions>
                            <Setter TargetName="DGR_Border" Property="Background" Value="#BBDEFB"/>
                        </MultiTrigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <!-- Modern DataGrid Cell Style -->
    <Style x:Key="ModernDataGridCellStyle" TargetType="DataGridCell">
        <Setter Property="Background" Value="Transparent"/>
        <Setter Property="BorderBrush" Value="Transparent"/>
        <Setter Property="BorderThickness" Value="0"/>
        <Setter Property="Padding" Value="16,12"/>
        <Setter Property="VerticalContentAlignment" Value="Center"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="DataGridCell">
                    <Border Background="{TemplateBinding Background}"
                          BorderBrush="{TemplateBinding BorderBrush}"
                          BorderThickness="{TemplateBinding BorderThickness}"
                          SnapsToDevicePixels="True">
                        <ContentPresenter Margin="{TemplateBinding Padding}"
                                        VerticalAlignment="{TemplateBinding VerticalContentAlignment}"
                                        SnapsToDevicePixels="{TemplateBinding SnapsToDevicePixels}"/>
                    </Border>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
        <Style.Triggers>
            <Trigger Property="IsSelected" Value="True">
                <Setter Property="Background" Value="Transparent"/>
                <Setter Property="Foreground" Value="{StaticResource TextPrimaryColor}"/>
            </Trigger>
            <Trigger Property="IsFocused" Value="True">
                <Setter Property="Background" Value="Transparent"/>
            </Trigger>
        </Style.Triggers>
    </Style>

    <!-- Animated Loading DataGrid Style -->
    <Style x:Key="LoadingDataGridStyle" TargetType="DataGrid" BasedOn="{StaticResource ModernDataGridStyle}">
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="DataGrid">
                    <Grid>
                        <Border Background="{TemplateBinding Background}"
                              BorderBrush="{TemplateBinding BorderBrush}"
                              BorderThickness="{TemplateBinding BorderThickness}"
                              CornerRadius="12">
                            <ScrollViewer Name="DG_ScrollViewer" Focusable="false">
                                <ItemsPresenter SnapsToDevicePixels="{TemplateBinding SnapsToDevicePixels}"/>
                            </ScrollViewer>
                        </Border>
                        
                        <!-- Loading Overlay -->
                        <Border Name="LoadingOverlay"
                              Background="#80FFFFFF"
                              CornerRadius="12"
                              Visibility="Collapsed">
                            <StackPanel HorizontalAlignment="Center" VerticalAlignment="Center">
                                <Ellipse Name="LoadingSpinner"
                                       Width="40" Height="40"
                                       Stroke="{StaticResource PrimaryColor}"
                                       StrokeThickness="4"
                                       StrokeDashArray="20,20"
                                       RenderTransformOrigin="0.5,0.5">
                                    <Ellipse.RenderTransform>
                                        <RotateTransform/>
                                    </Ellipse.RenderTransform>
                                </Ellipse>
                                <TextBlock Text="جاري التحميل..."
                                         Foreground="{StaticResource TextSecondaryColor}"
                                         FontSize="14"
                                         Margin="0,16,0,0"
                                         HorizontalAlignment="Center"/>
                            </StackPanel>
                        </Border>
                    </Grid>
                    <ControlTemplate.Triggers>
                        <DataTrigger Binding="{Binding Items.Count, RelativeSource={RelativeSource Self}}" Value="0">
                            <Setter TargetName="LoadingOverlay" Property="Visibility" Value="Visible"/>
                            <DataTrigger.EnterActions>
                                <BeginStoryboard>
                                    <Storyboard RepeatBehavior="Forever">
                                        <DoubleAnimation Storyboard.TargetName="LoadingSpinner"
                                                       Storyboard.TargetProperty="(Ellipse.RenderTransform).(RotateTransform.Angle)"
                                                       From="0" To="360" Duration="0:0:1"/>
                                    </Storyboard>
                                </BeginStoryboard>
                            </DataTrigger.EnterActions>
                        </DataTrigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

</ResourceDictionary>
