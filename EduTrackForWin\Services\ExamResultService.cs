using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using EduTrackForWin.Data;
using EduTrackForWin.Models;
using Microsoft.EntityFrameworkCore;

namespace EduTrackForWin.Services
{
    public class ExamResultService
    {
        private readonly EduTrackDbContext _context;

        public ExamResultService()
        {
            _context = new EduTrackDbContext();
        }

        // Get all exam results
        public async Task<List<ExamResult>> GetAllExamResultsAsync()
        {
            return await _context.ExamResults
                .Include(er => er.Exam)
                .ThenInclude(e => e.Group)
                .Include(er => er.Student)
                .OrderByDescending(er => er.Exam.ExamDate)
                .ToListAsync();
        }

        // Get exam result by ID
        public async Task<ExamResult?> GetExamResultByIdAsync(int id)
        {
            return await _context.ExamResults
                .Include(er => er.Exam)
                .ThenInclude(e => e.Group)
                .Include(er => er.Student)
                .FirstOrDefaultAsync(er => er.Id == id);
        }

        // Get exam results by exam
        public async Task<List<ExamResult>> GetExamResultsByExamAsync(int examId)
        {
            return await _context.ExamResults
                .Include(er => er.Exam)
                .Include(er => er.Student)
                .Where(er => er.ExamId == examId)
                .OrderBy(er => er.Student.Name)
                .ToListAsync();
        }

        // Get exam results by student
        public async Task<List<ExamResult>> GetExamResultsByStudentAsync(int studentId)
        {
            return await _context.ExamResults
                .Include(er => er.Exam)
                .ThenInclude(e => e.Group)
                .Include(er => er.Student)
                .Where(er => er.StudentId == studentId)
                .OrderByDescending(er => er.Exam.ExamDate)
                .ToListAsync();
        }

        // Get exam results by group
        public async Task<List<ExamResult>> GetExamResultsByGroupAsync(int groupId)
        {
            return await _context.ExamResults
                .Include(er => er.Exam)
                .ThenInclude(e => e.Group)
                .Include(er => er.Student)
                .Where(er => er.Exam.GroupId == groupId)
                .OrderByDescending(er => er.Exam.ExamDate)
                .ThenBy(er => er.Student.Name)
                .ToListAsync();
        }

        // Update exam result
        public async Task<ExamResult> UpdateExamResultAsync(ExamResult examResult)
        {
            try
            {
                var existingResult = await _context.ExamResults.FindAsync(examResult.Id);
                if (existingResult == null)
                {
                    throw new Exception("نتيجة الاختبار غير موجودة");
                }

                existingResult.Marks = examResult.Marks;
                existingResult.BonusMarks = examResult.BonusMarks;
                existingResult.Notes = examResult.Notes;
                existingResult.Status = examResult.Status;
                existingResult.SubmittedAt = examResult.SubmittedAt;
                existingResult.IsAbsent = examResult.IsAbsent;
                existingResult.IsCheating = examResult.IsCheating;
                existingResult.TeacherComments = examResult.TeacherComments;
                existingResult.UpdatedAt = DateTime.Now;

                await _context.SaveChangesAsync();
                return existingResult;
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في تحديث نتيجة الاختبار: {ex.Message}");
            }
        }

        // Bulk update exam results
        public async Task<int> BulkUpdateExamResultsAsync(List<ExamResult> examResults)
        {
            try
            {
                int updatedCount = 0;

                foreach (var result in examResults)
                {
                    var existingResult = await _context.ExamResults.FindAsync(result.Id);
                    if (existingResult != null)
                    {
                        existingResult.Marks = result.Marks;
                        existingResult.BonusMarks = result.BonusMarks;
                        existingResult.Notes = result.Notes;
                        existingResult.Status = result.Status;
                        existingResult.SubmittedAt = result.SubmittedAt;
                        existingResult.IsAbsent = result.IsAbsent;
                        existingResult.IsCheating = result.IsCheating;
                        existingResult.TeacherComments = result.TeacherComments;
                        existingResult.UpdatedAt = DateTime.Now;
                        updatedCount++;
                    }
                }

                await _context.SaveChangesAsync();
                return updatedCount;
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في التحديث الجماعي للنتائج: {ex.Message}");
            }
        }

        // Get student performance summary
        public async Task<StudentPerformanceSummary> GetStudentPerformanceSummaryAsync(int studentId)
        {
            try
            {
                var results = await GetExamResultsByStudentAsync(studentId);
                var presentResults = results.Where(r => !r.IsAbsent).ToList();

                var summary = new StudentPerformanceSummary
                {
                    StudentId = studentId,
                    TotalExams = results.Count,
                    AttendedExams = presentResults.Count,
                    AbsentExams = results.Count(r => r.IsAbsent),
                    PassedExams = presentResults.Count(r => r.IsPassed),
                    FailedExams = presentResults.Count(r => !r.IsPassed),
                    AverageMarks = presentResults.Any() ? presentResults.Average(r => r.TotalMarks) : 0,
                    AveragePercentage = presentResults.Any() ? presentResults.Average(r => r.Percentage) : 0,
                    HighestMarks = presentResults.Any() ? presentResults.Max(r => r.TotalMarks) : 0,
                    LowestMarks = presentResults.Any() ? presentResults.Min(r => r.TotalMarks) : 0,
                    PassingRate = presentResults.Any() ? (presentResults.Count(r => r.IsPassed) * 100m / presentResults.Count) : 0
                };

                return summary;
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في حساب ملخص أداء الطالب: {ex.Message}");
            }
        }

        // Get grade distribution for an exam
        public async Task<Dictionary<string, int>> GetGradeDistributionAsync(int examId)
        {
            try
            {
                var results = await GetExamResultsByExamAsync(examId);
                var presentResults = results.Where(r => !r.IsAbsent).ToList();

                var distribution = new Dictionary<string, int>
                {
                    ["ممتاز+"] = 0,
                    ["ممتاز"] = 0,
                    ["جيد جداً+"] = 0,
                    ["جيد جداً"] = 0,
                    ["جيد+"] = 0,
                    ["جيد"] = 0,
                    ["مقبول+"] = 0,
                    ["مقبول"] = 0,
                    ["ضعيف"] = 0,
                    ["راسب"] = 0,
                    ["غائب"] = results.Count(r => r.IsAbsent),
                    ["غش"] = results.Count(r => r.IsCheating)
                };

                foreach (var result in presentResults.Where(r => !r.IsCheating))
                {
                    distribution[result.Grade]++;
                }

                return distribution;
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في حساب توزيع الدرجات: {ex.Message}");
            }
        }

        // Generate exam report
        public async Task<string> GenerateExamReportAsync(int examId)
        {
            try
            {
                var exam = await _context.Exams
                    .Include(e => e.Group)
                    .Include(e => e.ExamResults)
                    .ThenInclude(er => er.Student)
                    .FirstOrDefaultAsync(e => e.Id == examId);

                if (exam == null)
                {
                    throw new Exception("الاختبار غير موجود");
                }

                var statistics = await _context.ExamResults
                    .Where(er => er.ExamId == examId && !er.IsAbsent)
                    .GroupBy(er => 1)
                    .Select(g => new
                    {
                        Count = g.Count(),
                        Average = g.Average(er => er.TotalMarks),
                        Max = g.Max(er => er.TotalMarks),
                        Min = g.Min(er => er.TotalMarks),
                        Passed = g.Count(er => er.TotalMarks >= exam.PassingMarks)
                    })
                    .FirstOrDefaultAsync();

                var report = $"تقرير الاختبار\n";
                report += $"=============\n\n";
                report += $"عنوان الاختبار: {exam.Title}\n";
                report += $"المجموعة: {exam.Group.Name}\n";
                report += $"تاريخ الاختبار: {exam.ExamDate:yyyy/MM/dd}\n";
                report += $"وقت الاختبار: {exam.StartTime} - {exam.EndTime}\n";
                report += $"الدرجة الكاملة: {exam.TotalMarks}\n";
                report += $"درجة النجاح: {exam.PassingMarks}\n\n";

                if (statistics != null)
                {
                    report += $"الإحصائيات:\n";
                    report += $"----------\n";
                    report += $"عدد الطلاب الحاضرين: {statistics.Count}\n";
                    report += $"عدد الطلاب الناجحين: {statistics.Passed}\n";
                    report += $"عدد الطلاب الراسبين: {statistics.Count - statistics.Passed}\n";
                    report += $"نسبة النجاح: {(statistics.Passed * 100.0 / statistics.Count):F1}%\n";
                    report += $"متوسط الدرجات: {statistics.Average:F2}\n";
                    report += $"أعلى درجة: {statistics.Max:F2}\n";
                    report += $"أقل درجة: {statistics.Min:F2}\n\n";
                }

                report += $"تفاصيل النتائج:\n";
                report += $"---------------\n";
                foreach (var result in exam.ExamResults.OrderBy(r => r.Student.Name))
                {
                    if (result.IsAbsent)
                    {
                        report += $"• {result.Student.Name}: غائب\n";
                    }
                    else if (result.IsCheating)
                    {
                        report += $"• {result.Student.Name}: غش\n";
                    }
                    else
                    {
                        report += $"• {result.Student.Name}: {result.TotalMarks:F2}/{exam.TotalMarks} ({result.Percentage:F1}%) - {result.Grade}\n";
                    }
                }

                return report;
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في إنشاء تقرير الاختبار: {ex.Message}");
            }
        }

        public void Dispose()
        {
            _context?.Dispose();
        }
    }

    // Helper class for student performance summary
    public class StudentPerformanceSummary
    {
        public int StudentId { get; set; }
        public int TotalExams { get; set; }
        public int AttendedExams { get; set; }
        public int AbsentExams { get; set; }
        public int PassedExams { get; set; }
        public int FailedExams { get; set; }
        public decimal AverageMarks { get; set; }
        public decimal AveragePercentage { get; set; }
        public decimal HighestMarks { get; set; }
        public decimal LowestMarks { get; set; }
        public decimal PassingRate { get; set; }
    }
}
