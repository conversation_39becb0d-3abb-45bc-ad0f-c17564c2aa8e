using Microsoft.EntityFrameworkCore;
using EduTrackForWin.Data;
using EduTrackForWin.Models;

namespace EduTrackForWin.Services
{
    public class ScheduleService
    {
        private readonly EduTrackDbContext _context;

        public ScheduleService()
        {
            _context = new EduTrackDbContext();
        }

        public async Task<List<Schedule>> GetAllSchedulesAsync()
        {
            return await _context.Schedules
                .Include(s => s.Group)
                .Include(s => s.Teacher)
                .OrderByDescending(s => s.StartDate)
                .ToListAsync();
        }

        public async Task<Schedule?> GetScheduleByIdAsync(int id)
        {
            return await _context.Schedules
                .Include(s => s.Group)
                .Include(s => s.Teacher)
                .Include(s => s.Sessions)
                .FirstOrDefaultAsync(s => s.Id == id);
        }

        public async Task<List<Schedule>> GetActiveSchedulesAsync()
        {
            var today = DateTime.Today;
            return await _context.Schedules
                .Include(s => s.Group)
                .Include(s => s.Teacher)
                .Where(s => s.EndDate >= today)
                .OrderBy(s => s.StartDate)
                .ToListAsync();
        }

        public async Task<List<Schedule>> GetSchedulesByGroupAsync(int groupId)
        {
            return await _context.Schedules
                .Include(s => s.Teacher)
                .Where(s => s.GroupId == groupId)
                .OrderByDescending(s => s.StartDate)
                .ToListAsync();
        }

        public async Task<Schedule> AddScheduleAsync(Schedule schedule)
        {
            schedule.CreatedAt = DateTime.Now;
            _context.Schedules.Add(schedule);
            await _context.SaveChangesAsync();
            
            // إنشاء الحصص بناءً على الجدول الزمني
            await GenerateSessionsFromScheduleAsync(schedule);
            
            return schedule;
        }

        public async Task<Schedule> UpdateScheduleAsync(Schedule schedule)
        {
            schedule.UpdatedAt = DateTime.Now;
            _context.Schedules.Update(schedule);
            await _context.SaveChangesAsync();
            return schedule;
        }

        public async Task<bool> DeleteScheduleAsync(int id)
        {
            var schedule = await _context.Schedules
                .Include(s => s.Sessions)
                .FirstOrDefaultAsync(s => s.Id == id);
            
            if (schedule == null) return false;

            // حذف الحصص المرتبطة بالجدول الزمني
            if (schedule.Sessions.Any())
            {
                var futureSessions = schedule.Sessions.Where(s => s.Date >= DateTime.Today).ToList();
                _context.Sessions.RemoveRange(futureSessions);
            }

            _context.Schedules.Remove(schedule);
            await _context.SaveChangesAsync();
            return true;
        }

        public async Task<bool> RegenerateSessionsAsync(int scheduleId)
        {
            var schedule = await GetScheduleByIdAsync(scheduleId);
            if (schedule == null) return false;

            // حذف الحصص المستقبلية المرتبطة بالجدول
            var futureSessions = await _context.Sessions
                .Where(s => s.ScheduleId == scheduleId && s.Date >= DateTime.Today)
                .ToListAsync();
            
            _context.Sessions.RemoveRange(futureSessions);
            await _context.SaveChangesAsync();

            // إعادة إنشاء الحصص
            await GenerateSessionsFromScheduleAsync(schedule);
            
            return true;
        }

        private async Task GenerateSessionsFromScheduleAsync(Schedule schedule)
        {
            var sessions = new List<Session>();
            var currentDate = schedule.StartDate;
            
            while (currentDate <= schedule.EndDate)
            {
                if ((currentDate.DayOfWeek == DayOfWeek.Sunday && schedule.Sunday) ||
                    (currentDate.DayOfWeek == DayOfWeek.Monday && schedule.Monday) ||
                    (currentDate.DayOfWeek == DayOfWeek.Tuesday && schedule.Tuesday) ||
                    (currentDate.DayOfWeek == DayOfWeek.Wednesday && schedule.Wednesday) ||
                    (currentDate.DayOfWeek == DayOfWeek.Thursday && schedule.Thursday) ||
                    (currentDate.DayOfWeek == DayOfWeek.Friday && schedule.Friday) ||
                    (currentDate.DayOfWeek == DayOfWeek.Saturday && schedule.Saturday))
                {
                    var session = new Session
                    {
                        Date = currentDate,
                        StartTime = schedule.StartTime,
                        EndTime = schedule.EndTime,
                        Room = schedule.Room,
                        Topic = schedule.Topic,
                        Notes = schedule.Notes,
                        GroupId = schedule.GroupId,
                        TeacherId = schedule.TeacherId,
                        ScheduleId = schedule.Id,
                        IsCompleted = false,
                        TeacherPresent = true,
                        CreatedAt = DateTime.Now
                    };
                    
                    sessions.Add(session);
                }
                
                currentDate = currentDate.AddDays(1);
            }
            
            if (sessions.Any())
            {
                await _context.Sessions.AddRangeAsync(sessions);
                await _context.SaveChangesAsync();
            }
        }

        public void Dispose()
        {
            _context?.Dispose();
        }
    }
}