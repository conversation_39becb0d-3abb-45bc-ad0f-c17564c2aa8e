﻿#pragma checksum "..\..\..\..\Views\GroupDetailsWindow.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "693DF5B093639EF10A77F0E7DA3C6B7679191102"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using EduTrackForWin.Views;
using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace EduTrackForWin.Views {
    
    
    /// <summary>
    /// GroupDetailsWindow
    /// </summary>
    public partial class GroupDetailsWindow : System.Windows.Window, System.Windows.Markup.IComponentConnector {
        
        
        #line 53 "..\..\..\..\Views\GroupDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TxtGroupName;
        
        #line default
        #line hidden
        
        
        #line 74 "..\..\..\..\Views\GroupDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TxtName;
        
        #line default
        #line hidden
        
        
        #line 79 "..\..\..\..\Views\GroupDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TxtSubject;
        
        #line default
        #line hidden
        
        
        #line 84 "..\..\..\..\Views\GroupDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TxtGrade;
        
        #line default
        #line hidden
        
        
        #line 89 "..\..\..\..\Views\GroupDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TxtRoom;
        
        #line default
        #line hidden
        
        
        #line 96 "..\..\..\..\Views\GroupDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TxtTeacher;
        
        #line default
        #line hidden
        
        
        #line 101 "..\..\..\..\Views\GroupDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TxtMonthlyFee;
        
        #line default
        #line hidden
        
        
        #line 106 "..\..\..\..\Views\GroupDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TxtStudentCount;
        
        #line default
        #line hidden
        
        
        #line 111 "..\..\..\..\Views\GroupDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TxtStatus;
        
        #line default
        #line hidden
        
        
        #line 118 "..\..\..\..\Views\GroupDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TxtDescription;
        
        #line default
        #line hidden
        
        
        #line 137 "..\..\..\..\Views\GroupDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TxtExpectedRevenue;
        
        #line default
        #line hidden
        
        
        #line 143 "..\..\..\..\Views\GroupDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TxtTotalPayments;
        
        #line default
        #line hidden
        
        
        #line 149 "..\..\..\..\Views\GroupDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TxtOutstandingAmount;
        
        #line default
        #line hidden
        
        
        #line 167 "..\..\..\..\Views\GroupDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BtnManageStudents;
        
        #line default
        #line hidden
        
        
        #line 184 "..\..\..\..\Views\GroupDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DataGrid DgStudents;
        
        #line default
        #line hidden
        
        
        #line 220 "..\..\..\..\Views\GroupDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BtnAddSession;
        
        #line default
        #line hidden
        
        
        #line 237 "..\..\..\..\Views\GroupDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DataGrid DgSessions;
        
        #line default
        #line hidden
        
        
        #line 285 "..\..\..\..\Views\GroupDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BtnEdit;
        
        #line default
        #line hidden
        
        
        #line 301 "..\..\..\..\Views\GroupDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BtnViewSchedule;
        
        #line default
        #line hidden
        
        
        #line 317 "..\..\..\..\Views\GroupDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BtnFinancialReport;
        
        #line default
        #line hidden
        
        
        #line 333 "..\..\..\..\Views\GroupDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BtnClose;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/EduTrackForWin;component/views/groupdetailswindow.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\Views\GroupDetailsWindow.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.TxtGroupName = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 2:
            this.TxtName = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 3:
            this.TxtSubject = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 4:
            this.TxtGrade = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 5:
            this.TxtRoom = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 6:
            this.TxtTeacher = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 7:
            this.TxtMonthlyFee = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 8:
            this.TxtStudentCount = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 9:
            this.TxtStatus = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 10:
            this.TxtDescription = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 11:
            this.TxtExpectedRevenue = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 12:
            this.TxtTotalPayments = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 13:
            this.TxtOutstandingAmount = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 14:
            this.BtnManageStudents = ((System.Windows.Controls.Button)(target));
            
            #line 170 "..\..\..\..\Views\GroupDetailsWindow.xaml"
            this.BtnManageStudents.Click += new System.Windows.RoutedEventHandler(this.BtnManageStudents_Click);
            
            #line default
            #line hidden
            return;
            case 15:
            this.DgStudents = ((System.Windows.Controls.DataGrid)(target));
            return;
            case 16:
            this.BtnAddSession = ((System.Windows.Controls.Button)(target));
            
            #line 223 "..\..\..\..\Views\GroupDetailsWindow.xaml"
            this.BtnAddSession.Click += new System.Windows.RoutedEventHandler(this.BtnAddSession_Click);
            
            #line default
            #line hidden
            return;
            case 17:
            this.DgSessions = ((System.Windows.Controls.DataGrid)(target));
            return;
            case 18:
            this.BtnEdit = ((System.Windows.Controls.Button)(target));
            
            #line 288 "..\..\..\..\Views\GroupDetailsWindow.xaml"
            this.BtnEdit.Click += new System.Windows.RoutedEventHandler(this.BtnEdit_Click);
            
            #line default
            #line hidden
            return;
            case 19:
            this.BtnViewSchedule = ((System.Windows.Controls.Button)(target));
            
            #line 304 "..\..\..\..\Views\GroupDetailsWindow.xaml"
            this.BtnViewSchedule.Click += new System.Windows.RoutedEventHandler(this.BtnViewSchedule_Click);
            
            #line default
            #line hidden
            return;
            case 20:
            this.BtnFinancialReport = ((System.Windows.Controls.Button)(target));
            
            #line 320 "..\..\..\..\Views\GroupDetailsWindow.xaml"
            this.BtnFinancialReport.Click += new System.Windows.RoutedEventHandler(this.BtnFinancialReport_Click);
            
            #line default
            #line hidden
            return;
            case 21:
            this.BtnClose = ((System.Windows.Controls.Button)(target));
            
            #line 336 "..\..\..\..\Views\GroupDetailsWindow.xaml"
            this.BtnClose.Click += new System.Windows.RoutedEventHandler(this.BtnClose_Click);
            
            #line default
            #line hidden
            return;
            }
            this._contentLoaded = true;
        }
    }
}

