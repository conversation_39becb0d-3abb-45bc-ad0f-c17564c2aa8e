# دليل التثبيت والإعداد - EduTrack for Windows

## متطلبات النظام

### الحد الأدنى
- **نظام التشغيل**: Windows 10 (64-bit)
- **المعالج**: Intel Core i3 أو AMD Ryzen 3
- **الذاكرة**: 4 GB RAM
- **التخزين**: 1 GB مساحة فارغة
- **الشاشة**: 1366x768 دقة

### الموصى به
- **نظام التشغيل**: Windows 11 (64-bit)
- **المعالج**: Intel Core i5 أو AMD Ryzen 5
- **الذاكرة**: 8 GB RAM
- **التخزين**: 2 GB مساحة فارغة
- **الشاشة**: 1920x1080 دقة أو أعلى

## خطوات التثبيت

### 1. تثبيت .NET 8 Runtime

#### الطريقة الأولى: التحميل المباشر
1. اذهب إلى [موقع Microsoft الرسمي](https://dotnet.microsoft.com/download/dotnet/8.0)
2. اختر "Download .NET 8.0 Runtime" للـ Windows
3. حمل النسخة x64
4. شغل الملف المحمل واتبع التعليمات

#### الطريقة الثانية: استخدام winget
```powershell
winget install Microsoft.DotNet.Runtime.8
```

### 2. تحميل التطبيق

#### من GitHub
```bash
git clone https://github.com/your-username/EduTrackForWin.git
cd EduTrackForWin
```

#### أو تحميل ZIP
1. اذهب إلى صفحة المشروع على GitHub
2. اضغط "Code" ثم "Download ZIP"
3. استخرج الملفات في مجلد مناسب

### 3. بناء وتشغيل التطبيق

#### باستخدام Visual Studio
1. افتح ملف `EduTrackForWin.sln`
2. اضغط F5 أو "Start Debugging"

#### باستخدام Command Line
```bash
# الانتقال لمجلد المشروع
cd EduTrackForWin

# استعادة الحزم
dotnet restore

# بناء المشروع
dotnet build

# تشغيل التطبيق
dotnet run
```

## الإعداد الأولي

### 1. التشغيل الأول
عند تشغيل التطبيق لأول مرة:
- سيتم إنشاء قاعدة البيانات تلقائياً
- ستظهر لوحة التحكم الرئيسية
- جميع الإحصائيات ستكون فارغة

### 2. إعداد معلومات المركز
1. اذهب إلى "الإعدادات" من القائمة الجانبية
2. املأ معلومات المركز:
   - اسم المركز
   - العنوان
   - أرقام الهاتف
   - البريد الإلكتروني

### 3. الإعدادات المالية
في نفس صفحة الإعدادات:
- اختر العملة المستخدمة
- حدد نسبة الضريبة
- اضبط رسوم التأخير
- حدد خصم الدفع المبكر

### 4. الإعدادات الأكاديمية
- حدد مدة الحصة الافتراضية
- اضبط الحد الأقصى لطلاب المجموعة
- اختر أيام العمل
- حدد ساعات العمل

## البدء في الاستخدام

### 1. إضافة المعلمين
1. اذهب إلى "المعلمين"
2. اضغط "إضافة معلم جديد"
3. املأ البيانات المطلوبة:
   - الاسم الكامل
   - التخصص
   - بيانات الاتصال
   - نوع الراتب (ثابت أو بالحصة)

### 2. إضافة الطلاب
1. اذهب إلى "الطلاب"
2. اضغط "إضافة طالب جديد"
3. املأ البيانات:
   - الاسم الكامل
   - الصف الدراسي
   - بيانات الاتصال
   - بيانات ولي الأمر

### 3. إنشاء المجموعات
1. اذهب إلى "المجموعات"
2. اضغط "إضافة مجموعة جديدة"
3. حدد:
   - اسم المجموعة
   - المادة والصف
   - المعلم المسؤول
   - الرسوم الشهرية

### 4. إضافة الطلاب للمجموعات
1. من صفحة المجموعات، اضغط "إدارة الطلاب"
2. اختر الطلاب من القائمة المتاحة
3. اضغط "إضافة" لكل طالب

### 5. جدولة الحصص
1. اذهب إلى "الحصص"
2. اضغط "إضافة حصة جديدة"
3. حدد:
   - المجموعة والمعلم
   - التاريخ والوقت
   - موضوع الحصة

## النسخ الاحتياطي

### موقع قاعدة البيانات
قاعدة البيانات محفوظة في:
```
%USERPROFILE%\AppData\Local\EduTrack\edutrack.db
```

### عمل نسخة احتياطية
1. أغلق التطبيق
2. انسخ ملف `edutrack.db`
3. احفظه في مكان آمن

### استعادة النسخة الاحتياطية
1. أغلق التطبيق
2. استبدل ملف `edutrack.db` بالنسخة الاحتياطية
3. شغل التطبيق

## حل المشاكل الشائعة

### التطبيق لا يبدأ
**المشكلة**: رسالة خطأ عند التشغيل
**الحل**:
1. تأكد من تثبيت .NET 8 Runtime
2. شغل Command Prompt كمدير وجرب:
   ```bash
   dotnet --version
   ```

### قاعدة البيانات تالفة
**المشكلة**: خطأ في قراءة البيانات
**الحل**:
1. أغلق التطبيق
2. احذف ملف `edutrack.db`
3. شغل التطبيق (سينشئ قاعدة بيانات جديدة)
4. استورد النسخة الاحتياطية إن وجدت

### بطء في الأداء
**المشكلة**: التطبيق بطيء
**الحل**:
1. تأكد من توفر ذاكرة كافية
2. أغلق البرامج غير الضرورية
3. أعد تشغيل التطبيق

### مشاكل العرض
**المشكلة**: واجهة المستخدم غير واضحة
**الحل**:
1. تأكد من دقة الشاشة (1366x768 أو أعلى)
2. اضبط إعدادات العرض في Windows
3. جرب تشغيل التطبيق بدقة مختلفة

## التحديثات

### فحص التحديثات
- تفقد صفحة GitHub للإصدارات الجديدة
- اقرأ ملاحظات الإصدار قبل التحديث

### تطبيق التحديث
1. اعمل نسخة احتياطية من قاعدة البيانات
2. حمل الإصدار الجديد
3. استبدل ملفات التطبيق
4. شغل التطبيق وتأكد من عمله

## الدعم الفني

### قبل طلب المساعدة
1. تأكد من قراءة هذا الدليل
2. جرب الحلول المقترحة
3. اجمع معلومات عن المشكلة:
   - نسخة Windows
   - نسخة .NET
   - رسالة الخطأ (إن وجدت)

### طرق التواصل
- **GitHub Issues**: للمشاكل التقنية
- **البريد الإلكتروني**: <EMAIL>
- **الوثائق**: راجع ملف README.md

---

**نتمنى لك تجربة ممتعة مع EduTrack! 🎓**
