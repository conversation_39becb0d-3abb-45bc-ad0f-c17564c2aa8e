using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Windows;
using EduTrackForWin.Models;
using EduTrackForWin.Services;

namespace EduTrackForWin.Views
{
    public partial class TakeAttendanceWindow : Window
    {
        private readonly Session _session;
        private readonly SessionService _sessionService;
        private ObservableCollection<AttendanceViewModel> _attendanceList;

        public TakeAttendanceWindow(Session session)
        {
            InitializeComponent();
            _session = session;
            _sessionService = new SessionService();
            _attendanceList = new ObservableCollection<AttendanceViewModel>();
            
            DgAttendance.ItemsSource = _attendanceList;
            LoadSessionData();
        }

        private async void LoadSessionData()
        {
            // Display session information
            TxtSessionInfo.Text = $"تسجيل الحضور - {_session.Group?.Name}";
            TxtSessionDetails.Text = $"{_session.Date:yyyy/MM/dd} - {_session.SessionTime} - {_session.Teacher?.Name}";

            // Load students and existing attendance
            if (_session.Group?.Students != null)
            {
                var existingAttendance = await _sessionService.GetSessionAttendanceAsync(_session.Id);
                
                foreach (var student in _session.Group.Students.OrderBy(s => s.Name))
                {
                    var attendance = existingAttendance.FirstOrDefault(a => a.StudentId == student.Id);
                    
                    var attendanceVM = new AttendanceViewModel
                    {
                        StudentId = student.Id,
                        StudentName = student.Name,
                        IsPresent = attendance?.IsPresent ?? false,
                        Notes = attendance?.Notes ?? ""
                    };
                    
                    attendanceVM.PropertyChanged += AttendanceVM_PropertyChanged;
                    _attendanceList.Add(attendanceVM);
                }
            }

            UpdateSummary();
        }

        private void AttendanceVM_PropertyChanged(object? sender, PropertyChangedEventArgs e)
        {
            if (e.PropertyName == nameof(AttendanceViewModel.IsPresent))
            {
                UpdateSummary();
            }
        }

        private void UpdateSummary()
        {
            var totalStudents = _attendanceList.Count;
            var presentCount = _attendanceList.Count(a => a.IsPresent);
            var attendanceRate = totalStudents > 0 ? (double)presentCount / totalStudents * 100 : 0;

            TxtTotalStudents.Text = totalStudents.ToString();
            TxtPresentCount.Text = presentCount.ToString();
            TxtAttendanceRate.Text = $"{attendanceRate:F1}%";
        }

        private void BtnMarkAllPresent_Click(object sender, RoutedEventArgs e)
        {
            foreach (var attendance in _attendanceList)
            {
                attendance.IsPresent = true;
            }
        }

        private void BtnMarkAllAbsent_Click(object sender, RoutedEventArgs e)
        {
            foreach (var attendance in _attendanceList)
            {
                attendance.IsPresent = false;
            }
        }

        private void BtnClearAll_Click(object sender, RoutedEventArgs e)
        {
            foreach (var attendance in _attendanceList)
            {
                attendance.IsPresent = false;
                attendance.Notes = "";
            }
        }

        private async void BtnSave_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var attendanceRecords = _attendanceList.Select(a => new Attendance
                {
                    StudentId = a.StudentId,
                    SessionId = _session.Id,
                    IsPresent = a.IsPresent,
                    Notes = a.Notes,
                    Date = _session.Date
                }).ToList();

                await _sessionService.TakeAttendanceAsync(_session.Id, attendanceRecords);
                
                MessageBox.Show("تم حفظ الحضور بنجاح", "نجح", 
                              MessageBoxButton.OK, MessageBoxImage.Information);
                
                DialogResult = true;
                Close();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في حفظ الحضور: {ex.Message}", "خطأ", 
                              MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void BtnCancel_Click(object sender, RoutedEventArgs e)
        {
            DialogResult = false;
            Close();
        }

        protected override void OnClosed(EventArgs e)
        {
            _sessionService?.Dispose();
            base.OnClosed(e);
        }
    }

    public class AttendanceViewModel : INotifyPropertyChanged
    {
        private bool _isPresent;
        private string _notes = "";

        public int StudentId { get; set; }
        public string StudentName { get; set; } = "";

        public bool IsPresent
        {
            get => _isPresent;
            set
            {
                _isPresent = value;
                OnPropertyChanged();
                OnPropertyChanged(nameof(IsAbsent));
            }
        }

        public bool IsAbsent
        {
            get => !_isPresent;
            set
            {
                _isPresent = !value;
                OnPropertyChanged();
                OnPropertyChanged(nameof(IsPresent));
            }
        }

        public string Notes
        {
            get => _notes;
            set
            {
                _notes = value;
                OnPropertyChanged();
            }
        }

        public event PropertyChangedEventHandler? PropertyChanged;

        protected virtual void OnPropertyChanged([System.Runtime.CompilerServices.CallerMemberName] string? propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
    }
}
