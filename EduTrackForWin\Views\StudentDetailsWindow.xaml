<Window x:Class="EduTrackForWin.Views.StudentDetailsWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:EduTrackForWin.Views"
        mc:Ignorable="d"
        Title="تفاصيل الطالب" Height="700" Width="800"
        WindowStartupLocation="CenterOwner">

    <Window.Resources>
        <!-- Card Style -->
        <Style x:Key="CardStyle" TargetType="Border">
            <Setter Property="Background" Value="White"/>
            <Setter Property="CornerRadius" Value="8"/>
            <Setter Property="Padding" Value="20"/>
            <Setter Property="Margin" Value="10"/>
            <Setter Property="Effect">
                <Setter.Value>
                    <DropShadowEffect Color="Gray" Direction="270" ShadowDepth="2" Opacity="0.3" BlurRadius="5"/>
                </Setter.Value>
            </Setter>
        </Style>

        <!-- Info Label Style -->
        <Style x:Key="InfoLabelStyle" TargetType="TextBlock">
            <Setter Property="FontWeight" Value="SemiBold"/>
            <Setter Property="Foreground" Value="#424242"/>
            <Setter Property="Margin" Value="0,5,10,5"/>
        </Style>

        <!-- Info Value Style -->
        <Style x:Key="InfoValueStyle" TargetType="TextBlock">
            <Setter Property="Foreground" Value="#212121"/>
            <Setter Property="Margin" Value="0,5"/>
        </Style>

        <!-- Section Header Style -->
        <Style x:Key="SectionHeaderStyle" TargetType="TextBlock">
            <Setter Property="FontSize" Value="18"/>
            <Setter Property="FontWeight" Value="Bold"/>
            <Setter Property="Foreground" Value="#2196F3"/>
            <Setter Property="Margin" Value="0,0,0,15"/>
        </Style>
    </Window.Resources>

    <Grid Background="#F5F5F5">
        <ScrollViewer VerticalScrollBarVisibility="Auto" Margin="20">
            <StackPanel>
                <!-- Header -->
                <Border Style="{StaticResource CardStyle}" Background="#E3F2FD">
                    <StackPanel>
                        <TextBlock Name="TxtStudentName" Text="اسم الطالب" FontSize="24" FontWeight="Bold" 
                                 HorizontalAlignment="Center" Foreground="#1976D2"/>
                        <TextBlock Text="تفاصيل الطالب الكاملة" FontSize="14" 
                                 HorizontalAlignment="Center" Foreground="#424242" Margin="0,5,0,0"/>
                    </StackPanel>
                </Border>

                <!-- Basic Information -->
                <Border Style="{StaticResource CardStyle}">
                    <StackPanel>
                        <TextBlock Text="المعلومات الأساسية" Style="{StaticResource SectionHeaderStyle}"/>
                        
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            
                            <StackPanel Grid.Column="0">
                                <StackPanel Orientation="Horizontal">
                                    <TextBlock Text="الاسم:" Style="{StaticResource InfoLabelStyle}"/>
                                    <TextBlock Name="TxtName" Style="{StaticResource InfoValueStyle}"/>
                                </StackPanel>
                                
                                <StackPanel Orientation="Horizontal">
                                    <TextBlock Text="الصف:" Style="{StaticResource InfoLabelStyle}"/>
                                    <TextBlock Name="TxtGrade" Style="{StaticResource InfoValueStyle}"/>
                                </StackPanel>
                                
                                <StackPanel Orientation="Horizontal">
                                    <TextBlock Text="رقم الهاتف:" Style="{StaticResource InfoLabelStyle}"/>
                                    <TextBlock Name="TxtPhone" Style="{StaticResource InfoValueStyle}"/>
                                </StackPanel>
                            </StackPanel>
                            
                            <StackPanel Grid.Column="1">
                                <StackPanel Orientation="Horizontal">
                                    <TextBlock Text="المجموعة:" Style="{StaticResource InfoLabelStyle}"/>
                                    <TextBlock Name="TxtGroup" Style="{StaticResource InfoValueStyle}"/>
                                </StackPanel>
                                
                                <StackPanel Orientation="Horizontal">
                                    <TextBlock Text="المعلم:" Style="{StaticResource InfoLabelStyle}"/>
                                    <TextBlock Name="TxtTeacher" Style="{StaticResource InfoValueStyle}"/>
                                </StackPanel>
                                
                                <StackPanel Orientation="Horizontal">
                                    <TextBlock Text="تاريخ التسجيل:" Style="{StaticResource InfoLabelStyle}"/>
                                    <TextBlock Name="TxtCreatedAt" Style="{StaticResource InfoValueStyle}"/>
                                </StackPanel>
                            </StackPanel>
                        </Grid>
                        
                        <StackPanel Margin="0,10,0,0">
                            <TextBlock Text="العنوان:" Style="{StaticResource InfoLabelStyle}"/>
                            <TextBlock Name="TxtAddress" Style="{StaticResource InfoValueStyle}" TextWrapping="Wrap"/>
                        </StackPanel>
                        
                        <StackPanel Margin="0,10,0,0">
                            <TextBlock Text="ملاحظات:" Style="{StaticResource InfoLabelStyle}"/>
                            <TextBlock Name="TxtNotes" Style="{StaticResource InfoValueStyle}" TextWrapping="Wrap"/>
                        </StackPanel>
                    </StackPanel>
                </Border>

                <!-- Financial Information -->
                <Border Style="{StaticResource CardStyle}">
                    <StackPanel>
                        <TextBlock Text="المعلومات المالية" Style="{StaticResource SectionHeaderStyle}"/>
                        
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            
                            <StackPanel Grid.Column="0">
                                <TextBlock Text="إجمالي المدفوع" Style="{StaticResource InfoLabelStyle}"/>
                                <TextBlock Name="TxtTotalPaid" Style="{StaticResource InfoValueStyle}" 
                                         FontSize="18" FontWeight="Bold" Foreground="#4CAF50"/>
                            </StackPanel>
                            
                            <StackPanel Grid.Column="1">
                                <TextBlock Text="المبلغ المستحق" Style="{StaticResource InfoLabelStyle}"/>
                                <TextBlock Name="TxtTotalDue" Style="{StaticResource InfoValueStyle}" 
                                         FontSize="18" FontWeight="Bold" Foreground="#F44336"/>
                            </StackPanel>
                            
                            <StackPanel Grid.Column="2">
                                <TextBlock Text="حالة الدفع" Style="{StaticResource InfoLabelStyle}"/>
                                <TextBlock Name="TxtPaymentStatus" Style="{StaticResource InfoValueStyle}" 
                                         FontSize="18" FontWeight="Bold"/>
                            </StackPanel>
                        </Grid>
                    </StackPanel>
                </Border>

                <!-- Recent Payments -->
                <Border Style="{StaticResource CardStyle}">
                    <StackPanel>
                        <TextBlock Text="آخر المدفوعات" Style="{StaticResource SectionHeaderStyle}"/>
                        
                        <DataGrid Name="DgPayments" 
                                AutoGenerateColumns="False" 
                                CanUserAddRows="False" 
                                CanUserDeleteRows="False"
                                IsReadOnly="True"
                                GridLinesVisibility="Horizontal"
                                HeadersVisibility="Column"
                                BorderThickness="0"
                                Background="Transparent"
                                RowBackground="White"
                                AlternatingRowBackground="#F5F5F5"
                                Height="150">
                            
                            <DataGrid.Columns>
                                <DataGridTextColumn Header="التاريخ" Binding="{Binding PaymentDate, StringFormat='{}{0:yyyy/MM/dd}'}" Width="100"/>
                                <DataGridTextColumn Header="المبلغ" Binding="{Binding Amount, StringFormat='{}{0:C}'}" Width="100"/>
                                <DataGridTextColumn Header="النوع" Binding="{Binding TypeName}" Width="100"/>
                                <DataGridTextColumn Header="الوصف" Binding="{Binding Description}" Width="*"/>
                            </DataGrid.Columns>
                        </DataGrid>
                    </StackPanel>
                </Border>

                <!-- Attendance Summary -->
                <Border Style="{StaticResource CardStyle}">
                    <StackPanel>
                        <TextBlock Text="ملخص الحضور" Style="{StaticResource SectionHeaderStyle}"/>
                        
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            
                            <StackPanel Grid.Column="0">
                                <TextBlock Text="إجمالي الحصص" Style="{StaticResource InfoLabelStyle}"/>
                                <TextBlock Name="TxtTotalSessions" Style="{StaticResource InfoValueStyle}" 
                                         FontSize="18" FontWeight="Bold" Foreground="#2196F3"/>
                            </StackPanel>
                            
                            <StackPanel Grid.Column="1">
                                <TextBlock Text="الحضور" Style="{StaticResource InfoLabelStyle}"/>
                                <TextBlock Name="TxtPresentSessions" Style="{StaticResource InfoValueStyle}" 
                                         FontSize="18" FontWeight="Bold" Foreground="#4CAF50"/>
                            </StackPanel>
                            
                            <StackPanel Grid.Column="2">
                                <TextBlock Text="نسبة الحضور" Style="{StaticResource InfoLabelStyle}"/>
                                <TextBlock Name="TxtAttendanceRate" Style="{StaticResource InfoValueStyle}" 
                                         FontSize="18" FontWeight="Bold" Foreground="#FF9800"/>
                            </StackPanel>
                        </Grid>
                    </StackPanel>
                </Border>

                <!-- Action Buttons -->
                <Border Style="{StaticResource CardStyle}">
                    <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                        <Button Name="BtnEdit" Content="تعديل البيانات"
                              Background="#2196F3" Foreground="White"
                              Padding="20,10" Margin="10" BorderThickness="0"
                              Click="BtnEdit_Click">
                            <Button.Template>
                                <ControlTemplate TargetType="Button">
                                    <Border Background="{TemplateBinding Background}"
                                          CornerRadius="5">
                                        <ContentPresenter HorizontalAlignment="Center"
                                                        VerticalAlignment="Center"
                                                        Margin="{TemplateBinding Padding}"/>
                                    </Border>
                                </ControlTemplate>
                            </Button.Template>
                        </Button>

                        <Button Name="BtnAddPayment" Content="إضافة دفعة"
                              Background="#4CAF50" Foreground="White"
                              Padding="20,10" Margin="10" BorderThickness="0"
                              Click="BtnAddPayment_Click">
                            <Button.Template>
                                <ControlTemplate TargetType="Button">
                                    <Border Background="{TemplateBinding Background}"
                                          CornerRadius="5">
                                        <ContentPresenter HorizontalAlignment="Center"
                                                        VerticalAlignment="Center"
                                                        Margin="{TemplateBinding Padding}"/>
                                    </Border>
                                </ControlTemplate>
                            </Button.Template>
                        </Button>

                        <Button Name="BtnViewAttendance" Content="عرض الحضور"
                              Background="#FF9800" Foreground="White"
                              Padding="20,10" Margin="10" BorderThickness="0"
                              Click="BtnViewAttendance_Click">
                            <Button.Template>
                                <ControlTemplate TargetType="Button">
                                    <Border Background="{TemplateBinding Background}"
                                          CornerRadius="5">
                                        <ContentPresenter HorizontalAlignment="Center"
                                                        VerticalAlignment="Center"
                                                        Margin="{TemplateBinding Padding}"/>
                                    </Border>
                                </ControlTemplate>
                            </Button.Template>
                        </Button>

                        <Button Name="BtnClose" Content="إغلاق"
                              Background="#757575" Foreground="White"
                              Padding="20,10" Margin="10" BorderThickness="0"
                              Click="BtnClose_Click">
                            <Button.Template>
                                <ControlTemplate TargetType="Button">
                                    <Border Background="{TemplateBinding Background}"
                                          CornerRadius="5">
                                        <ContentPresenter HorizontalAlignment="Center"
                                                        VerticalAlignment="Center"
                                                        Margin="{TemplateBinding Padding}"/>
                                    </Border>
                                </ControlTemplate>
                            </Button.Template>
                        </Button>
                    </StackPanel>
                </Border>
            </StackPanel>
        </ScrollViewer>
    </Grid>
</Window>
