using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace EduTrackForWin.Models
{
    public class ExamResult
    {
        [Key]
        public int Id { get; set; }

        [Required]
        public int ExamId { get; set; }

        [ForeignKey("ExamId")]
        public Exam Exam { get; set; } = null!;

        [Required]
        public int StudentId { get; set; }

        [ForeignKey("StudentId")]
        public Student Student { get; set; } = null!;

        [Column(TypeName = "decimal(5,2)")]
        public decimal Marks { get; set; }

        [Column(TypeName = "decimal(5,2)")]
        public decimal? BonusMarks { get; set; }

        [StringLength(1000)]
        public string Notes { get; set; } = string.Empty;

        public ExamResultStatus Status { get; set; } = ExamResultStatus.Present;

        public DateTime? SubmittedAt { get; set; }

        public bool IsAbsent { get; set; } = false;

        public bool IsCheating { get; set; } = false;

        [StringLength(500)]
        public string TeacherComments { get; set; } = string.Empty;

        // Computed Properties
        [NotMapped]
        public decimal TotalMarks => Marks + (BonusMarks ?? 0);

        [NotMapped]
        public decimal Percentage => Exam?.TotalMarks > 0 ? (TotalMarks * 100 / Exam.TotalMarks) : 0;

        [NotMapped]
        public bool IsPassed => Exam?.PassingMarks > 0 && TotalMarks >= Exam.PassingMarks;

        [NotMapped]
        public string Grade
        {
            get
            {
                if (IsAbsent) return "غائب";
                if (IsCheating) return "غش";
                
                var percentage = Percentage;
                return percentage switch
                {
                    >= 95 => "ممتاز+",
                    >= 90 => "ممتاز",
                    >= 85 => "جيد جداً+",
                    >= 80 => "جيد جداً",
                    >= 75 => "جيد+",
                    >= 70 => "جيد",
                    >= 65 => "مقبول+",
                    >= 60 => "مقبول",
                    >= 50 => "ضعيف",
                    _ => "راسب"
                };
            }
        }

        [NotMapped]
        public string StatusName => Status switch
        {
            ExamResultStatus.Present => "حاضر",
            ExamResultStatus.Absent => "غائب",
            ExamResultStatus.Late => "متأخر",
            ExamResultStatus.Excused => "معذور",
            _ => "غير محدد"
        };

        public DateTime CreatedAt { get; set; } = DateTime.Now;
        public DateTime? UpdatedAt { get; set; }
    }

    public enum ExamResultStatus
    {
        Present = 1,  // حاضر
        Absent = 2,   // غائب
        Late = 3,     // متأخر
        Excused = 4   // معذور
    }
}
