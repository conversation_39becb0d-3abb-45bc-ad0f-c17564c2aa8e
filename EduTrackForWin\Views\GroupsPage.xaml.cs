using System.Collections.ObjectModel;
using System.Windows;
using System.Windows.Controls;
using EduTrackForWin.Models;
using EduTrackForWin.Data;
using Microsoft.EntityFrameworkCore;

namespace EduTrackForWin.Views
{
    public partial class GroupsPage : Page
    {
        private readonly EduTrackDbContext _context;
        private ObservableCollection<Group> _groups;
        private List<Group> _allGroups;

        public GroupsPage()
        {
            InitializeComponent();
            _context = new EduTrackDbContext();
            _groups = new ObservableCollection<Group>();
            _allGroups = new List<Group>();

            DgGroups.ItemsSource = _groups;
            LoadData();
        }

        private async void LoadData()
        {
            await LoadTeachers();
            await LoadGroups();
        }

        private async Task LoadTeachers()
        {
            try
            {
                var teachers = await _context.Teachers
                    .OrderBy(t => t.Name)
                    .ToListAsync();

                CmbTeacherFilter.Items.Clear();
                CmbTeacherFilter.Items.Add(new ComboBoxItem { Content = "جميع المعلمين", Tag = 0 });

                foreach (var teacher in teachers)
                {
                    CmbTeacherFilter.Items.Add(new ComboBoxItem { Content = teacher.Name, Tag = teacher.Id });
                }

                CmbTeacherFilter.SelectedIndex = 0;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل المعلمين: {ex.Message}", "خطأ",
                              MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async Task LoadGroups()
        {
            try
            {
                _allGroups = await _context.Groups
                    .Include(g => g.Teacher)
                    .Include(g => g.Students)
                    .OrderBy(g => g.Name)
                    .ToListAsync();

                RefreshGroupsList();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل المجموعات: {ex.Message}", "خطأ",
                              MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void RefreshGroupsList()
        {
            _groups.Clear();

            var filteredGroups = _allGroups.AsEnumerable();

            // Apply search filter
            if (!string.IsNullOrWhiteSpace(TxtSearch.Text))
            {
                var searchTerm = TxtSearch.Text.ToLower();
                filteredGroups = filteredGroups.Where(g =>
                    g.Name.ToLower().Contains(searchTerm) ||
                    g.Subject.ToLower().Contains(searchTerm) ||
                    g.Grade.ToLower().Contains(searchTerm) ||
                    g.Teacher?.Name.ToLower().Contains(searchTerm) == true);
            }

            // Apply teacher filter
            if (CmbTeacherFilter.SelectedItem is ComboBoxItem selectedTeacher &&
                selectedTeacher.Tag is int teacherId && teacherId > 0)
            {
                filteredGroups = filteredGroups.Where(g => g.TeacherId == teacherId);
            }

            // Apply status filter
            if (CmbStatusFilter.SelectedIndex > 0)
            {
                var selectedStatus = ((ComboBoxItem)CmbStatusFilter.SelectedItem).Content.ToString();
                var isActive = selectedStatus == "نشطة";
                filteredGroups = filteredGroups.Where(g => g.IsActive == isActive);
            }

            foreach (var group in filteredGroups)
            {
                _groups.Add(group);
            }

            // Show/hide no data message
            TxtNoData.Visibility = _groups.Count == 0 ? Visibility.Visible : Visibility.Collapsed;
            DgGroups.Visibility = _groups.Count == 0 ? Visibility.Collapsed : Visibility.Visible;
        }

        private void TxtSearch_TextChanged(object sender, TextChangedEventArgs e)
        {
            RefreshGroupsList();
        }

        private void CmbTeacherFilter_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            if (IsLoaded)
                RefreshGroupsList();
        }

        private void CmbStatusFilter_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            if (IsLoaded)
                RefreshGroupsList();
        }

        private void BtnRefresh_Click(object sender, RoutedEventArgs e)
        {
            LoadData();
        }

        private void BtnAddGroup_Click(object sender, RoutedEventArgs e)
        {
            var addGroupWindow = new AddEditGroupWindow();
            if (addGroupWindow.ShowDialog() == true)
            {
                LoadGroups(); // Refresh the list
            }
        }

        private void BtnViewGroup_Click(object sender, RoutedEventArgs e)
        {
            if (sender is Button button && button.Tag is int groupId)
            {
                var group = _allGroups.FirstOrDefault(g => g.Id == groupId);
                if (group != null)
                {
                    var viewWindow = new GroupDetailsWindow(group);
                    viewWindow.ShowDialog();
                }
            }
        }

        private void BtnEditGroup_Click(object sender, RoutedEventArgs e)
        {
            if (sender is Button button && button.Tag is int groupId)
            {
                var group = _allGroups.FirstOrDefault(g => g.Id == groupId);
                if (group != null)
                {
                    var editWindow = new AddEditGroupWindow(group);
                    if (editWindow.ShowDialog() == true)
                    {
                        LoadGroups(); // Refresh the list
                    }
                }
            }
        }

        private void BtnManageStudents_Click(object sender, RoutedEventArgs e)
        {
            if (sender is Button button && button.Tag is int groupId)
            {
                var group = _allGroups.FirstOrDefault(g => g.Id == groupId);
                if (group != null)
                {
                    var manageWindow = new ManageGroupStudentsWindow(group);
                    if (manageWindow.ShowDialog() == true)
                    {
                        LoadGroups(); // Refresh the list
                    }
                }
            }
        }

        private async void BtnDeleteGroup_Click(object sender, RoutedEventArgs e)
        {
            if (sender is Button button && button.Tag is int groupId)
            {
                var group = _allGroups.FirstOrDefault(g => g.Id == groupId);
                if (group != null)
                {
                    // Check if group has students
                    if (group.Students != null && group.Students.Any())
                    {
                        MessageBox.Show(
                            $"لا يمكن حذف المجموعة '{group.Name}' لأنها تحتوي على طلاب.\nيرجى نقل الطلاب إلى مجموعة أخرى أولاً.",
                            "تعذر الحذف",
                            MessageBoxButton.OK,
                            MessageBoxImage.Warning);
                        return;
                    }

                    var result = MessageBox.Show(
                        $"هل أنت متأكد من حذف المجموعة '{group.Name}'؟\nسيتم حذف جميع البيانات المرتبطة بها.",
                        "تأكيد الحذف",
                        MessageBoxButton.YesNo,
                        MessageBoxImage.Warning);

                    if (result == MessageBoxResult.Yes)
                    {
                        try
                        {
                            _context.Groups.Remove(group);
                            await _context.SaveChangesAsync();
                            LoadGroups(); // Refresh the list
                            MessageBox.Show("تم حذف المجموعة بنجاح", "نجح",
                                          MessageBoxButton.OK, MessageBoxImage.Information);
                        }
                        catch (Exception ex)
                        {
                            MessageBox.Show($"خطأ في حذف المجموعة: {ex.Message}", "خطأ",
                                          MessageBoxButton.OK, MessageBoxImage.Error);
                        }
                    }
                }
            }
        }

        private void Page_Unloaded(object sender, RoutedEventArgs e)
        {
            _context?.Dispose();
        }
    }
}
