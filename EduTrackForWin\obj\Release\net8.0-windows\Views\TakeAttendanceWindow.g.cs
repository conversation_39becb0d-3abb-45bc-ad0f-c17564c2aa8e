﻿#pragma checksum "..\..\..\..\Views\TakeAttendanceWindow.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "C5E951F4B10DDC3AC88CC36B28BB7D52CD2DFBB3"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using EduTrackForWin.Views;
using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace EduTrackForWin.Views {
    
    
    /// <summary>
    /// TakeAttendanceWindow
    /// </summary>
    public partial class TakeAttendanceWindow : System.Windows.Window, System.Windows.Markup.IComponentConnector {
        
        
        #line 74 "..\..\..\..\Views\TakeAttendanceWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TxtSessionInfo;
        
        #line default
        #line hidden
        
        
        #line 76 "..\..\..\..\Views\TakeAttendanceWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TxtSessionDetails;
        
        #line default
        #line hidden
        
        
        #line 88 "..\..\..\..\Views\TakeAttendanceWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BtnMarkAllPresent;
        
        #line default
        #line hidden
        
        
        #line 104 "..\..\..\..\Views\TakeAttendanceWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BtnMarkAllAbsent;
        
        #line default
        #line hidden
        
        
        #line 120 "..\..\..\..\Views\TakeAttendanceWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BtnClearAll;
        
        #line default
        #line hidden
        
        
        #line 145 "..\..\..\..\Views\TakeAttendanceWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DataGrid DgAttendance;
        
        #line default
        #line hidden
        
        
        #line 201 "..\..\..\..\Views\TakeAttendanceWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TxtTotalStudents;
        
        #line default
        #line hidden
        
        
        #line 207 "..\..\..\..\Views\TakeAttendanceWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TxtPresentCount;
        
        #line default
        #line hidden
        
        
        #line 213 "..\..\..\..\Views\TakeAttendanceWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TxtAttendanceRate;
        
        #line default
        #line hidden
        
        
        #line 222 "..\..\..\..\Views\TakeAttendanceWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BtnSave;
        
        #line default
        #line hidden
        
        
        #line 224 "..\..\..\..\Views\TakeAttendanceWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BtnCancel;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/EduTrackForWin;component/views/takeattendancewindow.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\Views\TakeAttendanceWindow.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.TxtSessionInfo = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 2:
            this.TxtSessionDetails = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 3:
            this.BtnMarkAllPresent = ((System.Windows.Controls.Button)(target));
            
            #line 91 "..\..\..\..\Views\TakeAttendanceWindow.xaml"
            this.BtnMarkAllPresent.Click += new System.Windows.RoutedEventHandler(this.BtnMarkAllPresent_Click);
            
            #line default
            #line hidden
            return;
            case 4:
            this.BtnMarkAllAbsent = ((System.Windows.Controls.Button)(target));
            
            #line 107 "..\..\..\..\Views\TakeAttendanceWindow.xaml"
            this.BtnMarkAllAbsent.Click += new System.Windows.RoutedEventHandler(this.BtnMarkAllAbsent_Click);
            
            #line default
            #line hidden
            return;
            case 5:
            this.BtnClearAll = ((System.Windows.Controls.Button)(target));
            
            #line 123 "..\..\..\..\Views\TakeAttendanceWindow.xaml"
            this.BtnClearAll.Click += new System.Windows.RoutedEventHandler(this.BtnClearAll_Click);
            
            #line default
            #line hidden
            return;
            case 6:
            this.DgAttendance = ((System.Windows.Controls.DataGrid)(target));
            return;
            case 7:
            this.TxtTotalStudents = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 8:
            this.TxtPresentCount = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 9:
            this.TxtAttendanceRate = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 10:
            this.BtnSave = ((System.Windows.Controls.Button)(target));
            
            #line 223 "..\..\..\..\Views\TakeAttendanceWindow.xaml"
            this.BtnSave.Click += new System.Windows.RoutedEventHandler(this.BtnSave_Click);
            
            #line default
            #line hidden
            return;
            case 11:
            this.BtnCancel = ((System.Windows.Controls.Button)(target));
            
            #line 225 "..\..\..\..\Views\TakeAttendanceWindow.xaml"
            this.BtnCancel.Click += new System.Windows.RoutedEventHandler(this.BtnCancel_Click);
            
            #line default
            #line hidden
            return;
            }
            this._contentLoaded = true;
        }
    }
}

