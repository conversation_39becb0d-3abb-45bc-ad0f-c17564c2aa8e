using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using EduTrackForWin.Models;
using EduTrackForWin.Services;

namespace EduTrackForWin.Views
{
    /// <summary>
    /// Interaction logic for OverdueManagementPage.xaml
    /// </summary>
    public partial class OverdueManagementPage : Page
    {
        private readonly OverdueService _overdueService;
        private readonly PaymentService _paymentService;
        private List<OverdueStudent> _allOverdueStudents;
        private OverdueSummary _summary;

        public OverdueManagementPage()
        {
            try
            {
                InitializeComponent();
                _overdueService = new OverdueService();
                _paymentService = new PaymentService();
                _allOverdueStudents = new List<OverdueStudent>();

                Loaded += OverdueManagementPage_Loaded;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تهيئة صفحة إدارة المتأخرات: {ex.Message}", "خطأ", 
                              MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async void OverdueManagementPage_Loaded(object sender, RoutedEventArgs e)
        {
            await LoadData();
        }

        private async Task LoadData()
        {
            try
            {
                // Load overdue students and summary
                _allOverdueStudents = await _overdueService.GetOverdueStudentsAsync();
                _summary = await _overdueService.GetOverdueSummaryAsync();

                // Update statistics
                UpdateStatistics();

                // Refresh the list
                RefreshOverdueList();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل البيانات: {ex.Message}", "خطأ", 
                              MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void UpdateStatistics()
        {
            try
            {
                if (TxtTotalOverdue != null) 
                    TxtTotalOverdue.Text = _summary.TotalOverdueStudents.ToString();

                if (TxtCriticalOverdue != null) 
                    TxtCriticalOverdue.Text = _summary.CriticalOverdue.Count.ToString();

                if (TxtTotalAmount != null) 
                    TxtTotalAmount.Text = $"{_summary.TotalOverdueAmount:C}";

                if (TxtLateFees != null) 
                    TxtLateFees.Text = $"{_summary.TotalLateFees:C}";

                if (TxtAverageDays != null)
                {
                    var avgDays = _allOverdueStudents.Any() 
                        ? _allOverdueStudents.Average(o => o.OverdueDays) 
                        : 0;
                    TxtAverageDays.Text = $"{avgDays:F0} يوم";
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحديث الإحصائيات: {ex.Message}", "خطأ", 
                              MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void RefreshOverdueList()
        {
            try
            {
                var filteredStudents = _allOverdueStudents.AsEnumerable();

                // Apply severity filter
                if (CmbSeverityFilter?.SelectedItem is ComboBoxItem severityItem && 
                    severityItem.Content.ToString() != "جميع المستويات")
                {
                    var severity = severityItem.Content.ToString();
                    filteredStudents = filteredStudents.Where(s => s.SeverityLevel == severity);
                }

                // Update DataGrid
                if (DgOverdueStudents != null)
                {
                    DgOverdueStudents.ItemsSource = filteredStudents.ToList();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تصفية البيانات: {ex.Message}", "خطأ", 
                              MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        // Event Handlers
        private async void BtnApplyLateFees_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var result = MessageBox.Show(
                    "هل أنت متأكد من تطبيق غرامات التأخير على جميع الطلاب المتأخرين؟\n\nسيتم إضافة غرامات حسب عدد أيام التأخير.",
                    "تأكيد تطبيق الغرامات", 
                    MessageBoxButton.YesNo, 
                    MessageBoxImage.Warning);

                if (result == MessageBoxResult.Yes)
                {
                    var feesApplied = await _overdueService.ApplyLateFees();
                    
                    MessageBox.Show($"تم تطبيق غرامات التأخير على {feesApplied} طالب", "تم التطبيق", 
                                  MessageBoxButton.OK, MessageBoxImage.Information);
                    
                    // Refresh data
                    await LoadData();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تطبيق غرامات التأخير: {ex.Message}", "خطأ", 
                              MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void BtnSendReminders_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var overdueCount = _allOverdueStudents.Count;
                if (overdueCount == 0)
                {
                    MessageBox.Show("لا يوجد طلاب متأخرين لإرسال تذكيرات لهم", "تنبيه", 
                                  MessageBoxButton.OK, MessageBoxImage.Information);
                    return;
                }

                var message = $"سيتم إرسال تذكيرات لـ {overdueCount} طالب متأخر:\n\n";
                
                // Show preview of reminders
                foreach (var student in _allOverdueStudents.Take(5))
                {
                    message += $"• {student.StudentName} - {student.TotalDue:C} - {student.OverdueDays} يوم\n";
                }
                
                if (overdueCount > 5)
                {
                    message += $"... و {overdueCount - 5} طلاب آخرين\n";
                }

                message += "\nملاحظة: هذه ميزة تجريبية - سيتم تطويرها لاحقاً لإرسال رسائل فعلية.";

                MessageBox.Show(message, "تذكيرات الدفع", 
                              MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في إرسال التذكيرات: {ex.Message}", "خطأ", 
                              MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async void BtnGenerateReport_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var report = await _overdueService.GenerateOverdueReportAsync();
                
                var reportWindow = new Window
                {
                    Title = "تقرير المتأخرات المالية",
                    Width = 600,
                    Height = 500,
                    WindowStartupLocation = WindowStartupLocation.CenterScreen
                };

                var scrollViewer = new ScrollViewer();
                var textBlock = new TextBlock
                {
                    Text = report,
                    FontFamily = new System.Windows.Media.FontFamily("Consolas"),
                    FontSize = 12,
                    Margin = new Thickness(20),
                    TextWrapping = TextWrapping.Wrap
                };

                scrollViewer.Content = textBlock;
                reportWindow.Content = scrollViewer;
                reportWindow.ShowDialog();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في إنشاء التقرير: {ex.Message}", "خطأ", 
                              MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async void BtnPayment_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (DgOverdueStudents.SelectedItem is OverdueStudent selectedStudent)
                {
                    var paymentWindow = new AddEditPaymentWindow();
                    
                    // Pre-select the student
                    paymentWindow.Loaded += (s, args) =>
                    {
                        if (paymentWindow.CmbStudent != null)
                        {
                            paymentWindow.CmbStudent.SelectedValue = selectedStudent.StudentId;
                        }
                    };

                    if (paymentWindow.ShowDialog() == true)
                    {
                        await LoadData();
                        MessageBox.Show("تم تسجيل الدفعة بنجاح", "نجح", 
                                      MessageBoxButton.OK, MessageBoxImage.Information);
                    }
                }
                else
                {
                    MessageBox.Show("يرجى اختيار طالب لتسجيل دفعة له", "تنبيه", 
                                  MessageBoxButton.OK, MessageBoxImage.Warning);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تسجيل الدفعة: {ex.Message}", "خطأ", 
                              MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void BtnReminder_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (DgOverdueStudents.SelectedItem is OverdueStudent selectedStudent)
                {
                    var message = $"تذكير بالدفع\n" +
                                $"================\n\n" +
                                $"الطالب: {selectedStudent.StudentName}\n" +
                                $"المجموعة: {selectedStudent.GroupName}\n" +
                                $"الهاتف: {selectedStudent.Phone}\n" +
                                $"المبلغ المستحق: {selectedStudent.TotalDue:C}\n" +
                                $"أيام التأخير: {selectedStudent.OverdueDays} يوم\n" +
                                $"مستوى التأخير: {selectedStudent.SeverityLevel}\n\n" +
                                $"يرجى سداد المبلغ المستحق في أقرب وقت ممكن.";

                    MessageBox.Show(message, "تذكير بالدفع", 
                                  MessageBoxButton.OK, MessageBoxImage.Information);
                }
                else
                {
                    MessageBox.Show("يرجى اختيار طالب لإرسال تذكير له", "تنبيه", 
                                  MessageBoxButton.OK, MessageBoxImage.Warning);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في إرسال التذكير: {ex.Message}", "خطأ", 
                              MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async void BtnLateFee_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (DgOverdueStudents.SelectedItem is OverdueStudent selectedStudent)
                {
                    if (selectedStudent.LateFee <= 0)
                    {
                        MessageBox.Show("لا توجد غرامة تأخير مستحقة لهذا الطالب", "تنبيه", 
                                      MessageBoxButton.OK, MessageBoxImage.Information);
                        return;
                    }

                    var result = MessageBox.Show(
                        $"هل تريد تطبيق غرامة تأخير بمبلغ {selectedStudent.LateFee:C} على الطالب {selectedStudent.StudentName}؟",
                        "تأكيد الغرامة", 
                        MessageBoxButton.YesNo, 
                        MessageBoxImage.Question);

                    if (result == MessageBoxResult.Yes)
                    {
                        var lateFeePayment = new Payment
                        {
                            StudentId = selectedStudent.StudentId,
                            Type = PaymentType.Fine,
                            Amount = -selectedStudent.LateFee, // Negative for fee
                            PaymentDate = DateTime.Now,
                            PaymentMethod = "غرامة تأخير",
                            Notes = $"غرامة تأخير - {selectedStudent.OverdueDays} يوم",
                            CreatedAt = DateTime.Now
                        };

                        await _paymentService.AddPaymentAsync(lateFeePayment);
                        await LoadData();
                        
                        MessageBox.Show("تم تطبيق غرامة التأخير بنجاح", "تم التطبيق", 
                                      MessageBoxButton.OK, MessageBoxImage.Information);
                    }
                }
                else
                {
                    MessageBox.Show("يرجى اختيار طالب لتطبيق غرامة عليه", "تنبيه", 
                                  MessageBoxButton.OK, MessageBoxImage.Warning);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تطبيق الغرامة: {ex.Message}", "خطأ", 
                              MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void Filter_Changed(object sender, SelectionChangedEventArgs e)
        {
            if (_allOverdueStudents != null)
            {
                RefreshOverdueList();
            }
        }

        private async void BtnRefresh_Click(object sender, RoutedEventArgs e)
        {
            await LoadData();
        }
    }
}
