using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace EduTrackForWin.Models
{
    public class Teacher
    {
        [Key]
        public int Id { get; set; }

        [Required]
        [StringLength(100)]
        public string Name { get; set; } = string.Empty;

        [StringLength(100)]
        public string Specialization { get; set; } = string.Empty;

        [StringLength(20)]
        public string Phone { get; set; } = string.Empty;

        [StringLength(100)]
        public string Email { get; set; } = string.Empty;

        [Column(TypeName = "decimal(18,2)")]
        public decimal MonthlySalary { get; set; }

        [Column(TypeName = "decimal(18,2)")]
        public decimal SalaryPerSession { get; set; }

        public bool IsFixedSalary { get; set; } = true; // true for monthly, false for per session

        [StringLength(500)]
        public string Notes { get; set; } = string.Empty;

        // Navigation Properties
        public virtual ICollection<Group> Groups { get; set; } = new List<Group>();
        public virtual ICollection<Session> Sessions { get; set; } = new List<Session>();
        public virtual ICollection<Attendance> TeacherAttendances { get; set; } = new List<Attendance>();

        // Computed Properties
        [NotMapped]
        public int TotalGroups => Groups?.Count ?? 0;

        [NotMapped]
        public int SessionsThisMonth => Sessions?.Count(s => s.Date.Month == DateTime.Now.Month && s.Date.Year == DateTime.Now.Year) ?? 0;

        [NotMapped]
        public decimal CalculatedSalary => IsFixedSalary ? MonthlySalary : (SessionsThisMonth * SalaryPerSession);

        public DateTime CreatedAt { get; set; } = DateTime.Now;
        public DateTime? UpdatedAt { get; set; }
    }
}
