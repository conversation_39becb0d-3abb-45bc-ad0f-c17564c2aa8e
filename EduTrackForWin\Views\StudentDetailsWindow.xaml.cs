using System.Windows;
using EduTrackForWin.Models;

namespace EduTrackForWin.Views
{
    public partial class StudentDetailsWindow : Window
    {
        private readonly Student _student;

        public StudentDetailsWindow(Student student)
        {
            InitializeComponent();
            _student = student;
            LoadStudentDetails();
        }

        private void LoadStudentDetails()
        {
            // Basic Information
            TxtStudentName.Text = _student.Name;
            TxtName.Text = _student.Name;
            TxtGrade.Text = string.IsNullOrEmpty(_student.Grade) ? "غير محدد" : _student.Grade;
            TxtPhone.Text = string.IsNullOrEmpty(_student.Phone) ? "غير محدد" : _student.Phone;
            TxtAddress.Text = string.IsNullOrEmpty(_student.Address) ? "غير محدد" : _student.Address;
            TxtNotes.Text = string.IsNullOrEmpty(_student.Notes) ? "لا توجد ملاحظات" : _student.Notes;
            TxtCreatedAt.Text = _student.CreatedAt.ToString("yyyy/MM/dd");

            // Group and Teacher Information
            if (_student.Group != null)
            {
                TxtGroup.Text = _student.Group.Name;
                TxtTeacher.Text = _student.Group.Teacher?.Name ?? "غير محدد";
            }
            else
            {
                TxtGroup.Text = "غير مسجل في مجموعة";
                TxtTeacher.Text = "غير محدد";
            }

            // Financial Information
            TxtTotalPaid.Text = $"{_student.TotalPaid:C}";
            TxtTotalDue.Text = $"{_student.TotalDue:C}";
            TxtPaymentStatus.Text = _student.PaymentStatus;
            
            // Set payment status color
            if (_student.PaymentStatus == "مدفوع")
            {
                TxtPaymentStatus.Foreground = System.Windows.Media.Brushes.Green;
            }
            else
            {
                TxtPaymentStatus.Foreground = System.Windows.Media.Brushes.Red;
            }

            // Load Payments
            if (_student.Payments != null && _student.Payments.Any())
            {
                var recentPayments = _student.Payments
                    .OrderByDescending(p => p.PaymentDate)
                    .Take(5)
                    .ToList();
                DgPayments.ItemsSource = recentPayments;
            }

            // Attendance Summary
            if (_student.Attendances != null && _student.Attendances.Any())
            {
                var totalSessions = _student.Attendances.Count;
                var presentSessions = _student.Attendances.Count(a => a.IsPresent);
                var attendanceRate = totalSessions > 0 ? (double)presentSessions / totalSessions * 100 : 0;

                TxtTotalSessions.Text = totalSessions.ToString();
                TxtPresentSessions.Text = presentSessions.ToString();
                TxtAttendanceRate.Text = $"{attendanceRate:F1}%";
            }
            else
            {
                TxtTotalSessions.Text = "0";
                TxtPresentSessions.Text = "0";
                TxtAttendanceRate.Text = "0%";
            }
        }

        private void BtnEdit_Click(object sender, RoutedEventArgs e)
        {
            var editWindow = new AddEditStudentWindow(_student);
            if (editWindow.ShowDialog() == true)
            {
                // Refresh the data
                LoadStudentDetails();
            }
        }

        private void BtnAddPayment_Click(object sender, RoutedEventArgs e)
        {
            // TODO: Implement add payment functionality
            MessageBox.Show("سيتم تطوير هذه الميزة قريباً", "قيد التطوير", 
                          MessageBoxButton.OK, MessageBoxImage.Information);
        }

        private void BtnViewAttendance_Click(object sender, RoutedEventArgs e)
        {
            // TODO: Implement view attendance functionality
            MessageBox.Show("سيتم تطوير هذه الميزة قريباً", "قيد التطوير", 
                          MessageBoxButton.OK, MessageBoxImage.Information);
        }

        private void BtnClose_Click(object sender, RoutedEventArgs e)
        {
            Close();
        }
    }
}
