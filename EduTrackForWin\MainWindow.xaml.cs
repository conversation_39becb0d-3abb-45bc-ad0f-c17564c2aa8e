﻿using System.Windows;
using System.Windows.Controls;
using EduTrackForWin.Views;
using EduTrackForWin.Services;

namespace EduTrackForWin;

/// <summary>
/// Interaction logic for MainWindow.xaml
/// </summary>
public partial class MainWindow : Window
{
    private readonly DatabaseService _databaseService;
    private Button? _activeButton;

    public MainWindow()
    {
        InitializeComponent();
        _databaseService = new DatabaseService();
        InitializeAsync();
    }

    private async void InitializeAsync()
    {
        try
        {
            await _databaseService.InitializeDatabaseAsync();
            LoadDashboard();
        }
        catch (Exception ex)
        {
            MessageBox.Show($"خطأ في تهيئة التطبيق: {ex.Message}", "خطأ",
                          MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    private void LoadDashboard()
    {
        SetActiveButton(BtnDashboard);
        TxtPageTitle.Text = "لوحة المعلومات";
        MainFrame.Navigate(new DashboardPage());
    }

    private void SetActiveButton(Button button)
    {
        // Reset previous active button
        if (_activeButton != null)
        {
            _activeButton.Tag = null;
        }

        // Set new active button
        _activeButton = button;
        _activeButton.Tag = "Active";
    }

    private void BtnDashboard_Click(object sender, RoutedEventArgs e)
    {
        SetActiveButton(BtnDashboard);
        TxtPageTitle.Text = "لوحة المعلومات";
        MainFrame.Navigate(new DashboardPage());
    }

    private void BtnStudents_Click(object sender, RoutedEventArgs e)
    {
        try
        {
            SetActiveButton(BtnStudents);
            TxtPageTitle.Text = "إدارة الطلاب";
            MainFrame.Navigate(new StudentsPage());
        }
        catch (Exception ex)
        {
            MessageBox.Show($"خطأ في عرض صفحة الطلاب: {ex.Message}", "خطأ",
                          MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    private void BtnTeachers_Click(object sender, RoutedEventArgs e)
    {
        try
        {
            SetActiveButton(BtnTeachers);
            TxtPageTitle.Text = "إدارة المعلمين";
            MainFrame.Navigate(new TeachersPage());
        }
        catch (Exception ex)
        {
            MessageBox.Show($"خطأ في عرض صفحة المعلمين: {ex.Message}", "خطأ",
                          MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    private void BtnGroups_Click(object sender, RoutedEventArgs e)
    {
        try
        {
            SetActiveButton(BtnGroups);
            TxtPageTitle.Text = "إدارة المجموعات";
            MainFrame.Navigate(new GroupsPage());
        }
        catch (Exception ex)
        {
            MessageBox.Show($"خطأ في عرض صفحة المجموعات: {ex.Message}", "خطأ",
                          MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    private void BtnSessions_Click(object sender, RoutedEventArgs e)
    {
        try
        {
            SetActiveButton(BtnSessions);
            TxtPageTitle.Text = "إدارة الحصص";
            MainFrame.Navigate(new SessionsPage());
        }
        catch (Exception ex)
        {
            MessageBox.Show($"خطأ في عرض صفحة الحصص: {ex.Message}", "خطأ",
                          MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    private void BtnAttendance_Click(object sender, RoutedEventArgs e)
    {
        try
        {
            SetActiveButton(BtnAttendance);
            TxtPageTitle.Text = "الحضور والغياب";
            MainFrame.Navigate(new AttendancePage());
        }
        catch (Exception ex)
        {
            MessageBox.Show($"خطأ في عرض صفحة الحضور والغياب: {ex.Message}", "خطأ",
                          MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    private void BtnFinance_Click(object sender, RoutedEventArgs e)
    {
        try
        {
            SetActiveButton(BtnFinance);
            TxtPageTitle.Text = "النظام المالي";
            MainFrame.Navigate(new FinancePage());
        }
        catch (Exception ex)
        {
            MessageBox.Show($"خطأ في عرض صفحة النظام المالي: {ex.Message}", "خطأ",
                          MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    private void BtnStudentPayments_Click(object sender, RoutedEventArgs e)
    {
        try
        {
            SetActiveButton(BtnStudentPayments);
            TxtPageTitle.Text = "تسجيل دفع الطلاب";
            MainFrame.Navigate(new StudentPaymentsPage());
        }
        catch (Exception ex)
        {
            MessageBox.Show($"خطأ في عرض صفحة تسجيل دفع الطلاب: {ex.Message}", "خطأ",
                          MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    private void BtnOverdueManagement_Click(object sender, RoutedEventArgs e)
    {
        try
        {
            SetActiveButton(BtnOverdueManagement);
            TxtPageTitle.Text = "إدارة المتأخرات المالية";
            MainFrame.Navigate(new OverdueManagementPage());
        }
        catch (Exception ex)
        {
            MessageBox.Show($"خطأ في عرض صفحة إدارة المتأخرات المالية: {ex.Message}", "خطأ",
                          MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    private void BtnExams_Click(object sender, RoutedEventArgs e)
    {
        SetActiveButton(BtnExams);
        TxtPageTitle.Text = "إدارة الاختبارات";
        // MainFrame.Navigate(new ExamsPage()); // سيتم إنشاؤها لاحقاً
        MessageBox.Show("صفحة إدارة الاختبارات قيد التطوير", "قريباً",
                      MessageBoxButton.OK, MessageBoxImage.Information);
    }

    private void BtnReports_Click(object sender, RoutedEventArgs e)
    {
        try
        {
            SetActiveButton(BtnReports);
            TxtPageTitle.Text = "التقارير";
            MainFrame.Navigate(new ReportsPage());
        }
        catch (Exception ex)
        {
            MessageBox.Show($"خطأ في عرض صفحة التقارير: {ex.Message}", "خطأ",
                          MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    private void BtnSettings_Click(object sender, RoutedEventArgs e)
    {
        try
        {
            SetActiveButton(BtnSettings);
            TxtPageTitle.Text = "الإعدادات";
            MainFrame.Navigate(new SettingsPage());
        }
        catch (Exception ex)
        {
            MessageBox.Show($"خطأ في عرض صفحة الإعدادات: {ex.Message}", "خطأ",
                          MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    private void BtnMinimize_Click(object sender, RoutedEventArgs e)
    {
        WindowState = WindowState.Minimized;
    }

    private void BtnClose_Click(object sender, RoutedEventArgs e)
    {
        var result = MessageBox.Show("هل تريد إغلاق التطبيق؟", "تأكيد الإغلاق",
                                   MessageBoxButton.YesNo, MessageBoxImage.Question);
        if (result == MessageBoxResult.Yes)
        {
            Application.Current.Shutdown();
        }
    }

    protected override void OnClosed(EventArgs e)
    {
        _databaseService?.Dispose();
        base.OnClosed(e);
    }
}
