using Microsoft.EntityFrameworkCore;
using EduTrackForWin.Data;
using EduTrackForWin.Models;

namespace EduTrackForWin.Services
{
    public class StudentService
    {
        private readonly EduTrackDbContext _context;

        public StudentService()
        {
            _context = new EduTrackDbContext();
        }

        public async Task<List<Student>> GetAllStudentsAsync()
        {
            return await _context.Students
                .Include(s => s.Group)
                .ThenInclude(g => g!.Teacher)
                .Include(s => s.Payments)
                .OrderBy(s => s.Name)
                .ToListAsync();
        }

        public async Task<Student?> GetStudentByIdAsync(int id)
        {
            return await _context.Students
                .Include(s => s.Group)
                .ThenInclude(g => g!.Teacher)
                .Include(s => s.Payments)
                .Include(s => s.Attendances)
                .ThenInclude(a => a.Session)
                .FirstOrDefaultAsync(s => s.Id == id);
        }

        public async Task<List<Student>> SearchStudentsAsync(string searchTerm)
        {
            return await _context.Students
                .Include(s => s.Group)
                .ThenInclude(g => g!.Teacher)
                .Where(s => s.Name.Contains(searchTerm) || 
                           s.Phone.Contains(searchTerm) ||
                           s.Grade.Contains(searchTerm))
                .OrderBy(s => s.Name)
                .ToListAsync();
        }

        public async Task<List<Student>> GetStudentsByGroupAsync(int groupId)
        {
            return await _context.Students
                .Include(s => s.Group)
                .Include(s => s.Payments)
                .Where(s => s.GroupId == groupId)
                .OrderBy(s => s.Name)
                .ToListAsync();
        }

        public async Task<List<Student>> GetStudentsWithOverduePaymentsAsync()
        {
            return await _context.Students
                .Include(s => s.Group)
                .Include(s => s.Payments)
                .Where(s => s.Group != null && s.TotalDue > 0)
                .OrderBy(s => s.Name)
                .ToListAsync();
        }

        public async Task<Student> AddStudentAsync(Student student)
        {
            student.CreatedAt = DateTime.Now;
            _context.Students.Add(student);
            await _context.SaveChangesAsync();
            return student;
        }

        public async Task<Student> UpdateStudentAsync(Student student)
        {
            student.UpdatedAt = DateTime.Now;
            _context.Students.Update(student);
            await _context.SaveChangesAsync();
            return student;
        }

        public async Task<bool> DeleteStudentAsync(int id)
        {
            var student = await _context.Students.FindAsync(id);
            if (student == null) return false;

            _context.Students.Remove(student);
            await _context.SaveChangesAsync();
            return true;
        }

        public async Task<bool> AssignStudentToGroupAsync(int studentId, int groupId)
        {
            var student = await _context.Students.FindAsync(studentId);
            if (student == null) return false;

            student.GroupId = groupId;
            student.UpdatedAt = DateTime.Now;
            await _context.SaveChangesAsync();
            return true;
        }

        public async Task<bool> RemoveStudentFromGroupAsync(int studentId)
        {
            var student = await _context.Students.FindAsync(studentId);
            if (student == null) return false;

            student.GroupId = null;
            student.UpdatedAt = DateTime.Now;
            await _context.SaveChangesAsync();
            return true;
        }

        public async Task<Dictionary<string, int>> GetStudentStatisticsAsync()
        {
            var totalStudents = await _context.Students.CountAsync();
            var studentsWithGroups = await _context.Students.CountAsync(s => s.GroupId != null);
            var studentsWithOverduePayments = await _context.Students
                .Include(s => s.Group)
                .Include(s => s.Payments)
                .CountAsync(s => s.Group != null && s.TotalDue > 0);

            return new Dictionary<string, int>
            {
                ["TotalStudents"] = totalStudents,
                ["StudentsWithGroups"] = studentsWithGroups,
                ["StudentsWithoutGroups"] = totalStudents - studentsWithGroups,
                ["StudentsWithOverduePayments"] = studentsWithOverduePayments
            };
        }

        public void Dispose()
        {
            _context?.Dispose();
        }
    }
}
