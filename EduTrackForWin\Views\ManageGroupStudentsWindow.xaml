<Window x:Class="EduTrackForWin.Views.ManageGroupStudentsWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:EduTrackForWin.Views"
        mc:Ignorable="d"
        Title="إدارة طلاب المجموعة" Height="700" Width="900"
        WindowStartupLocation="CenterOwner">

    <Window.Resources>
        <!-- Card Style -->
        <Style x:Key="CardStyle" TargetType="Border">
            <Setter Property="Background" Value="White"/>
            <Setter Property="CornerRadius" Value="8"/>
            <Setter Property="Padding" Value="20"/>
            <Setter Property="Margin" Value="10"/>
            <Setter Property="Effect">
                <Setter.Value>
                    <DropShadowEffect Color="Gray" Direction="270" ShadowDepth="2" Opacity="0.3" BlurRadius="5"/>
                </Setter.Value>
            </Setter>
        </Style>

        <!-- Button Styles -->
        <Style x:Key="PrimaryButtonStyle" TargetType="Button">
            <Setter Property="Background" Value="#FF9800"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Padding" Value="15,8"/>
            <Setter Property="Margin" Value="5"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}"
                              CornerRadius="5">
                            <ContentPresenter HorizontalAlignment="Center"
                                            VerticalAlignment="Center"
                                            Margin="{TemplateBinding Padding}"/>
                        </Border>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <Style x:Key="SuccessButtonStyle" TargetType="Button">
            <Setter Property="Background" Value="#4CAF50"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Padding" Value="15,8"/>
            <Setter Property="Margin" Value="5"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}"
                              CornerRadius="5">
                            <ContentPresenter HorizontalAlignment="Center"
                                            VerticalAlignment="Center"
                                            Margin="{TemplateBinding Padding}"/>
                        </Border>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <Style x:Key="DangerButtonStyle" TargetType="Button">
            <Setter Property="Background" Value="#F44336"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Padding" Value="15,8"/>
            <Setter Property="Margin" Value="5"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}"
                              CornerRadius="5">
                            <ContentPresenter HorizontalAlignment="Center"
                                            VerticalAlignment="Center"
                                            Margin="{TemplateBinding Padding}"/>
                        </Border>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>
    </Window.Resources>

    <Grid Background="#F5F5F5">
        <Grid.ColumnDefinitions>
            <ColumnDefinition Width="*"/>
            <ColumnDefinition Width="*"/>
        </Grid.ColumnDefinitions>

        <!-- Current Students -->
        <Border Grid.Column="0" Style="{StaticResource CardStyle}" Margin="20,20,10,20">
            <StackPanel>
                <Grid Margin="0,0,0,15">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>
                    
                    <TextBlock Grid.Column="0" Name="TxtGroupName" Text="طلاب المجموعة" 
                             FontSize="18" FontWeight="Bold" Foreground="#FF9800"/>
                    
                    <TextBlock Grid.Column="1" Name="TxtStudentCount" Text="(0 طالب)" 
                             FontSize="14" Foreground="#757575" VerticalAlignment="Bottom"/>
                </Grid>

                <DataGrid Name="DgCurrentStudents" 
                        AutoGenerateColumns="False" 
                        CanUserAddRows="False" 
                        CanUserDeleteRows="False"
                        IsReadOnly="True"
                        GridLinesVisibility="Horizontal"
                        HeadersVisibility="Column"
                        BorderThickness="0"
                        Background="Transparent"
                        RowBackground="White"
                        AlternatingRowBackground="#F5F5F5"
                        Height="400"
                        SelectionMode="Single">
                    
                    <DataGrid.Columns>
                        <DataGridTextColumn Header="اسم الطالب" Binding="{Binding Name}" Width="150"/>
                        <DataGridTextColumn Header="الصف" Binding="{Binding Grade}" Width="80"/>
                        <DataGridTextColumn Header="رقم الهاتف" Binding="{Binding Phone}" Width="120"/>
                        
                        <DataGridTemplateColumn Header="الإجراءات" Width="100">
                            <DataGridTemplateColumn.CellTemplate>
                                <DataTemplate>
                                    <Button Content="إزالة" Style="{StaticResource DangerButtonStyle}" 
                                          Click="BtnRemoveStudent_Click" Tag="{Binding Id}"/>
                                </DataTemplate>
                            </DataGridTemplateColumn.CellTemplate>
                        </DataGridTemplateColumn>
                    </DataGrid.Columns>
                </DataGrid>

                <StackPanel Orientation="Horizontal" HorizontalAlignment="Center" Margin="0,20,0,0">
                    <Button Name="BtnRemoveAll" Content="إزالة الكل" Style="{StaticResource DangerButtonStyle}" 
                          Click="BtnRemoveAll_Click"/>
                </StackPanel>
            </StackPanel>
        </Border>

        <!-- Available Students -->
        <Border Grid.Column="1" Style="{StaticResource CardStyle}" Margin="10,20,20,20">
            <StackPanel>
                <Grid Margin="0,0,0,15">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>
                    
                    <TextBlock Grid.Column="0" Text="الطلاب المتاحون" 
                             FontSize="18" FontWeight="Bold" Foreground="#4CAF50"/>
                    
                    <TextBlock Grid.Column="1" Name="TxtAvailableCount" Text="(0 طالب)" 
                             FontSize="14" Foreground="#757575" VerticalAlignment="Bottom"/>
                </Grid>

                <!-- Search Box -->
                <TextBox Name="TxtSearch" 
                       Padding="10" FontSize="14" Margin="0,0,0,15"
                       BorderBrush="#E0E0E0" BorderThickness="1"
                       TextChanged="TxtSearch_TextChanged">
                    <TextBox.Style>
                        <Style TargetType="TextBox">
                            <Setter Property="Template">
                                <Setter.Value>
                                    <ControlTemplate TargetType="TextBox">
                                        <Border Background="{TemplateBinding Background}" 
                                              BorderBrush="{TemplateBinding BorderBrush}" 
                                              BorderThickness="{TemplateBinding BorderThickness}"
                                              CornerRadius="5">
                                            <Grid>
                                                <ScrollViewer x:Name="PART_ContentHost" 
                                                            VerticalAlignment="Center" 
                                                            Margin="10,0"/>
                                                <TextBlock Text="البحث عن طالب..." 
                                                         Foreground="#999" 
                                                         VerticalAlignment="Center" 
                                                         Margin="10,0"
                                                         IsHitTestVisible="False">
                                                    <TextBlock.Style>
                                                        <Style TargetType="TextBlock">
                                                            <Setter Property="Visibility" Value="Collapsed"/>
                                                            <Style.Triggers>
                                                                <DataTrigger Binding="{Binding Text, RelativeSource={RelativeSource TemplatedParent}}" Value="">
                                                                    <Setter Property="Visibility" Value="Visible"/>
                                                                </DataTrigger>
                                                            </Style.Triggers>
                                                        </Style>
                                                    </TextBlock.Style>
                                                </TextBlock>
                                            </Grid>
                                        </Border>
                                    </ControlTemplate>
                                </Setter.Value>
                            </Setter>
                        </Style>
                    </TextBox.Style>
                </TextBox>

                <DataGrid Name="DgAvailableStudents" 
                        AutoGenerateColumns="False" 
                        CanUserAddRows="False" 
                        CanUserDeleteRows="False"
                        IsReadOnly="True"
                        GridLinesVisibility="Horizontal"
                        HeadersVisibility="Column"
                        BorderThickness="0"
                        Background="Transparent"
                        RowBackground="White"
                        AlternatingRowBackground="#F5F5F5"
                        Height="350"
                        SelectionMode="Single">
                    
                    <DataGrid.Columns>
                        <DataGridTextColumn Header="اسم الطالب" Binding="{Binding Name}" Width="150"/>
                        <DataGridTextColumn Header="الصف" Binding="{Binding Grade}" Width="80"/>
                        <DataGridTextColumn Header="رقم الهاتف" Binding="{Binding Phone}" Width="120"/>
                        
                        <DataGridTemplateColumn Header="الإجراءات" Width="100">
                            <DataGridTemplateColumn.CellTemplate>
                                <DataTemplate>
                                    <Button Content="إضافة" Style="{StaticResource SuccessButtonStyle}" 
                                          Click="BtnAddStudent_Click" Tag="{Binding Id}"/>
                                </DataTemplate>
                            </DataGridTemplateColumn.CellTemplate>
                        </DataGridTemplateColumn>
                    </DataGrid.Columns>
                </DataGrid>

                <StackPanel Orientation="Horizontal" HorizontalAlignment="Center" Margin="0,20,0,0">
                    <Button Name="BtnAddSelected" Content="إضافة المحدد" Style="{StaticResource SuccessButtonStyle}"
                          Click="BtnAddSelected_Click"/>
                    <Button Name="BtnRefresh" Content="🔄 تحديث"
                          Background="#757575" Foreground="White"
                          Padding="15,8" Margin="5" BorderThickness="0"
                          Click="BtnRefresh_Click">
                        <Button.Template>
                            <ControlTemplate TargetType="Button">
                                <Border Background="{TemplateBinding Background}"
                                      CornerRadius="5">
                                    <ContentPresenter HorizontalAlignment="Center"
                                                    VerticalAlignment="Center"
                                                    Margin="{TemplateBinding Padding}"/>
                                </Border>
                            </ControlTemplate>
                        </Button.Template>
                    </Button>
                </StackPanel>
            </StackPanel>
        </Border>

        <!-- Action Buttons -->
        <Border Grid.Column="0" Grid.ColumnSpan="2" 
              Background="White" CornerRadius="8" Padding="20" 
              Margin="20,0,20,20" VerticalAlignment="Bottom">
            <Border.Effect>
                <DropShadowEffect Color="Gray" Direction="270" ShadowDepth="2" Opacity="0.3" BlurRadius="5"/>
            </Border.Effect>
            
            <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                <Button Name="BtnSave" Content="حفظ التغييرات" Style="{StaticResource PrimaryButtonStyle}" 
                      Padding="20,10" FontSize="14" Click="BtnSave_Click"/>
                <Button Name="BtnCancel" Content="إلغاء"
                      Background="#757575" Foreground="White"
                      Padding="20,10" Margin="10,0,0,0" BorderThickness="0"
                      FontSize="14" Click="BtnCancel_Click">
                    <Button.Template>
                        <ControlTemplate TargetType="Button">
                            <Border Background="{TemplateBinding Background}"
                                  CornerRadius="5">
                                <ContentPresenter HorizontalAlignment="Center"
                                                VerticalAlignment="Center"
                                                Margin="{TemplateBinding Padding}"/>
                            </Border>
                        </ControlTemplate>
                    </Button.Template>
                </Button>
            </StackPanel>
        </Border>
    </Grid>
</Window>
