<Page x:Class="EduTrackForWin.Views.OverdueManagementPage"
      xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
      xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
      xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
      xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
      xmlns:local="clr-namespace:EduTrackForWin.Views"
      mc:Ignorable="d" 
      d:DesignHeight="800" d:DesignWidth="1200"
      Title="OverdueManagementPage">

    <Page.Resources>
        <!-- Styles -->
        <Style x:Key="HeaderStyle" TargetType="TextBlock">
            <Setter Property="FontSize" Value="24"/>
            <Setter Property="FontWeight" Value="Bold"/>
            <Setter Property="Foreground" Value="#D32F2F"/>
            <Setter Property="Margin" Value="0,0,0,20"/>
        </Style>

        <Style x:Key="StatCardStyle" TargetType="Border">
            <Setter Property="Background" Value="White"/>
            <Setter Property="CornerRadius" Value="8"/>
            <Setter Property="Padding" Value="20"/>
            <Setter Property="Margin" Value="10"/>
            <Setter Property="Effect">
                <Setter.Value>
                    <DropShadowEffect Color="Gray" Direction="270" ShadowDepth="2" Opacity="0.2" BlurRadius="8"/>
                </Setter.Value>
            </Setter>
        </Style>

        <Style x:Key="PrimaryButtonStyle" TargetType="Button">
            <Setter Property="Background" Value="#D32F2F"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Padding" Value="15,8"/>
            <Setter Property="Margin" Value="5"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}" 
                              CornerRadius="5">
                            <ContentPresenter HorizontalAlignment="Center" 
                                            VerticalAlignment="Center"
                                            Margin="{TemplateBinding Padding}"/>
                        </Border>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <Style x:Key="SecondaryButtonStyle" TargetType="Button">
            <Setter Property="Background" Value="#FF9800"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Padding" Value="15,8"/>
            <Setter Property="Margin" Value="5"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}" 
                              CornerRadius="5">
                            <ContentPresenter HorizontalAlignment="Center" 
                                            VerticalAlignment="Center"
                                            Margin="{TemplateBinding Padding}"/>
                        </Border>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>
    </Page.Resources>

    <Grid Background="#F5F5F5">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <Border Grid.Row="0" Background="White" Padding="20" Margin="20,20,20,10">
            <Border.Effect>
                <DropShadowEffect Color="Gray" Direction="270" ShadowDepth="1" Opacity="0.2" BlurRadius="5"/>
            </Border.Effect>
            
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <StackPanel Grid.Column="0">
                    <TextBlock Text="⚠️ إدارة المتأخرات المالية" Style="{StaticResource HeaderStyle}"/>
                    <TextBlock Text="متابعة وإدارة الطلاب المتأخرين في الدفع" 
                             FontSize="16" Foreground="#424242"/>
                </StackPanel>

                <StackPanel Grid.Column="1" Orientation="Horizontal">
                    <Button Name="BtnApplyLateFees" Content="تطبيق غرامات التأخير" 
                          Style="{StaticResource PrimaryButtonStyle}" Click="BtnApplyLateFees_Click"/>
                    <Button Name="BtnSendReminders" Content="إرسال تذكيرات" 
                          Style="{StaticResource SecondaryButtonStyle}" Click="BtnSendReminders_Click"/>
                    <Button Name="BtnGenerateReport" Content="تقرير المتأخرات" 
                          Background="#2196F3" Foreground="White" BorderThickness="0" 
                          Padding="15,8" Margin="5" Cursor="Hand"
                          Click="BtnGenerateReport_Click"/>
                </StackPanel>
            </Grid>
        </Border>

        <!-- Statistics -->
        <Border Grid.Row="1" Background="White" Padding="20" Margin="20,10,20,10">
            <Border.Effect>
                <DropShadowEffect Color="Gray" Direction="270" ShadowDepth="1" Opacity="0.2" BlurRadius="5"/>
            </Border.Effect>
            
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>

                <!-- Total Overdue -->
                <Border Grid.Column="0" Style="{StaticResource StatCardStyle}" Background="#FFEBEE">
                    <StackPanel>
                        <TextBlock Text="⚠️" FontSize="32" HorizontalAlignment="Center" Margin="0,0,0,10"/>
                        <TextBlock Text="إجمالي المتأخرين" FontWeight="Bold" HorizontalAlignment="Center" 
                                 Foreground="#D32F2F" FontSize="14"/>
                        <TextBlock Name="TxtTotalOverdue" Text="0" FontWeight="Bold" 
                                 HorizontalAlignment="Center" Foreground="#B71C1C" FontSize="18" Margin="0,5,0,0"/>
                    </StackPanel>
                </Border>

                <!-- Critical Overdue -->
                <Border Grid.Column="1" Style="{StaticResource StatCardStyle}" Background="#FCE4EC">
                    <StackPanel>
                        <TextBlock Text="🚨" FontSize="32" HorizontalAlignment="Center" Margin="0,0,0,10"/>
                        <TextBlock Text="متأخرات حرجة" FontWeight="Bold" HorizontalAlignment="Center" 
                                 Foreground="#E91E63" FontSize="14"/>
                        <TextBlock Name="TxtCriticalOverdue" Text="0" FontWeight="Bold" 
                                 HorizontalAlignment="Center" Foreground="#AD1457" FontSize="18" Margin="0,5,0,0"/>
                    </StackPanel>
                </Border>

                <!-- Total Amount -->
                <Border Grid.Column="2" Style="{StaticResource StatCardStyle}" Background="#FFF3E0">
                    <StackPanel>
                        <TextBlock Text="💰" FontSize="32" HorizontalAlignment="Center" Margin="0,0,0,10"/>
                        <TextBlock Text="إجمالي المبالغ" FontWeight="Bold" HorizontalAlignment="Center" 
                                 Foreground="#F57C00" FontSize="14"/>
                        <TextBlock Name="TxtTotalAmount" Text="0 ج.م" FontWeight="Bold" 
                                 HorizontalAlignment="Center" Foreground="#E65100" FontSize="18" Margin="0,5,0,0"/>
                    </StackPanel>
                </Border>

                <!-- Late Fees -->
                <Border Grid.Column="3" Style="{StaticResource StatCardStyle}" Background="#F3E5F5">
                    <StackPanel>
                        <TextBlock Text="📈" FontSize="32" HorizontalAlignment="Center" Margin="0,0,0,10"/>
                        <TextBlock Text="غرامات التأخير" FontWeight="Bold" HorizontalAlignment="Center" 
                                 Foreground="#7B1FA2" FontSize="14"/>
                        <TextBlock Name="TxtLateFees" Text="0 ج.م" FontWeight="Bold" 
                                 HorizontalAlignment="Center" Foreground="#4A148C" FontSize="18" Margin="0,5,0,0"/>
                    </StackPanel>
                </Border>

                <!-- Average Days -->
                <Border Grid.Column="4" Style="{StaticResource StatCardStyle}" Background="#E8F5E8">
                    <StackPanel>
                        <TextBlock Text="📅" FontSize="32" HorizontalAlignment="Center" Margin="0,0,0,10"/>
                        <TextBlock Text="متوسط أيام التأخير" FontWeight="Bold" HorizontalAlignment="Center" 
                                 Foreground="#2E7D32" FontSize="14"/>
                        <TextBlock Name="TxtAverageDays" Text="0 يوم" FontWeight="Bold" 
                                 HorizontalAlignment="Center" Foreground="#1B5E20" FontSize="18" Margin="0,5,0,0"/>
                    </StackPanel>
                </Border>
            </Grid>
        </Border>

        <!-- Overdue Students List -->
        <Border Grid.Row="2" Background="White" Padding="20" Margin="20,10,20,20">
            <Border.Effect>
                <DropShadowEffect Color="Gray" Direction="270" ShadowDepth="1" Opacity="0.2" BlurRadius="5"/>
            </Border.Effect>
            
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="*"/>
                </Grid.RowDefinitions>

                <!-- Filters -->
                <StackPanel Grid.Row="0" Orientation="Horizontal" Margin="0,0,0,15">
                    <TextBlock Text="تصفية:" VerticalAlignment="Center" FontWeight="SemiBold" Margin="0,0,10,0"/>
                    
                    <ComboBox Name="CmbSeverityFilter" MinWidth="150" Margin="5"
                            SelectionChanged="Filter_Changed">
                        <ComboBoxItem Content="جميع المستويات" IsSelected="True"/>
                        <ComboBoxItem Content="حرج"/>
                        <ComboBoxItem Content="عالي"/>
                        <ComboBoxItem Content="متوسط"/>
                        <ComboBoxItem Content="منخفض"/>
                    </ComboBox>

                    <Button Name="BtnRefresh" Content="تحديث" 
                          Background="#4CAF50" Foreground="White" BorderThickness="0" 
                          Padding="15,8" Margin="10,0,0,0" Cursor="Hand"
                          Click="BtnRefresh_Click"/>
                </StackPanel>

                <!-- DataGrid -->
                <DataGrid Name="DgOverdueStudents" Grid.Row="1"
                        AutoGenerateColumns="False" 
                        CanUserAddRows="False"
                        CanUserDeleteRows="False"
                        IsReadOnly="True"
                        GridLinesVisibility="Horizontal"
                        HeadersVisibility="Column"
                        AlternatingRowBackground="#F9F9F9"
                        RowBackground="White"
                        BorderBrush="#E0E0E0"
                        BorderThickness="1">
                    
                    <DataGrid.Columns>
                        <!-- Student Name -->
                        <DataGridTextColumn Header="اسم الطالب" Binding="{Binding StudentName}" Width="150"/>
                        
                        <!-- Group -->
                        <DataGridTextColumn Header="المجموعة" Binding="{Binding GroupName}" Width="120"/>
                        
                        <!-- Phone -->
                        <DataGridTextColumn Header="الهاتف" Binding="{Binding Phone}" Width="120"/>
                        
                        <!-- Total Due -->
                        <DataGridTextColumn Header="المبلغ المستحق" Width="120">
                            <DataGridTextColumn.Binding>
                                <Binding Path="TotalDue" StringFormat="{}{0:C}"/>
                            </DataGridTextColumn.Binding>
                        </DataGridTextColumn>
                        
                        <!-- Overdue Days -->
                        <DataGridTextColumn Header="أيام التأخير" Binding="{Binding OverdueDays}" Width="100"/>
                        
                        <!-- Severity -->
                        <DataGridTextColumn Header="المستوى" Binding="{Binding SeverityLevel}" Width="80"/>
                        
                        <!-- Late Fee -->
                        <DataGridTextColumn Header="غرامة التأخير" Width="120">
                            <DataGridTextColumn.Binding>
                                <Binding Path="LateFee" StringFormat="{}{0:C}"/>
                            </DataGridTextColumn.Binding>
                        </DataGridTextColumn>
                        
                        <!-- Last Payment -->
                        <DataGridTextColumn Header="آخر دفعة" Width="100">
                            <DataGridTextColumn.Binding>
                                <Binding Path="LastPaymentDate" StringFormat="{}{0:yyyy/MM/dd}"/>
                            </DataGridTextColumn.Binding>
                        </DataGridTextColumn>
                        
                        <!-- Actions -->
                        <DataGridTemplateColumn Header="الإجراءات" Width="200">
                            <DataGridTemplateColumn.CellTemplate>
                                <DataTemplate>
                                    <StackPanel Orientation="Horizontal">
                                        <Button Content="دفعة" 
                                              Background="#4CAF50" 
                                              Foreground="White" 
                                              BorderThickness="0" 
                                              Padding="8,4" 
                                              Margin="2"
                                              Cursor="Hand"
                                              Click="BtnPayment_Click"/>
                                        <Button Content="تذكير" 
                                              Background="#FF9800" 
                                              Foreground="White" 
                                              BorderThickness="0" 
                                              Padding="8,4" 
                                              Margin="2"
                                              Cursor="Hand"
                                              Click="BtnReminder_Click"/>
                                        <Button Content="غرامة" 
                                              Background="#F44336" 
                                              Foreground="White" 
                                              BorderThickness="0" 
                                              Padding="8,4" 
                                              Margin="2"
                                              Cursor="Hand"
                                              Click="BtnLateFee_Click"/>
                                    </StackPanel>
                                </DataTemplate>
                            </DataGridTemplateColumn.CellTemplate>
                        </DataGridTemplateColumn>
                    </DataGrid.Columns>
                </DataGrid>
            </Grid>
        </Border>
    </Grid>
</Page>
