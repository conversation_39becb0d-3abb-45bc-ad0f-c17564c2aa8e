<Page x:Class="EduTrackForWin.Views.ReportsPage"
      xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
      xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
      xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
      xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
      xmlns:local="clr-namespace:EduTrackForWin.Views"
      mc:Ignorable="d" 
      d:DesignHeight="450" d:DesignWidth="800"
      Title="ReportsPage">

    <Page.Resources>
        <!-- Card Style -->
        <Style x:Key="CardStyle" TargetType="Border">
            <Setter Property="Background" Value="{StaticResource SurfaceColor}"/>
            <Setter Property="CornerRadius" Value="8"/>
            <Setter Property="Padding" Value="20"/>
            <Setter Property="Margin" Value="10"/>
        </Style>

        <!-- Report Card Style -->
        <Style x:Key="ReportCardStyle" TargetType="Border">
            <Setter Property="Background" Value="{StaticResource SurfaceColor}"/>
            <Setter Property="CornerRadius" Value="8"/>
            <Setter Property="Padding" Value="20"/>
            <Setter Property="Margin" Value="10"/>
            <Setter Property="Cursor" Value="Hand"/>
        </Style>
    </Page.Resources>

    <Grid Background="#F5F5F5" Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <Border Grid.Row="0" Style="{StaticResource CardStyle}" Background="#E8EAF6">
            <StackPanel>
                <TextBlock Text="📊 التقارير والإحصائيات" FontSize="24" FontWeight="Bold" Foreground="#3F51B5"/>
                <TextBlock Text="إنشاء وعرض التقارير المختلفة للمركز التعليمي" FontSize="14" Foreground="#424242" Margin="0,5,0,0"/>
            </StackPanel>
        </Border>

        <!-- Reports Grid -->
        <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                </Grid.RowDefinitions>

                <!-- Financial Reports -->
                <StackPanel Grid.Row="0">
                    <TextBlock Text="التقارير المالية" FontSize="18" FontWeight="Bold" 
                             Foreground="#4CAF50" Margin="10,20,10,10"/>
                    
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>

                        <!-- Monthly Financial Report -->
                        <Border Grid.Column="0" Style="{StaticResource ReportCardStyle}" 
                              MouseLeftButtonUp="FinancialReport_Click" Tag="Monthly">
                            <StackPanel HorizontalAlignment="Center">
                                <TextBlock Text="💰" FontSize="48" HorizontalAlignment="Center"/>
                                <TextBlock Text="التقرير المالي الشهري" FontSize="16" FontWeight="Bold" 
                                         HorizontalAlignment="Center" Margin="0,10,0,5"/>
                                <TextBlock Text="إيرادات ومصروفات الشهر الحالي" FontSize="12" 
                                         Foreground="#757575" HorizontalAlignment="Center" TextWrapping="Wrap"/>
                            </StackPanel>
                        </Border>

                        <!-- Yearly Financial Report -->
                        <Border Grid.Column="1" Style="{StaticResource ReportCardStyle}" 
                              MouseLeftButtonUp="FinancialReport_Click" Tag="Yearly">
                            <StackPanel HorizontalAlignment="Center">
                                <TextBlock Text="📈" FontSize="48" HorizontalAlignment="Center"/>
                                <TextBlock Text="التقرير المالي السنوي" FontSize="16" FontWeight="Bold" 
                                         HorizontalAlignment="Center" Margin="0,10,0,5"/>
                                <TextBlock Text="إجمالي الإيرادات والمصروفات السنوية" FontSize="12" 
                                         Foreground="#757575" HorizontalAlignment="Center" TextWrapping="Wrap"/>
                            </StackPanel>
                        </Border>

                        <!-- Outstanding Dues Report -->
                        <Border Grid.Column="2" Style="{StaticResource ReportCardStyle}" 
                              MouseLeftButtonUp="DuesReport_Click">
                            <StackPanel HorizontalAlignment="Center">
                                <TextBlock Text="⚠️" FontSize="48" HorizontalAlignment="Center"/>
                                <TextBlock Text="تقرير الديون المستحقة" FontSize="16" FontWeight="Bold" 
                                         HorizontalAlignment="Center" Margin="0,10,0,5"/>
                                <TextBlock Text="الطلاب المتأخرين في الدفع" FontSize="12" 
                                         Foreground="#757575" HorizontalAlignment="Center" TextWrapping="Wrap"/>
                            </StackPanel>
                        </Border>
                    </Grid>
                </StackPanel>

                <!-- Academic Reports -->
                <StackPanel Grid.Row="1">
                    <TextBlock Text="التقارير الأكاديمية" FontSize="18" FontWeight="Bold" 
                             Foreground="#2196F3" Margin="10,20,10,10"/>
                    
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>

                        <!-- Attendance Report -->
                        <Border Grid.Column="0" Style="{StaticResource ReportCardStyle}" 
                              MouseLeftButtonUp="AttendanceReport_Click">
                            <StackPanel HorizontalAlignment="Center">
                                <TextBlock Text="✅" FontSize="48" HorizontalAlignment="Center"/>
                                <TextBlock Text="تقرير الحضور" FontSize="16" FontWeight="Bold" 
                                         HorizontalAlignment="Center" Margin="0,10,0,5"/>
                                <TextBlock Text="إحصائيات الحضور والغياب" FontSize="12" 
                                         Foreground="#757575" HorizontalAlignment="Center" TextWrapping="Wrap"/>
                            </StackPanel>
                        </Border>

                        <!-- Students Report -->
                        <Border Grid.Column="1" Style="{StaticResource ReportCardStyle}" 
                              MouseLeftButtonUp="StudentsReport_Click">
                            <StackPanel HorizontalAlignment="Center">
                                <TextBlock Text="👦" FontSize="48" HorizontalAlignment="Center"/>
                                <TextBlock Text="تقرير الطلاب" FontSize="16" FontWeight="Bold" 
                                         HorizontalAlignment="Center" Margin="0,10,0,5"/>
                                <TextBlock Text="إحصائيات وبيانات الطلاب" FontSize="12" 
                                         Foreground="#757575" HorizontalAlignment="Center" TextWrapping="Wrap"/>
                            </StackPanel>
                        </Border>

                        <!-- Teachers Report -->
                        <Border Grid.Column="2" Style="{StaticResource ReportCardStyle}" 
                              MouseLeftButtonUp="TeachersReport_Click">
                            <StackPanel HorizontalAlignment="Center">
                                <TextBlock Text="🧑‍🏫" FontSize="48" HorizontalAlignment="Center"/>
                                <TextBlock Text="تقرير المعلمين" FontSize="16" FontWeight="Bold" 
                                         HorizontalAlignment="Center" Margin="0,10,0,5"/>
                                <TextBlock Text="أداء وإحصائيات المعلمين" FontSize="12" 
                                         Foreground="#757575" HorizontalAlignment="Center" TextWrapping="Wrap"/>
                            </StackPanel>
                        </Border>
                    </Grid>
                </StackPanel>

                <!-- Administrative Reports -->
                <StackPanel Grid.Row="2">
                    <TextBlock Text="التقارير الإدارية" FontSize="18" FontWeight="Bold" 
                             Foreground="#FF9800" Margin="10,20,10,10"/>
                    
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>

                        <!-- Groups Report -->
                        <Border Grid.Column="0" Style="{StaticResource ReportCardStyle}" 
                              MouseLeftButtonUp="GroupsReport_Click">
                            <StackPanel HorizontalAlignment="Center">
                                <TextBlock Text="🧩" FontSize="48" HorizontalAlignment="Center"/>
                                <TextBlock Text="تقرير المجموعات" FontSize="16" FontWeight="Bold" 
                                         HorizontalAlignment="Center" Margin="0,10,0,5"/>
                                <TextBlock Text="إحصائيات المجموعات والحصص" FontSize="12" 
                                         Foreground="#757575" HorizontalAlignment="Center" TextWrapping="Wrap"/>
                            </StackPanel>
                        </Border>

                        <!-- Sessions Report -->
                        <Border Grid.Column="1" Style="{StaticResource ReportCardStyle}" 
                              MouseLeftButtonUp="SessionsReport_Click">
                            <StackPanel HorizontalAlignment="Center">
                                <TextBlock Text="📅" FontSize="48" HorizontalAlignment="Center"/>
                                <TextBlock Text="تقرير الحصص" FontSize="16" FontWeight="Bold" 
                                         HorizontalAlignment="Center" Margin="0,10,0,5"/>
                                <TextBlock Text="إحصائيات الحصص المنجزة" FontSize="12" 
                                         Foreground="#757575" HorizontalAlignment="Center" TextWrapping="Wrap"/>
                            </StackPanel>
                        </Border>

                        <!-- Custom Report -->
                        <Border Grid.Column="2" Style="{StaticResource ReportCardStyle}" 
                              MouseLeftButtonUp="CustomReport_Click">
                            <StackPanel HorizontalAlignment="Center">
                                <TextBlock Text="⚙️" FontSize="48" HorizontalAlignment="Center"/>
                                <TextBlock Text="تقرير مخصص" FontSize="16" FontWeight="Bold" 
                                         HorizontalAlignment="Center" Margin="0,10,0,5"/>
                                <TextBlock Text="إنشاء تقرير حسب المعايير المحددة" FontSize="12" 
                                         Foreground="#757575" HorizontalAlignment="Center" TextWrapping="Wrap"/>
                            </StackPanel>
                        </Border>
                    </Grid>
                </StackPanel>
            </Grid>
        </ScrollViewer>
    </Grid>
</Page>
