using System.Collections.ObjectModel;
using System.Windows;
using System.Windows.Controls;
using EduTrackForWin.Models;
using EduTrackForWin.Services;

namespace EduTrackForWin.Views
{
    public partial class ManageGroupStudentsWindow : Window
    {
        private readonly Group _group;
        private readonly GroupService _groupService;
        private readonly StudentService _studentService;
        private ObservableCollection<Student> _currentStudents;
        private ObservableCollection<Student> _availableStudents;
        private List<Student> _allAvailableStudents;

        public ManageGroupStudentsWindow(Group group)
        {
            try
            {
                InitializeComponent();
                _group = group;
                _groupService = new GroupService();
                _studentService = new StudentService();
                _currentStudents = new ObservableCollection<Student>();
                _availableStudents = new ObservableCollection<Student>();
                _allAvailableStudents = new List<Student>();

                if (DgCurrentStudents != null) DgCurrentStudents.ItemsSource = _currentStudents;
                if (DgAvailableStudents != null) DgAvailableStudents.ItemsSource = _availableStudents;

                LoadData();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تهيئة نافذة إدارة طلاب المجموعة: {ex.Message}", "خطأ",
                              MessageBoxButton.OK, MessageBoxImage.Error);
                throw;
            }
        }

        private async void LoadData()
        {
            try
            {
                if (TxtGroupName != null) TxtGroupName.Text = $"طلاب مجموعة {_group.Name}";

                await LoadCurrentStudents();
                await LoadAvailableStudents();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل البيانات: {ex.Message}", "خطأ",
                              MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async Task LoadCurrentStudents()
        {
            try
            {
                var students = await _studentService.GetStudentsByGroupAsync(_group.Id);
                
                _currentStudents.Clear();
                foreach (var student in students)
                {
                    _currentStudents.Add(student);
                }

                if (TxtStudentCount != null) TxtStudentCount.Text = $"({_currentStudents.Count} طالب)";
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل طلاب المجموعة: {ex.Message}", "خطأ", 
                              MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async Task LoadAvailableStudents()
        {
            try
            {
                _allAvailableStudents = await _groupService.GetAvailableStudentsForGroupAsync(_group.Id);
                RefreshAvailableStudents();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل الطلاب المتاحين: {ex.Message}", "خطأ", 
                              MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void RefreshAvailableStudents()
        {
            _availableStudents.Clear();
            
            var filteredStudents = _allAvailableStudents.AsEnumerable();

            // Apply search filter
            if (TxtSearch != null && !string.IsNullOrWhiteSpace(TxtSearch.Text))
            {
                var searchTerm = TxtSearch.Text.ToLower();
                filteredStudents = filteredStudents.Where(s =>
                    s.Name.ToLower().Contains(searchTerm) ||
                    s.Phone.Contains(searchTerm) ||
                    s.Grade.ToLower().Contains(searchTerm));
            }

            foreach (var student in filteredStudents)
            {
                _availableStudents.Add(student);
            }

            if (TxtAvailableCount != null) TxtAvailableCount.Text = $"({_availableStudents.Count} طالب)";
        }

        private void TxtSearch_TextChanged(object sender, TextChangedEventArgs e)
        {
            RefreshAvailableStudents();
        }

        private async void BtnAddStudent_Click(object sender, RoutedEventArgs e)
        {
            if (sender is Button button && button.Tag is int studentId)
            {
                try
                {
                    await _groupService.AddStudentToGroupAsync(studentId, _group.Id);
                    
                    // Move student from available to current
                    var student = _availableStudents.FirstOrDefault(s => s.Id == studentId);
                    if (student != null)
                    {
                        _availableStudents.Remove(student);
                        _allAvailableStudents.Remove(student);
                        _currentStudents.Add(student);
                        
                        if (TxtStudentCount != null) TxtStudentCount.Text = $"({_currentStudents.Count} طالب)";
                        if (TxtAvailableCount != null) TxtAvailableCount.Text = $"({_availableStudents.Count} طالب)";
                    }
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"خطأ في إضافة الطالب: {ex.Message}", "خطأ", 
                                  MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
        }

        private async void BtnRemoveStudent_Click(object sender, RoutedEventArgs e)
        {
            if (sender is Button button && button.Tag is int studentId)
            {
                try
                {
                    await _groupService.RemoveStudentFromGroupAsync(studentId);
                    
                    // Move student from current to available
                    var student = _currentStudents.FirstOrDefault(s => s.Id == studentId);
                    if (student != null)
                    {
                        _currentStudents.Remove(student);
                        _allAvailableStudents.Add(student);
                        RefreshAvailableStudents();
                        
                        TxtStudentCount.Text = $"({_currentStudents.Count} طالب)";
                    }
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"خطأ في إزالة الطالب: {ex.Message}", "خطأ", 
                                  MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
        }

        private async void BtnAddSelected_Click(object sender, RoutedEventArgs e)
        {
            if (DgAvailableStudents.SelectedItem is Student selectedStudent)
            {
                try
                {
                    await _groupService.AddStudentToGroupAsync(selectedStudent.Id, _group.Id);
                    
                    _availableStudents.Remove(selectedStudent);
                    _allAvailableStudents.Remove(selectedStudent);
                    _currentStudents.Add(selectedStudent);
                    
                    if (TxtStudentCount != null) TxtStudentCount.Text = $"({_currentStudents.Count} طالب)";
                    if (TxtAvailableCount != null) TxtAvailableCount.Text = $"({_availableStudents.Count} طالب)";
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"خطأ في إضافة الطالب: {ex.Message}", "خطأ", 
                                  MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
            else
            {
                MessageBox.Show("يرجى اختيار طالب من القائمة", "تنبيه", 
                              MessageBoxButton.OK, MessageBoxImage.Information);
            }
        }

        private async void BtnRemoveAll_Click(object sender, RoutedEventArgs e)
        {
            if (_currentStudents.Count == 0)
            {
                MessageBox.Show("لا توجد طلاب في المجموعة", "تنبيه", 
                              MessageBoxButton.OK, MessageBoxImage.Information);
                return;
            }

            var result = MessageBox.Show(
                $"هل أنت متأكد من إزالة جميع الطلاب ({_currentStudents.Count}) من المجموعة؟",
                "تأكيد الإزالة",
                MessageBoxButton.YesNo,
                MessageBoxImage.Warning);

            if (result == MessageBoxResult.Yes)
            {
                try
                {
                    var studentsToRemove = _currentStudents.ToList();
                    
                    foreach (var student in studentsToRemove)
                    {
                        await _groupService.RemoveStudentFromGroupAsync(student.Id);
                        _currentStudents.Remove(student);
                        _allAvailableStudents.Add(student);
                    }
                    
                    RefreshAvailableStudents();
                    TxtStudentCount.Text = $"({_currentStudents.Count} طالب)";
                    
                    MessageBox.Show("تم إزالة جميع الطلاب من المجموعة", "نجح", 
                                  MessageBoxButton.OK, MessageBoxImage.Information);
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"خطأ في إزالة الطلاب: {ex.Message}", "خطأ", 
                                  MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
        }

        private void BtnRefresh_Click(object sender, RoutedEventArgs e)
        {
            LoadData();
        }

        private void BtnSave_Click(object sender, RoutedEventArgs e)
        {
            MessageBox.Show("تم حفظ التغييرات بنجاح", "نجح", 
                          MessageBoxButton.OK, MessageBoxImage.Information);
            DialogResult = true;
            Close();
        }

        private void BtnCancel_Click(object sender, RoutedEventArgs e)
        {
            DialogResult = false;
            Close();
        }

        protected override void OnClosed(EventArgs e)
        {
            _groupService?.Dispose();
            _studentService?.Dispose();
            base.OnClosed(e);
        }
    }
}
