<Window x:Class="EduTrackForWin.Views.AddEditTeacherWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:EduTrackForWin.Views"
        mc:Ignorable="d"
        Title="إضافة/تعديل معلم" Height="650" Width="500"
        WindowStartupLocation="CenterOwner"
        ResizeMode="NoResize">

    <Window.Resources>
        <!-- Input Field Style -->
        <Style x:Key="InputFieldStyle" TargetType="TextBox">
            <Setter Property="Padding" Value="10"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="BorderBrush" Value="#E0E0E0"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="Margin" Value="0,5,0,15"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="TextBox">
                        <Border Background="{TemplateBinding Background}" 
                              BorderBrush="{TemplateBinding BorderBrush}" 
                              BorderThickness="{TemplateBinding BorderThickness}"
                              CornerRadius="5">
                            <ScrollViewer x:Name="PART_ContentHost" 
                                        VerticalAlignment="Center" 
                                        Margin="{TemplateBinding Padding}"/>
                        </Border>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <!-- ComboBox Style -->
        <Style x:Key="ComboBoxStyle" TargetType="ComboBox">
            <Setter Property="Padding" Value="10"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="BorderBrush" Value="#E0E0E0"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="Margin" Value="0,5,0,15"/>
        </Style>

        <!-- Label Style -->
        <Style x:Key="LabelStyle" TargetType="TextBlock">
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="FontWeight" Value="SemiBold"/>
            <Setter Property="Foreground" Value="#212121"/>
            <Setter Property="Margin" Value="0,10,0,5"/>
        </Style>

        <!-- Button Styles -->
        <Style x:Key="PrimaryButtonStyle" TargetType="Button">
            <Setter Property="Background" Value="#2196F3"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Padding" Value="20,10"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}" 
                              CornerRadius="5">
                            <ContentPresenter HorizontalAlignment="Center" 
                                            VerticalAlignment="Center"
                                            Margin="{TemplateBinding Padding}"/>
                        </Border>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <Style x:Key="SecondaryButtonStyle" TargetType="Button">
            <Setter Property="Background" Value="#757575"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Padding" Value="20,10"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}" 
                              CornerRadius="5">
                            <ContentPresenter HorizontalAlignment="Center" 
                                            VerticalAlignment="Center"
                                            Margin="{TemplateBinding Padding}"/>
                        </Border>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>
    </Window.Resources>

    <Grid Margin="30">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <StackPanel Grid.Row="0" Margin="0,0,0,20">
            <TextBlock Name="TxtTitle" Text="إضافة معلم جديد" FontSize="24" FontWeight="Bold" 
                     Foreground="#212121" HorizontalAlignment="Center"/>
            <TextBlock Text="املأ البيانات التالية لإضافة معلم جديد" FontSize="14" 
                     Foreground="#757575" HorizontalAlignment="Center" Margin="0,5,0,0"/>
        </StackPanel>

        <!-- Form -->
        <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto">
            <StackPanel>
                <!-- Teacher Name -->
                <TextBlock Text="اسم المعلم *" Style="{StaticResource LabelStyle}"/>
                <TextBox Name="TxtName" Style="{StaticResource InputFieldStyle}" 
                       MaxLength="100"/>

                <!-- Specialization -->
                <TextBlock Text="التخصص" Style="{StaticResource LabelStyle}"/>
                <ComboBox Name="CmbSpecialization" Style="{StaticResource ComboBoxStyle}" IsEditable="True">
                    <ComboBoxItem Content="الرياضيات"/>
                    <ComboBoxItem Content="العلوم"/>
                    <ComboBoxItem Content="اللغة العربية"/>
                    <ComboBoxItem Content="اللغة الإنجليزية"/>
                    <ComboBoxItem Content="الفيزياء"/>
                    <ComboBoxItem Content="الكيمياء"/>
                    <ComboBoxItem Content="الأحياء"/>
                    <ComboBoxItem Content="التاريخ"/>
                    <ComboBoxItem Content="الجغرافيا"/>
                    <ComboBoxItem Content="التربية الإسلامية"/>
                    <ComboBoxItem Content="الحاسوب"/>
                    <ComboBoxItem Content="الفنون"/>
                    <ComboBoxItem Content="الموسيقى"/>
                    <ComboBoxItem Content="التربية البدنية"/>
                </ComboBox>

                <!-- Phone -->
                <TextBlock Text="رقم الهاتف" Style="{StaticResource LabelStyle}"/>
                <TextBox Name="TxtPhone" Style="{StaticResource InputFieldStyle}" 
                       MaxLength="20"/>

                <!-- Email -->
                <TextBlock Text="البريد الإلكتروني" Style="{StaticResource LabelStyle}"/>
                <TextBox Name="TxtEmail" Style="{StaticResource InputFieldStyle}"
                       MaxLength="100"/>

                <!-- Salary Type -->
                <TextBlock Text="نوع الراتب" Style="{StaticResource LabelStyle}"/>
                <StackPanel Orientation="Horizontal" Margin="0,5,0,15">
                    <RadioButton Name="RbFixedSalary" Content="راتب ثابت شهري" IsChecked="True" 
                               Margin="0,0,20,0" Checked="SalaryType_Changed"/>
                    <RadioButton Name="RbPerSession" Content="راتب لكل حصة" 
                               Checked="SalaryType_Changed"/>
                </StackPanel>

                <!-- Monthly Salary -->
                <StackPanel Name="PnlMonthlySalary">
                    <TextBlock Text="الراتب الشهري (ج.م)" Style="{StaticResource LabelStyle}"/>
                    <TextBox Name="TxtMonthlySalary" Style="{StaticResource InputFieldStyle}" 
                           Text="0"/>
                </StackPanel>

                <!-- Per Session Salary -->
                <StackPanel Name="PnlPerSessionSalary" Visibility="Collapsed">
                    <TextBlock Text="الراتب لكل حصة (ج.م)" Style="{StaticResource LabelStyle}"/>
                    <TextBox Name="TxtSalaryPerSession" Style="{StaticResource InputFieldStyle}" 
                           Text="0"/>
                </StackPanel>

                <!-- Notes -->
                <TextBlock Text="ملاحظات" Style="{StaticResource LabelStyle}"/>
                <TextBox Name="TxtNotes" Style="{StaticResource InputFieldStyle}" 
                       MaxLength="500" Height="80" TextWrapping="Wrap" 
                       VerticalScrollBarVisibility="Auto"/>
            </StackPanel>
        </ScrollViewer>

        <!-- Buttons -->
        <StackPanel Grid.Row="2" Orientation="Horizontal" HorizontalAlignment="Center" 
                  Margin="0,20,0,0">
            <Button Name="BtnSave" Content="حفظ" Style="{StaticResource PrimaryButtonStyle}" 
                  Margin="0,0,10,0" Click="BtnSave_Click"/>
            <Button Name="BtnCancel" Content="إلغاء" Style="{StaticResource SecondaryButtonStyle}" 
                  Margin="10,0,0,0" Click="BtnCancel_Click"/>
        </StackPanel>
    </Grid>
</Window>
