using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using EduTrackForWin.Data;
using EduTrackForWin.Models;
using EduTrackForWin.Services;

namespace EduTrackForWin.Views
{
    /// <summary>
    /// Interaction logic for AddEditPaymentWindow.xaml
    /// </summary>
    public partial class AddEditPaymentWindow : Window
    {
        private readonly EduTrackDbContext _context;
        private readonly PaymentService _paymentService;
        private readonly StudentService _studentService;
        private readonly Payment? _currentPayment;
        private readonly bool _isEditMode;
        private readonly bool _isViewOnly;
        private List<Student> _students;

        public AddEditPaymentWindow(Payment? payment = null, bool viewOnly = false)
        {
            try
            {
                InitializeComponent();
                _context = new EduTrackDbContext();
                _paymentService = new PaymentService();
                _studentService = new StudentService();
                _currentPayment = payment;
                _isEditMode = payment != null;
                _isViewOnly = viewOnly;
                _students = new List<Student>();

                InitializeWindow();
                LoadData();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تهيئة نافذة الدفعة: {ex.Message}", "خطأ", 
                              MessageBoxButton.OK, MessageBoxImage.Error);
                throw;
            }
        }

        private void InitializeWindow()
        {
            try
            {
                if (_isViewOnly)
                {
                    if (TxtTitle != null) TxtTitle.Text = "عرض تفاصيل الدفعة";
                    Title = "عرض تفاصيل الدفعة";
                    if (BtnSave != null) BtnSave.Content = "موافق";
                    
                    // Disable all inputs in view mode
                    if (CmbStudent != null) CmbStudent.IsEnabled = false;
                    if (CmbPaymentType != null) CmbPaymentType.IsEnabled = false;
                    if (TxtAmount != null) TxtAmount.IsReadOnly = true;
                    if (DpPaymentDate != null) DpPaymentDate.IsEnabled = false;
                    if (CmbPaymentMethod != null) CmbPaymentMethod.IsEnabled = false;
                    if (TxtNotes != null) TxtNotes.IsReadOnly = true;
                }
                else if (_isEditMode && _currentPayment != null)
                {
                    if (TxtTitle != null) TxtTitle.Text = "تعديل بيانات الدفعة";
                    Title = "تعديل بيانات الدفعة";
                    if (BtnSave != null) BtnSave.Content = "تحديث";
                }
                else
                {
                    if (TxtTitle != null) TxtTitle.Text = "إضافة دفعة جديدة";
                    Title = "إضافة دفعة جديدة";
                    if (BtnSave != null) BtnSave.Content = "حفظ";
                    if (DpPaymentDate != null) DpPaymentDate.SelectedDate = DateTime.Today;
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تهيئة النافذة: {ex.Message}", "خطأ", 
                              MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async void LoadData()
        {
            try
            {
                await LoadStudents();
                
                if (_isEditMode && _currentPayment != null)
                {
                    LoadPaymentData();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل البيانات: {ex.Message}", "خطأ", 
                              MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async Task LoadStudents()
        {
            try
            {
                _students = await _studentService.GetAllStudentsAsync();
                
                if (CmbStudent != null)
                {
                    CmbStudent.ItemsSource = _students;
                    if (_students.Any())
                    {
                        CmbStudent.SelectedIndex = 0;
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل الطلاب: {ex.Message}", "خطأ", 
                              MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void LoadPaymentData()
        {
            try
            {
                if (_currentPayment == null) return;

                // Set student
                if (CmbStudent != null && _students.Any())
                {
                    var student = _students.FirstOrDefault(s => s.Id == _currentPayment.StudentId);
                    if (student != null)
                    {
                        CmbStudent.SelectedItem = student;
                    }
                }

                // Set payment type
                if (CmbPaymentType != null)
                {
                    foreach (ComboBoxItem item in CmbPaymentType.Items)
                    {
                        if (item.Content.ToString() == _currentPayment.TypeName)
                        {
                            CmbPaymentType.SelectedItem = item;
                            break;
                        }
                    }
                }

                // Set amount
                if (TxtAmount != null) TxtAmount.Text = _currentPayment.Amount.ToString("F2");

                // Set payment date
                if (DpPaymentDate != null) DpPaymentDate.SelectedDate = _currentPayment.PaymentDate;

                // Set payment method
                if (CmbPaymentMethod != null && !string.IsNullOrEmpty(_currentPayment.PaymentMethod))
                {
                    foreach (ComboBoxItem item in CmbPaymentMethod.Items)
                    {
                        if (item.Content.ToString() == _currentPayment.PaymentMethod)
                        {
                            CmbPaymentMethod.SelectedItem = item;
                            break;
                        }
                    }
                }

                // Set notes
                if (TxtNotes != null) TxtNotes.Text = _currentPayment.Notes ?? "";
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل بيانات الدفعة: {ex.Message}", "خطأ", 
                              MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async void BtnSave_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (_isViewOnly)
                {
                    DialogResult = true;
                    Close();
                    return;
                }

                if (!ValidateInput())
                    return;

                var payment = _isEditMode ? _currentPayment : new Payment();
                if (payment == null) return;

                // Update payment properties
                if (CmbStudent?.SelectedItem is Student selectedStudent)
                {
                    payment.StudentId = selectedStudent.Id;
                }

                if (CmbPaymentType?.SelectedItem is ComboBoxItem paymentTypeItem)
                {
                    var paymentTypeName = paymentTypeItem.Content.ToString();
                    payment.Type = paymentTypeName switch
                    {
                        "اشتراك شهري" => PaymentType.Subscription,
                        "رسوم تسجيل" => PaymentType.Other,
                        "رسوم إضافية" => PaymentType.Other,
                        "خصم" => PaymentType.Discount,
                        "غرامة تأخير" => PaymentType.Fine,
                        _ => PaymentType.Other
                    };
                }

                if (decimal.TryParse(TxtAmount?.Text, out var amount))
                {
                    payment.Amount = amount;
                }

                if (DpPaymentDate?.SelectedDate.HasValue == true)
                {
                    payment.PaymentDate = DpPaymentDate.SelectedDate.Value;
                }

                if (CmbPaymentMethod?.SelectedItem is ComboBoxItem paymentMethodItem)
                {
                    payment.PaymentMethod = paymentMethodItem.Content.ToString();
                }

                payment.Notes = TxtNotes?.Text?.Trim() ?? "";

                if (_isEditMode)
                {
                    await _paymentService.UpdatePaymentAsync(payment);
                    MessageBox.Show("تم تحديث الدفعة بنجاح", "تم التحديث", 
                                  MessageBoxButton.OK, MessageBoxImage.Information);
                }
                else
                {
                    await _paymentService.AddPaymentAsync(payment);
                    MessageBox.Show("تم إضافة الدفعة بنجاح", "تم الحفظ", 
                                  MessageBoxButton.OK, MessageBoxImage.Information);
                }

                DialogResult = true;
                Close();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في حفظ الدفعة: {ex.Message}", "خطأ", 
                              MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void BtnCancel_Click(object sender, RoutedEventArgs e)
        {
            DialogResult = false;
            Close();
        }

        private bool ValidateInput()
        {
            try
            {
                if (CmbStudent?.SelectedItem == null)
                {
                    MessageBox.Show("يرجى اختيار الطالب", "خطأ في البيانات", 
                                  MessageBoxButton.OK, MessageBoxImage.Warning);
                    CmbStudent?.Focus();
                    return false;
                }

                if (CmbPaymentType?.SelectedItem == null)
                {
                    MessageBox.Show("يرجى اختيار نوع الدفعة", "خطأ في البيانات", 
                                  MessageBoxButton.OK, MessageBoxImage.Warning);
                    CmbPaymentType?.Focus();
                    return false;
                }

                if (TxtAmount == null || !decimal.TryParse(TxtAmount.Text, out var amount) || amount <= 0)
                {
                    MessageBox.Show("يرجى إدخال مبلغ صحيح أكبر من صفر", "خطأ في البيانات", 
                                  MessageBoxButton.OK, MessageBoxImage.Warning);
                    TxtAmount?.Focus();
                    return false;
                }

                if (DpPaymentDate?.SelectedDate == null)
                {
                    MessageBox.Show("يرجى اختيار تاريخ الدفع", "خطأ في البيانات", 
                                  MessageBoxButton.OK, MessageBoxImage.Warning);
                    DpPaymentDate?.Focus();
                    return false;
                }

                if (CmbPaymentMethod?.SelectedItem == null)
                {
                    MessageBox.Show("يرجى اختيار طريقة الدفع", "خطأ في البيانات", 
                                  MessageBoxButton.OK, MessageBoxImage.Warning);
                    CmbPaymentMethod?.Focus();
                    return false;
                }

                return true;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في التحقق من البيانات: {ex.Message}", "خطأ", 
                              MessageBoxButton.OK, MessageBoxImage.Error);
                return false;
            }
        }
    }
}
