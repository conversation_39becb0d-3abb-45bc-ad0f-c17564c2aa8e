<Page x:Class="EduTrackForWin.Views.TeachersPage"
      xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
      xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
      xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
      xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
      xmlns:local="clr-namespace:EduTrackForWin.Views"
      mc:Ignorable="d"
      d:DesignHeight="450" d:DesignWidth="800"
      Title="TeachersPage">

    <Page.Resources>
        <!-- Button Styles -->
        <Style x:Key="ActionButtonStyle" TargetType="Button">
            <Setter Property="Padding" Value="10,5"/>
            <Setter Property="Margin" Value="5"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Cursor" Value="Hand"/>
        </Style>

        <Style x:Key="PrimaryButtonStyle" TargetType="Button" BasedOn="{StaticResource ActionButtonStyle}">
            <Setter Property="Background" Value="{StaticResource PrimaryColor}"/>
            <Setter Property="Foreground" Value="White"/>
        </Style>

        <Style x:Key="SuccessButtonStyle" TargetType="Button" BasedOn="{StaticResource ActionButtonStyle}">
            <Setter Property="Background" Value="{StaticResource SuccessColor}"/>
            <Setter Property="Foreground" Value="White"/>
        </Style>

        <Style x:Key="DangerButtonStyle" TargetType="Button" BasedOn="{StaticResource ActionButtonStyle}">
            <Setter Property="Background" Value="{StaticResource ErrorColor}"/>
            <Setter Property="Foreground" Value="White"/>
        </Style>
    </Page.Resources>

    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <!-- Header Section -->
        <Border Grid.Row="0" Background="White" CornerRadius="8" Padding="20" Margin="0,0,0,20">
            <Border.Effect>
                <DropShadowEffect Color="Gray" Direction="270" ShadowDepth="2" Opacity="0.3" BlurRadius="5"/>
            </Border.Effect>

            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <StackPanel Grid.Column="0">
                    <TextBlock Text="🧑‍🏫 إدارة المعلمين" FontSize="24" FontWeight="Bold" Foreground="#2196F3"/>
                    <TextBlock Text="إضافة وتعديل وإدارة بيانات المعلمين والرواتب" FontSize="14" Foreground="#757575" Margin="0,5,0,0"/>
                </StackPanel>

                <Button Grid.Column="1" Name="BtnAddTeacher" Content="+ إضافة معلم جديد"
                      Style="{StaticResource PrimaryButtonStyle}"
                      FontSize="14" Padding="20,10" Click="BtnAddTeacher_Click"/>
            </Grid>
        </Border>

        <!-- Search and Filter Section -->
        <Border Grid.Row="1" Background="White" CornerRadius="8" Padding="20" Margin="0,0,0,20">
            <Border.Effect>
                <DropShadowEffect Color="Gray" Direction="270" ShadowDepth="2" Opacity="0.3" BlurRadius="5"/>
            </Border.Effect>

            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="200"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <!-- Search Box -->
                <TextBox Grid.Column="0" Name="TxtSearch"
                       Padding="10" FontSize="14"
                       BorderBrush="#E0E0E0" BorderThickness="1"
                       TextChanged="TxtSearch_TextChanged">
                    <TextBox.Style>
                        <Style TargetType="TextBox">
                            <Setter Property="Template">
                                <Setter.Value>
                                    <ControlTemplate TargetType="TextBox">
                                        <Border Background="{TemplateBinding Background}"
                                              BorderBrush="{TemplateBinding BorderBrush}"
                                              BorderThickness="{TemplateBinding BorderThickness}"
                                              CornerRadius="5">
                                            <Grid>
                                                <ScrollViewer x:Name="PART_ContentHost"
                                                            VerticalAlignment="Center"
                                                            Margin="10,0"/>
                                                <TextBlock Text="البحث عن معلم..."
                                                         Foreground="#999"
                                                         VerticalAlignment="Center"
                                                         Margin="10,0"
                                                         IsHitTestVisible="False">
                                                    <TextBlock.Style>
                                                        <Style TargetType="TextBlock">
                                                            <Setter Property="Visibility" Value="Collapsed"/>
                                                            <Style.Triggers>
                                                                <DataTrigger Binding="{Binding Text, RelativeSource={RelativeSource TemplatedParent}}" Value="">
                                                                    <Setter Property="Visibility" Value="Visible"/>
                                                                </DataTrigger>
                                                            </Style.Triggers>
                                                        </Style>
                                                    </TextBlock.Style>
                                                </TextBlock>
                                            </Grid>
                                        </Border>
                                    </ControlTemplate>
                                </Setter.Value>
                            </Setter>
                        </Style>
                    </TextBox.Style>
                </TextBox>

                <!-- Specialization Filter -->
                <ComboBox Grid.Column="1" Name="CmbSpecializationFilter"
                        Margin="10,0,0,0" Padding="10" FontSize="14"
                        BorderBrush="#E0E0E0" BorderThickness="1"
                        SelectionChanged="CmbSpecializationFilter_SelectionChanged">
                    <ComboBoxItem Content="جميع التخصصات" IsSelected="True"/>
                    <ComboBoxItem Content="الرياضيات"/>
                    <ComboBoxItem Content="العلوم"/>
                    <ComboBoxItem Content="اللغة العربية"/>
                    <ComboBoxItem Content="اللغة الإنجليزية"/>
                    <ComboBoxItem Content="الفيزياء"/>
                    <ComboBoxItem Content="الكيمياء"/>
                    <ComboBoxItem Content="الأحياء"/>
                    <ComboBoxItem Content="التاريخ"/>
                    <ComboBoxItem Content="الجغرافيا"/>
                </ComboBox>

                <!-- Refresh Button -->
                <Button Grid.Column="2" Name="BtnRefresh" Content="🔄 تحديث"
                      Style="{StaticResource ActionButtonStyle}"
                      Background="#757575" Foreground="White"
                      Margin="10,0,0,0" Click="BtnRefresh_Click"/>
            </Grid>
        </Border>

        <!-- Teachers List -->
        <Border Grid.Row="2" Background="White" CornerRadius="8" Padding="20">
            <Border.Effect>
                <DropShadowEffect Color="Gray" Direction="270" ShadowDepth="2" Opacity="0.3" BlurRadius="5"/>
            </Border.Effect>

            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="*"/>
                </Grid.RowDefinitions>

                <!-- List Header -->
                <TextBlock Grid.Row="0" Text="قائمة المعلمين" FontSize="18" FontWeight="SemiBold"
                         Margin="0,0,0,15" Foreground="#212121"/>

                <!-- Teachers DataGrid -->
                <DataGrid Grid.Row="1" Name="DgTeachers"
                        AutoGenerateColumns="False"
                        CanUserAddRows="False"
                        CanUserDeleteRows="False"
                        IsReadOnly="True"
                        GridLinesVisibility="Horizontal"
                        HeadersVisibility="Column"
                        BorderThickness="0"
                        Background="Transparent"
                        RowBackground="White"
                        AlternatingRowBackground="#F5F5F5"
                        SelectionMode="Single">

                    <DataGrid.Columns>
                        <DataGridTextColumn Header="الاسم" Binding="{Binding Name}" Width="200"/>
                        <DataGridTextColumn Header="التخصص" Binding="{Binding Specialization}" Width="150"/>
                        <DataGridTextColumn Header="رقم الهاتف" Binding="{Binding Phone}" Width="150"/>
                        <DataGridTextColumn Header="البريد الإلكتروني" Binding="{Binding Email}" Width="200"/>
                        <DataGridTextColumn Header="عدد المجموعات" Binding="{Binding TotalGroups}" Width="100"/>
                        <DataGridTextColumn Header="الراتب المحسوب" Binding="{Binding CalculatedSalary, StringFormat='{}{0:C}'}" Width="120"/>

                        <DataGridTemplateColumn Header="الإجراءات" Width="200">
                            <DataGridTemplateColumn.CellTemplate>
                                <DataTemplate>
                                    <StackPanel Orientation="Horizontal">
                                        <Button Content="عرض" Style="{StaticResource PrimaryButtonStyle}"
                                              Click="BtnViewTeacher_Click" Tag="{Binding Id}"/>
                                        <Button Content="تعديل" Style="{StaticResource SuccessButtonStyle}"
                                              Click="BtnEditTeacher_Click" Tag="{Binding Id}"/>
                                        <Button Content="حذف" Style="{StaticResource DangerButtonStyle}"
                                              Click="BtnDeleteTeacher_Click" Tag="{Binding Id}"/>
                                    </StackPanel>
                                </DataTemplate>
                            </DataGridTemplateColumn.CellTemplate>
                        </DataGridTemplateColumn>
                    </DataGrid.Columns>
                </DataGrid>

                <!-- No Data Message -->
                <TextBlock Name="TxtNoData" Grid.Row="1"
                         Text="لا توجد بيانات معلمين للعرض"
                         FontSize="16" Foreground="#757575"
                         HorizontalAlignment="Center" VerticalAlignment="Center"
                         Visibility="Collapsed"/>
            </Grid>
        </Border>
    </Grid>
</Page>
