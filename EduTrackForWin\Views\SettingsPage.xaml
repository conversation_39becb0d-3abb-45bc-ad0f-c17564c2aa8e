<Page x:Class="EduTrackForWin.Views.SettingsPage"
      xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
      xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
      xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
      xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
      xmlns:local="clr-namespace:EduTrackForWin.Views"
      mc:Ignorable="d" 
      d:DesignHeight="450" d:DesignWidth="800"
      Title="SettingsPage">

    <Page.Resources>
        <!-- Card Style -->
        <Style x:Key="CardStyle" TargetType="Border" BasedOn="{StaticResource ModernCardStyle}">
            <Setter Property="Background" Value="{StaticResource SurfaceColor}"/>
            <Setter Property="CornerRadius" Value="8"/>
            <Setter Property="Padding" Value="20"/>
            <Setter Property="Margin" Value="10"/>
        </Style>

        <!-- Input Field Style -->
        <Style x:Key="InputFieldStyle" TargetType="TextBox">
            <Setter Property="Padding" Value="10"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="Margin" Value="0,5,0,15"/>
        </Style>

        <!-- Label Style -->
        <Style x:Key="LabelStyle" TargetType="TextBlock" BasedOn="{StaticResource BodyStyle}">
            <Setter Property="FontWeight" Value="SemiBold"/>
            <Setter Property="Margin" Value="0,10,0,5"/>
        </Style>

        <!-- Section Header Style -->
        <Style x:Key="SectionHeaderStyle" TargetType="TextBlock" BasedOn="{StaticResource SubHeadingStyle}">
            <Setter Property="Foreground" Value="{StaticResource PrimaryColor}"/>
        </Style>

        <!-- Button Style -->
        <Style x:Key="PrimaryButtonStyle" TargetType="Button">
            <Setter Property="Background" Value="{StaticResource PrimaryColor}"/>
            <Setter Property="Foreground" Value="White"/>
        </Style>
    </Page.Resources>

    <Grid Background="#F5F5F5" Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <Border Grid.Row="0" Style="{StaticResource CardStyle}" Background="#EDE7F6">
            <StackPanel>
                <TextBlock Text="⚙️ الإعدادات" FontSize="24" FontWeight="Bold" Foreground="#673AB7"/>
                <TextBlock Text="إعدادات النظام والمركز التعليمي" FontSize="14" Foreground="#424242" Margin="0,5,0,0"/>
            </StackPanel>
        </Border>

        <!-- Settings Content -->
        <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto">
            <StackPanel>
                <!-- Center Information -->
                <Border Style="{StaticResource CardStyle}">
                    <StackPanel>
                        <TextBlock Text="معلومات المركز" Style="{StaticResource SectionHeaderStyle}"/>
                        
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            
                            <StackPanel Grid.Column="0" Margin="0,0,10,0">
                                <TextBlock Text="اسم المركز" Style="{StaticResource LabelStyle}"/>
                                <TextBox Name="TxtCenterName" Style="{StaticResource InputFieldStyle}" 
                                       Text="مركز النور التعليمي"/>

                                <TextBlock Text="رقم الهاتف" Style="{StaticResource LabelStyle}"/>
                                <TextBox Name="TxtCenterPhone" Style="{StaticResource InputFieldStyle}" 
                                       Text="01234567890"/>

                                <TextBlock Text="البريد الإلكتروني" Style="{StaticResource LabelStyle}"/>
                                <TextBox Name="TxtCenterEmail" Style="{StaticResource InputFieldStyle}" 
                                       Text="<EMAIL>"/>
                            </StackPanel>
                            
                            <StackPanel Grid.Column="1" Margin="10,0,0,0">
                                <TextBlock Text="العنوان" Style="{StaticResource LabelStyle}"/>
                                <TextBox Name="TxtCenterAddress" Style="{StaticResource InputFieldStyle}" 
                                       Height="80" TextWrapping="Wrap" VerticalScrollBarVisibility="Auto"
                                       Text="شارع الجامعة، المنصورة، الدقهلية"/>

                                <TextBlock Text="الموقع الإلكتروني" Style="{StaticResource LabelStyle}"/>
                                <TextBox Name="TxtCenterWebsite" Style="{StaticResource InputFieldStyle}" 
                                       Text="www.alnoor-center.com"/>
                            </StackPanel>
                        </Grid>
                    </StackPanel>
                </Border>

                <!-- Financial Settings -->
                <Border Style="{StaticResource CardStyle}">
                    <StackPanel>
                        <TextBlock Text="الإعدادات المالية" Style="{StaticResource SectionHeaderStyle}"/>
                        
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            
                            <StackPanel Grid.Column="0" Margin="0,0,10,0">
                                <TextBlock Text="العملة الافتراضية" Style="{StaticResource LabelStyle}"/>
                                <ComboBox Name="CmbCurrency" Padding="10" FontSize="14" Margin="0,5,0,15"
                                        BorderBrush="#E0E0E0" BorderThickness="1">
                                    <ComboBoxItem Content="جنيه مصري (ج.م)" IsSelected="True"/>
                                    <ComboBoxItem Content="دولار أمريكي ($)"/>
                                    <ComboBoxItem Content="يورو (€)"/>
                                </ComboBox>

                                <TextBlock Text="ضريبة القيمة المضافة (%)" Style="{StaticResource LabelStyle}"/>
                                <TextBox Name="TxtVATRate" Style="{StaticResource InputFieldStyle}" 
                                       Text="14"/>
                            </StackPanel>
                            
                            <StackPanel Grid.Column="1" Margin="10,0,0,0">
                                <TextBlock Text="رسوم التأخير اليومية" Style="{StaticResource LabelStyle}"/>
                                <TextBox Name="TxtLateFee" Style="{StaticResource InputFieldStyle}" 
                                       Text="10"/>

                                <TextBlock Text="خصم الدفع المبكر (%)" Style="{StaticResource LabelStyle}"/>
                                <TextBox Name="TxtEarlyPaymentDiscount" Style="{StaticResource InputFieldStyle}" 
                                       Text="5"/>
                            </StackPanel>
                        </Grid>
                    </StackPanel>
                </Border>

                <!-- Academic Settings -->
                <Border Style="{StaticResource CardStyle}">
                    <StackPanel>
                        <TextBlock Text="الإعدادات الأكاديمية" Style="{StaticResource SectionHeaderStyle}"/>
                        
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            
                            <StackPanel Grid.Column="0" Margin="0,0,10,0">
                                <TextBlock Text="مدة الحصة الافتراضية (دقيقة)" Style="{StaticResource LabelStyle}"/>
                                <TextBox Name="TxtDefaultSessionDuration" Style="{StaticResource InputFieldStyle}" 
                                       Text="60"/>

                                <TextBlock Text="الحد الأقصى لطلاب المجموعة" Style="{StaticResource LabelStyle}"/>
                                <TextBox Name="TxtMaxStudentsPerGroup" Style="{StaticResource InputFieldStyle}" 
                                       Text="15"/>
                            </StackPanel>
                            
                            <StackPanel Grid.Column="1" Margin="10,0,0,0">
                                <TextBlock Text="أيام العمل الأسبوعية" Style="{StaticResource LabelStyle}"/>
                                <StackPanel Orientation="Horizontal" Margin="0,5,0,15">
                                    <CheckBox Content="السبت" IsChecked="True" Margin="0,0,10,0"/>
                                    <CheckBox Content="الأحد" IsChecked="True" Margin="0,0,10,0"/>
                                    <CheckBox Content="الاثنين" IsChecked="True" Margin="0,0,10,0"/>
                                    <CheckBox Content="الثلاثاء" IsChecked="True" Margin="0,0,10,0"/>
                                    <CheckBox Content="الأربعاء" IsChecked="True" Margin="0,0,10,0"/>
                                    <CheckBox Content="الخميس" IsChecked="True"/>
                                </StackPanel>

                                <TextBlock Text="ساعات العمل" Style="{StaticResource LabelStyle}"/>
                                <StackPanel Orientation="Horizontal" Margin="0,5,0,15">
                                    <TextBox Name="TxtWorkStartTime" Style="{StaticResource InputFieldStyle}" 
                                           Text="08:00" Width="80" Margin="0,0,10,0"/>
                                    <TextBlock Text="إلى" VerticalAlignment="Center" Margin="0,0,10,0"/>
                                    <TextBox Name="TxtWorkEndTime" Style="{StaticResource InputFieldStyle}" 
                                           Text="22:00" Width="80"/>
                                </StackPanel>
                            </StackPanel>
                        </Grid>
                    </StackPanel>
                </Border>

                <!-- System Settings -->
                <Border Style="{StaticResource CardStyle}">
                    <StackPanel>
                        <TextBlock Text="إعدادات النظام" Style="{StaticResource SectionHeaderStyle}"/>
                        
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            
                            <StackPanel Grid.Column="0" Margin="0,0,10,0">
                                <CheckBox Name="ChkAutoBackup" Content="النسخ الاحتياطي التلقائي" 
                                        IsChecked="True" FontSize="14" Margin="0,10"/>
                                
                                <CheckBox Name="ChkEmailNotifications" Content="إشعارات البريد الإلكتروني" 
                                        IsChecked="True" FontSize="14" Margin="0,10"/>
                                
                                <CheckBox Name="ChkSMSNotifications" Content="إشعارات الرسائل النصية" 
                                        IsChecked="False" FontSize="14" Margin="0,10"/>
                            </StackPanel>
                            
                            <StackPanel Grid.Column="1" Margin="10,0,0,0">
                                <TextBlock Text="لغة النظام" Style="{StaticResource LabelStyle}"/>
                                <ComboBox Name="CmbLanguage" Padding="10" FontSize="14" Margin="0,5,0,15"
                                        BorderBrush="#E0E0E0" BorderThickness="1">
                                    <ComboBoxItem Content="العربية" IsSelected="True"/>
                                    <ComboBoxItem Content="English"/>
                                </ComboBox>

                                <TextBlock Text="المنطقة الزمنية" Style="{StaticResource LabelStyle}"/>
                                <ComboBox Name="CmbTimeZone" Padding="10" FontSize="14" Margin="0,5,0,15"
                                        BorderBrush="#E0E0E0" BorderThickness="1">
                                    <ComboBoxItem Content="القاهرة (GMT+2)" IsSelected="True"/>
                                    <ComboBoxItem Content="الرياض (GMT+3)"/>
                                    <ComboBoxItem Content="دبي (GMT+4)"/>
                                </ComboBox>
                            </StackPanel>
                        </Grid>
                    </StackPanel>
                </Border>
            </StackPanel>
        </ScrollViewer>

        <!-- Save Button -->
        <Border Grid.Row="2" Style="{StaticResource CardStyle}">
            <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                <Button Name="BtnSaveSettings" Content="حفظ الإعدادات" Style="{StaticResource PrimaryButtonStyle}" 
                      Click="BtnSaveSettings_Click"/>
                <Button Name="BtnResetSettings" Content="استعادة الافتراضي"
                      Background="#757575" Foreground="White"
                      BorderThickness="0" Padding="20,10" FontSize="14"
                      Margin="10,0,0,0" Click="BtnResetSettings_Click">
                    <Button.Template>
                        <ControlTemplate TargetType="Button">
                            <Border Background="{TemplateBinding Background}"
                                  CornerRadius="5">
                                <ContentPresenter HorizontalAlignment="Center"
                                                VerticalAlignment="Center"
                                                Margin="{TemplateBinding Padding}"/>
                            </Border>
                        </ControlTemplate>
                    </Button.Template>
                </Button>
            </StackPanel>
        </Border>
    </Grid>
</Page>
