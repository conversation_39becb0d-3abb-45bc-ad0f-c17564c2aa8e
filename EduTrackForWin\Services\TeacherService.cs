using Microsoft.EntityFrameworkCore;
using EduTrackForWin.Data;
using EduTrackForWin.Models;

namespace EduTrackForWin.Services
{
    public class TeacherService
    {
        private readonly EduTrackDbContext _context;

        public TeacherService()
        {
            _context = new EduTrackDbContext();
        }

        public async Task<List<Teacher>> GetAllTeachersAsync()
        {
            return await _context.Teachers
                .Include(t => t.Groups)
                .ThenInclude(g => g.Students)
                .Include(t => t.Sessions)
                .OrderBy(t => t.Name)
                .ToListAsync();
        }

        public async Task<Teacher?> GetTeacherByIdAsync(int id)
        {
            return await _context.Teachers
                .Include(t => t.Groups)
                .ThenInclude(g => g.Students)
                .Include(t => t.Sessions)
                .ThenInclude(s => s.Group)
                .FirstOrDefaultAsync(t => t.Id == id);
        }

        public async Task<List<Teacher>> SearchTeachersAsync(string searchTerm)
        {
            return await _context.Teachers
                .Include(t => t.Groups)
                .Where(t => t.Name.Contains(searchTerm) || 
                           t.Phone.Contains(searchTerm) ||
                           t.Email.Contains(searchTerm) ||
                           t.Specialization.Contains(searchTerm))
                .OrderBy(t => t.Name)
                .ToListAsync();
        }

        public async Task<List<Teacher>> GetTeachersBySpecializationAsync(string specialization)
        {
            return await _context.Teachers
                .Include(t => t.Groups)
                .Where(t => t.Specialization == specialization)
                .OrderBy(t => t.Name)
                .ToListAsync();
        }

        public async Task<Teacher> AddTeacherAsync(Teacher teacher)
        {
            teacher.CreatedAt = DateTime.Now;
            _context.Teachers.Add(teacher);
            await _context.SaveChangesAsync();
            return teacher;
        }

        public async Task<Teacher> UpdateTeacherAsync(Teacher teacher)
        {
            teacher.UpdatedAt = DateTime.Now;
            _context.Teachers.Update(teacher);
            await _context.SaveChangesAsync();
            return teacher;
        }

        public async Task<bool> DeleteTeacherAsync(int id)
        {
            var teacher = await _context.Teachers
                .Include(t => t.Groups)
                .FirstOrDefaultAsync(t => t.Id == id);
            
            if (teacher == null) return false;

            // Check if teacher has groups
            if (teacher.Groups != null && teacher.Groups.Any())
            {
                throw new InvalidOperationException("لا يمكن حذف المعلم لأنه مرتبط بمجموعات دراسية");
            }

            _context.Teachers.Remove(teacher);
            await _context.SaveChangesAsync();
            return true;
        }

        public async Task<decimal> CalculateTeacherSalaryAsync(int teacherId, DateTime month)
        {
            var teacher = await _context.Teachers.FindAsync(teacherId);
            if (teacher == null) return 0;

            if (teacher.IsFixedSalary)
            {
                return teacher.MonthlySalary;
            }
            else
            {
                var sessionsCount = await _context.Sessions
                    .CountAsync(s => s.TeacherId == teacherId && 
                               s.Date.Month == month.Month && 
                               s.Date.Year == month.Year &&
                               s.IsCompleted);
                
                return sessionsCount * teacher.SalaryPerSession;
            }
        }

        public async Task<Dictionary<string, object>> GetTeacherStatisticsAsync(int teacherId)
        {
            var teacher = await GetTeacherByIdAsync(teacherId);
            if (teacher == null) return new Dictionary<string, object>();

            var currentMonth = DateTime.Now;
            var sessionsThisMonth = teacher.Sessions?.Count(s => 
                s.Date.Month == currentMonth.Month && s.Date.Year == currentMonth.Year) ?? 0;

            var totalStudents = teacher.Groups?.Sum(g => g.Students?.Count ?? 0) ?? 0;
            var totalGroups = teacher.Groups?.Count ?? 0;
            var calculatedSalary = await CalculateTeacherSalaryAsync(teacherId, currentMonth);

            return new Dictionary<string, object>
            {
                ["SessionsThisMonth"] = sessionsThisMonth,
                ["TotalStudents"] = totalStudents,
                ["TotalGroups"] = totalGroups,
                ["CalculatedSalary"] = calculatedSalary,
                ["ExpectedRevenue"] = teacher.Groups?.Sum(g => g.TotalMonthlyRevenue) ?? 0
            };
        }

        public async Task<List<Session>> GetTeacherScheduleAsync(int teacherId, DateTime startDate, DateTime endDate)
        {
            return await _context.Sessions
                .Include(s => s.Group)
                .Where(s => s.TeacherId == teacherId && 
                           s.Date >= startDate && s.Date <= endDate)
                .OrderBy(s => s.Date)
                .ThenBy(s => s.StartTime)
                .ToListAsync();
        }

        public async Task<Dictionary<string, int>> GetTeacherStatisticsSummaryAsync()
        {
            var totalTeachers = await _context.Teachers.CountAsync();
            var teachersWithGroups = await _context.Teachers.CountAsync(t => t.Groups.Any());
            var activeTeachers = await _context.Teachers
                .CountAsync(t => t.Groups.Any(g => g.IsActive));

            return new Dictionary<string, int>
            {
                ["TotalTeachers"] = totalTeachers,
                ["TeachersWithGroups"] = teachersWithGroups,
                ["TeachersWithoutGroups"] = totalTeachers - teachersWithGroups,
                ["ActiveTeachers"] = activeTeachers
            };
        }

        public void Dispose()
        {
            _context?.Dispose();
        }
    }
}
