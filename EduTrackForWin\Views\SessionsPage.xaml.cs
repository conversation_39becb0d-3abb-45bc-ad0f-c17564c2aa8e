using System.Collections.ObjectModel;
using System.Windows;
using System.Windows.Controls;
using EduTrackForWin.Models;
using EduTrackForWin.Data;
using Microsoft.EntityFrameworkCore;

namespace EduTrackForWin.Views
{
    public partial class SessionsPage : Page
    {
        private readonly EduTrackDbContext _context;
        private ObservableCollection<Session> _sessions;
        private List<Session> _allSessions;

        public SessionsPage()
        {
            try
            {
                InitializeComponent();
                _context = new EduTrackDbContext();
                _sessions = new ObservableCollection<Session>();
                _allSessions = new List<Session>();

                // إضافة حدث Loaded
                Loaded += SessionsPage_Loaded;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تهيئة صفحة الحصص: {ex.Message}\n\nتفاصيل الخطأ: {ex.StackTrace}",
                              "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
                throw;
            }
        }

        private void SessionsPage_Loaded(object sender, RoutedEventArgs e)
        {
            try
            {
                if (DgSessions != null) DgSessions.ItemsSource = _sessions;
                if (DpDateFilter != null) DpDateFilter.SelectedDate = DateTime.Today;

                LoadData();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل صفحة الحصص: {ex.Message}", "خطأ",
                              MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async void LoadData()
        {
            await LoadGroups();
            await LoadSessions();
        }

        private async Task LoadGroups()
        {
            try
            {
                var groups = await _context.Groups
                    .Include(g => g.Teacher)
                    .Where(g => g.IsActive)
                    .OrderBy(g => g.Name)
                    .ToListAsync();

                if (CmbGroupFilter != null)
                {
                    CmbGroupFilter.Items.Clear();
                    CmbGroupFilter.Items.Add(new ComboBoxItem { Content = "جميع المجموعات", Tag = 0 });

                    foreach (var group in groups)
                    {
                        CmbGroupFilter.Items.Add(new ComboBoxItem { Content = group.Name, Tag = group.Id });
                    }

                    CmbGroupFilter.SelectedIndex = 0;
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل المجموعات: {ex.Message}", "خطأ",
                              MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async Task LoadSessions()
        {
            try
            {
                // تحميل البيانات من قاعدة البيانات بدون ترتيب TimeSpan
                var sessions = await _context.Sessions
                    .Include(s => s.Group)
                    .ThenInclude(g => g.Teacher)
                    .Include(s => s.Teacher)
                    .OrderBy(s => s.Date)
                    .ToListAsync();

                // ترتيب البيانات على العميل (LINQ to Objects)
                _allSessions = sessions
                    .OrderBy(s => s.Date)
                    .ThenBy(s => s.StartTime)
                    .ToList();

                RefreshSessionsList();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل الحصص: {ex.Message}", "خطأ",
                              MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void RefreshSessionsList()
        {
            _sessions.Clear();

            var filteredSessions = _allSessions.AsEnumerable();

            // Apply group filter
            if (CmbGroupFilter.SelectedItem is ComboBoxItem selectedGroup &&
                selectedGroup.Tag is int groupId && groupId > 0)
            {
                filteredSessions = filteredSessions.Where(s => s.GroupId == groupId);
            }

            // Apply date filter
            if (DpDateFilter.SelectedDate.HasValue)
            {
                var selectedDate = DpDateFilter.SelectedDate.Value.Date;
                filteredSessions = filteredSessions.Where(s => s.Date.Date == selectedDate);
            }

            foreach (var session in filteredSessions)
            {
                _sessions.Add(session);
            }
        }

        private void CmbGroupFilter_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            if (IsLoaded)
                RefreshSessionsList();
        }

        private void DpDateFilter_SelectedDateChanged(object sender, SelectionChangedEventArgs e)
        {
            if (IsLoaded)
                RefreshSessionsList();
        }

        private void ViewMode_Changed(object sender, RoutedEventArgs e)
        {
            try
            {
                // تأكد من أن الصفحة محملة بالكامل
                if (!IsLoaded)
                    return;

                // تأكد من أن العناصر محملة
                if (RbListView == null || ListViewContainer == null || CalendarViewContainer == null)
                    return;

                if (RbListView.IsChecked == true)
                {
                    ListViewContainer.Visibility = Visibility.Visible;
                    CalendarViewContainer.Visibility = Visibility.Collapsed;
                }
                else
                {
                    ListViewContainer.Visibility = Visibility.Collapsed;
                    CalendarViewContainer.Visibility = Visibility.Visible;
                    LoadCalendarSessions();
                }
            }
            catch (Exception ex)
            {
                // لا نعرض رسالة خطأ هنا لأنها قد تحدث أثناء التحميل
                System.Diagnostics.Debug.WriteLine($"خطأ في ViewMode_Changed: {ex.Message}");
            }
        }

        private void LoadCalendarSessions()
        {
            // Highlight dates with sessions
            var sessionDates = _allSessions.Select(s => s.Date.Date).Distinct().ToList();
            // Note: Calendar highlighting would need custom styling
        }

        private void SessionCalendar_SelectedDatesChanged(object sender, SelectionChangedEventArgs e)
        {
            if (SessionCalendar.SelectedDate.HasValue)
            {
                var selectedDate = SessionCalendar.SelectedDate.Value.Date;
                var sessionsOnDate = _allSessions
                    .Where(s => s.Date.Date == selectedDate)
                    .OrderBy(s => s.StartTime)
                    .ToList();

                LbSelectedDateSessions.ItemsSource = sessionsOnDate;
            }
        }

        private void BtnRefresh_Click(object sender, RoutedEventArgs e)
        {
            LoadData();
        }

        private void BtnAddSession_Click(object sender, RoutedEventArgs e)
        {
            var addWindow = new AddEditSessionWindow();
            if (addWindow.ShowDialog() == true)
            {
                LoadSessions();
            }
        }
        
        private void BtnAddSchedule_Click(object sender, RoutedEventArgs e)
        {
            var addScheduleWindow = new AddScheduleWindow();
            if (addScheduleWindow.ShowDialog() == true)
            {
                LoadSessions();
            }
        }

        private void BtnViewSession_Click(object sender, RoutedEventArgs e)
        {
            if (sender is Button button && button.Tag is int sessionId)
            {
                var session = _allSessions.FirstOrDefault(s => s.Id == sessionId);
                if (session != null)
                {
                    var viewWindow = new SessionDetailsWindow(session);
                    viewWindow.ShowDialog();
                }
            }
        }

        private void BtnEditSession_Click(object sender, RoutedEventArgs e)
        {
            if (sender is Button button && button.Tag is int sessionId)
            {
                var session = _allSessions.FirstOrDefault(s => s.Id == sessionId);
                if (session != null)
                {
                    var editWindow = new AddEditSessionWindow(session);
                    if (editWindow.ShowDialog() == true)
                    {
                        LoadSessions();
                    }
                }
            }
        }

        private async void BtnDeleteSession_Click(object sender, RoutedEventArgs e)
        {
            if (sender is Button button && button.Tag is int sessionId)
            {
                var session = _allSessions.FirstOrDefault(s => s.Id == sessionId);
                if (session != null)
                {
                    var result = MessageBox.Show(
                        $"هل أنت متأكد من حذف الحصة؟\nالتاريخ: {session.Date:yyyy/MM/dd}\nالوقت: {session.SessionTime}",
                        "تأكيد الحذف",
                        MessageBoxButton.YesNo,
                        MessageBoxImage.Warning);

                    if (result == MessageBoxResult.Yes)
                    {
                        try
                        {
                            _context.Sessions.Remove(session);
                            await _context.SaveChangesAsync();
                            LoadSessions();
                            MessageBox.Show("تم حذف الحصة بنجاح", "نجح",
                                          MessageBoxButton.OK, MessageBoxImage.Information);
                        }
                        catch (Exception ex)
                        {
                            MessageBox.Show($"خطأ في حذف الحصة: {ex.Message}", "خطأ",
                                          MessageBoxButton.OK, MessageBoxImage.Error);
                        }
                    }
                }
            }
        }

        private void Page_Unloaded(object sender, RoutedEventArgs e)
        {
            _context?.Dispose();
        }
    }
}
