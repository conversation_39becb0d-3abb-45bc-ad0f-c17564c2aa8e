using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace EduTrackForWin.Models
{
    public class Group
    {
        [Key]
        public int Id { get; set; }

        [Required]
        [StringLength(100)]
        public string Name { get; set; } = string.Empty;

        [StringLength(100)]
        public string Subject { get; set; } = string.Empty;

        [StringLength(50)]
        public string Grade { get; set; } = string.Empty;

        [Column(TypeName = "decimal(18,2)")]
        public decimal MonthlyFee { get; set; }

        // Day of month when payment is due (1-31)
        public int? BillingDay { get; set; } = 1;

        [StringLength(100)]
        public string Room { get; set; } = string.Empty;

        [StringLength(500)]
        public string Description { get; set; } = string.Empty;

        public bool IsActive { get; set; } = true;

        // Foreign Key
        public int TeacherId { get; set; }

        // Navigation Properties
        [ForeignKey("TeacherId")]
        public virtual Teacher Teacher { get; set; } = null!;

        public virtual ICollection<Student> Students { get; set; } = new List<Student>();
        public virtual ICollection<Session> Sessions { get; set; } = new List<Session>();

        // Computed Properties
        [NotMapped]
        public int StudentCount => Students?.Count ?? 0;

        [NotMapped]
        public decimal TotalMonthlyRevenue => StudentCount * MonthlyFee;

        [NotMapped]
        public int SessionsThisWeek
        {
            get
            {
                var startOfWeek = DateTime.Today.AddDays(-(int)DateTime.Today.DayOfWeek);
                var endOfWeek = startOfWeek.AddDays(7);
                return Sessions?.Count(s => s.Date >= startOfWeek && s.Date < endOfWeek) ?? 0;
            }
        }

        public DateTime CreatedAt { get; set; } = DateTime.Now;
        public DateTime? UpdatedAt { get; set; }
    }
}
