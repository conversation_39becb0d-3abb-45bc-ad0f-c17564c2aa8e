<Page x:Class="EduTrackForWin.Views.StudentsPage"
      xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
      xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
      xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
      xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
      xmlns:local="clr-namespace:EduTrackForWin.Views"
      mc:Ignorable="d" 
      d:DesignHeight="450" d:DesignWidth="800"
      Title="StudentsPage">

    <Page.Resources>
        <!-- Button Styles -->
        <Style x:Key="ActionButtonStyle" TargetType="Button">
            <Setter Property="Padding" Value="10,5"/>
            <Setter Property="Margin" Value="5"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Cursor" Value="Hand"/>
        </Style>

        <Style x:Key="PrimaryButtonStyle" TargetType="Button" BasedOn="{StaticResource ActionButtonStyle}">
            <Setter Property="Background" Value="{StaticResource PrimaryColor}"/>
            <Setter Property="Foreground" Value="White"/>
        </Style>

        <Style x:Key="SuccessButtonStyle" TargetType="Button" BasedOn="{StaticResource ActionButtonStyle}">
            <Setter Property="Background" Value="{StaticResource SuccessColor}"/>
            <Setter Property="Foreground" Value="White"/>
        </Style>

        <Style x:Key="DangerButtonStyle" TargetType="Button" BasedOn="{StaticResource ActionButtonStyle}">
            <Setter Property="Background" Value="{StaticResource ErrorColor}"/>
            <Setter Property="Foreground" Value="White"/>
        </Style>
    </Page.Resources>

    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <!-- Header Section -->
        <Border Grid.Row="0" Background="White" CornerRadius="8" Padding="20" Margin="0,0,0,20">
            <Border.Effect>
                <DropShadowEffect Color="Gray" Direction="270" ShadowDepth="2" Opacity="0.3" BlurRadius="5"/>
            </Border.Effect>
            
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <StackPanel Grid.Column="0">
                    <TextBlock Text="إدارة الطلاب" FontSize="24" FontWeight="Bold" Foreground="#212121"/>
                    <TextBlock Text="إضافة وتعديل وإدارة بيانات الطلاب" FontSize="14" Foreground="#757575" Margin="0,5,0,0"/>
                </StackPanel>

                <Button Grid.Column="1" Name="BtnAddStudent" Content="+ إضافة طالب جديد" 
                      Style="{StaticResource PrimaryButtonStyle}" 
                      FontSize="14" Padding="20,10" Click="BtnAddStudent_Click"/>
            </Grid>
        </Border>

        <!-- Search and Filter Section -->
        <Border Grid.Row="1" Background="White" CornerRadius="8" Padding="20" Margin="0,0,0,20">
            <Border.Effect>
                <DropShadowEffect Color="Gray" Direction="270" ShadowDepth="2" Opacity="0.3" BlurRadius="5"/>
            </Border.Effect>
            
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="200"/>
                    <ColumnDefinition Width="200"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <!-- Search Box -->
                <TextBox Grid.Column="0" Name="TxtSearch" 
                       Padding="10" FontSize="14" 
                       BorderBrush="#E0E0E0" BorderThickness="1"
                       Text="{Binding SearchText, UpdateSourceTrigger=PropertyChanged}"
                       TextChanged="TxtSearch_TextChanged">
                    <TextBox.Style>
                        <Style TargetType="TextBox">
                            <Setter Property="Template">
                                <Setter.Value>
                                    <ControlTemplate TargetType="TextBox">
                                        <Border Background="{TemplateBinding Background}" 
                                              BorderBrush="{TemplateBinding BorderBrush}" 
                                              BorderThickness="{TemplateBinding BorderThickness}"
                                              CornerRadius="5">
                                            <Grid>
                                                <ScrollViewer x:Name="PART_ContentHost" 
                                                            VerticalAlignment="Center" 
                                                            Margin="10,0"/>
                                                <TextBlock Text="البحث عن طالب..." 
                                                         Foreground="#999" 
                                                         VerticalAlignment="Center" 
                                                         Margin="10,0"
                                                         IsHitTestVisible="False">
                                                    <TextBlock.Style>
                                                        <Style TargetType="TextBlock">
                                                            <Setter Property="Visibility" Value="Collapsed"/>
                                                            <Style.Triggers>
                                                                <DataTrigger Binding="{Binding Text, RelativeSource={RelativeSource TemplatedParent}}" Value="">
                                                                    <Setter Property="Visibility" Value="Visible"/>
                                                                </DataTrigger>
                                                            </Style.Triggers>
                                                        </Style>
                                                    </TextBlock.Style>
                                                </TextBlock>
                                            </Grid>
                                        </Border>
                                    </ControlTemplate>
                                </Setter.Value>
                            </Setter>
                        </Style>
                    </TextBox.Style>
                </TextBox>

                <!-- Grade Filter -->
                <ComboBox Grid.Column="1" Name="CmbGradeFilter" 
                        Margin="10,0,0,0" Padding="10" FontSize="14"
                        BorderBrush="#E0E0E0" BorderThickness="1"
                        SelectionChanged="CmbGradeFilter_SelectionChanged">
                    <ComboBoxItem Content="جميع الصفوف" IsSelected="True"/>
                    <ComboBoxItem Content="الصف الأول"/>
                    <ComboBoxItem Content="الصف الثاني"/>
                    <ComboBoxItem Content="الصف الثالث"/>
                    <ComboBoxItem Content="الصف الرابع"/>
                    <ComboBoxItem Content="الصف الخامس"/>
                    <ComboBoxItem Content="الصف السادس"/>
                </ComboBox>

                <!-- Payment Status Filter -->
                <ComboBox Grid.Column="2" Name="CmbPaymentFilter" 
                        Margin="10,0,0,0" Padding="10" FontSize="14"
                        BorderBrush="#E0E0E0" BorderThickness="1"
                        SelectionChanged="CmbPaymentFilter_SelectionChanged">
                    <ComboBoxItem Content="جميع الحالات" IsSelected="True"/>
                    <ComboBoxItem Content="مدفوع"/>
                    <ComboBoxItem Content="متأخر"/>
                </ComboBox>

                <!-- Refresh Button -->
                <Button Grid.Column="3" Name="BtnRefresh" Content="🔄 تحديث" 
                      Style="{StaticResource ActionButtonStyle}" 
                      Background="#757575" Foreground="White"
                      Margin="10,0,0,0" Click="BtnRefresh_Click"/>
            </Grid>
        </Border>

        <!-- Students List -->
        <Border Grid.Row="2" Background="White" CornerRadius="8" Padding="20">
            <Border.Effect>
                <DropShadowEffect Color="Gray" Direction="270" ShadowDepth="2" Opacity="0.3" BlurRadius="5"/>
            </Border.Effect>
            
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="*"/>
                </Grid.RowDefinitions>

                <!-- List Header -->
                <TextBlock Grid.Row="0" Text="قائمة الطلاب" FontSize="18" FontWeight="SemiBold" 
                         Margin="0,0,0,15" Foreground="#212121"/>

                <!-- Students DataGrid -->
                <DataGrid Grid.Row="1" Name="DgStudents" 
                        AutoGenerateColumns="False" 
                        CanUserAddRows="False" 
                        CanUserDeleteRows="False"
                        IsReadOnly="True"
                        GridLinesVisibility="Horizontal"
                        HeadersVisibility="Column"
                        BorderThickness="0"
                        Background="Transparent"
                        RowBackground="White"
                        AlternatingRowBackground="#F5F5F5"
                        SelectionMode="Single"
                        SelectionChanged="DgStudents_SelectionChanged">
                    
                    <DataGrid.Columns>
                        <DataGridTextColumn Header="الاسم" Binding="{Binding Name}" Width="200"/>
                        <DataGridTextColumn Header="الصف" Binding="{Binding Grade}" Width="100"/>
                        <DataGridTextColumn Header="رقم الهاتف" Binding="{Binding Phone}" Width="150"/>
                        <DataGridTextColumn Header="المجموعة" Binding="{Binding Group.Name}" Width="150"/>
                        <DataGridTextColumn Header="حالة الدفع" Binding="{Binding PaymentStatus}" Width="100"/>
                        <DataGridTextColumn Header="المبلغ المستحق" Binding="{Binding TotalDue, StringFormat='{}{0:C}'}" Width="120"/>
                        
                        <DataGridTemplateColumn Header="الإجراءات" Width="200">
                            <DataGridTemplateColumn.CellTemplate>
                                <DataTemplate>
                                    <StackPanel Orientation="Horizontal">
                                        <Button Content="عرض" Style="{StaticResource PrimaryButtonStyle}" 
                                              Click="BtnViewStudent_Click" Tag="{Binding Id}"/>
                                        <Button Content="تعديل" Style="{StaticResource SuccessButtonStyle}" 
                                              Click="BtnEditStudent_Click" Tag="{Binding Id}"/>
                                        <Button Content="حذف" Style="{StaticResource DangerButtonStyle}" 
                                              Click="BtnDeleteStudent_Click" Tag="{Binding Id}"/>
                                    </StackPanel>
                                </DataTemplate>
                            </DataGridTemplateColumn.CellTemplate>
                        </DataGridTemplateColumn>
                    </DataGrid.Columns>
                </DataGrid>

                <!-- No Data Message -->
                <TextBlock Name="TxtNoData" Grid.Row="1" 
                         Text="لا توجد بيانات طلاب للعرض" 
                         FontSize="16" Foreground="#757575" 
                         HorizontalAlignment="Center" VerticalAlignment="Center"
                         Visibility="Collapsed"/>
            </Grid>
        </Border>
    </Grid>
</Page>
