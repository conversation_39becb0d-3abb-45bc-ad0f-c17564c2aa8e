using Microsoft.EntityFrameworkCore;
using EduTrackForWin.Data;
using EduTrackForWin.Models;

namespace EduTrackForWin.Services
{
    public class GroupService
    {
        private readonly EduTrackDbContext _context;

        public GroupService()
        {
            _context = new EduTrackDbContext();
        }

        public async Task<List<Group>> GetAllGroupsAsync()
        {
            return await _context.Groups
                .Include(g => g.Teacher)
                .Include(g => g.Students)
                .ThenInclude(s => s.Payments)
                .Include(g => g.Sessions)
                .OrderBy(g => g.Name)
                .ToListAsync();
        }

        public async Task<Group?> GetGroupByIdAsync(int id)
        {
            return await _context.Groups
                .Include(g => g.Teacher)
                .Include(g => g.Students)
                .ThenInclude(s => s.Payments)
                .Include(g => g.Sessions)
                .ThenInclude(s => s.Attendances)
                .FirstOrDefaultAsync(g => g.Id == id);
        }

        public async Task<List<Group>> GetActiveGroupsAsync()
        {
            return await _context.Groups
                .Include(g => g.Teacher)
                .Include(g => g.Students)
                .Where(g => g.IsActive)
                .OrderBy(g => g.Name)
                .ToListAsync();
        }

        public async Task<List<Group>> GetGroupsByTeacherAsync(int teacherId)
        {
            return await _context.Groups
                .Include(g => g.Students)
                .Include(g => g.Sessions)
                .Where(g => g.TeacherId == teacherId)
                .OrderBy(g => g.Name)
                .ToListAsync();
        }

        public async Task<List<Group>> SearchGroupsAsync(string searchTerm)
        {
            return await _context.Groups
                .Include(g => g.Teacher)
                .Include(g => g.Students)
                .Where(g => g.Name.Contains(searchTerm) || 
                           g.Subject.Contains(searchTerm) ||
                           g.Grade.Contains(searchTerm) ||
                           g.Teacher.Name.Contains(searchTerm))
                .OrderBy(g => g.Name)
                .ToListAsync();
        }

        public async Task<Group> AddGroupAsync(Group group)
        {
            group.CreatedAt = DateTime.Now;
            _context.Groups.Add(group);
            await _context.SaveChangesAsync();
            return group;
        }

        public async Task<Group> UpdateGroupAsync(Group group)
        {
            group.UpdatedAt = DateTime.Now;
            _context.Groups.Update(group);
            await _context.SaveChangesAsync();
            return group;
        }

        public async Task<bool> DeleteGroupAsync(int id)
        {
            var group = await _context.Groups
                .Include(g => g.Students)
                .Include(g => g.Sessions)
                .FirstOrDefaultAsync(g => g.Id == id);
            
            if (group == null) return false;

            // Check if group has students
            if (group.Students != null && group.Students.Any())
            {
                throw new InvalidOperationException("لا يمكن حذف المجموعة لأنها تحتوي على طلاب");
            }

            _context.Groups.Remove(group);
            await _context.SaveChangesAsync();
            return true;
        }

        public async Task<bool> AddStudentToGroupAsync(int studentId, int groupId)
        {
            var student = await _context.Students.FindAsync(studentId);
            if (student == null) return false;

            student.GroupId = groupId;
            student.UpdatedAt = DateTime.Now;
            await _context.SaveChangesAsync();
            return true;
        }

        public async Task<bool> RemoveStudentFromGroupAsync(int studentId)
        {
            var student = await _context.Students.FindAsync(studentId);
            if (student == null) return false;

            student.GroupId = null;
            student.UpdatedAt = DateTime.Now;
            await _context.SaveChangesAsync();
            return true;
        }

        public async Task<Dictionary<string, object>> GetGroupFinancialSummaryAsync(int groupId)
        {
            var group = await GetGroupByIdAsync(groupId);
            if (group == null) return new Dictionary<string, object>();

            var studentCount = group.Students?.Count ?? 0;
            var expectedRevenue = studentCount * group.MonthlyFee;
            var totalPayments = group.Students?.Sum(s => s.TotalPaid) ?? 0;
            var outstandingAmount = group.Students?.Sum(s => s.TotalDue) ?? 0;

            return new Dictionary<string, object>
            {
                ["StudentCount"] = studentCount,
                ["ExpectedRevenue"] = expectedRevenue,
                ["TotalPayments"] = totalPayments,
                ["OutstandingAmount"] = outstandingAmount,
                ["CollectionRate"] = expectedRevenue > 0 ? (totalPayments / expectedRevenue) * 100 : 0
            };
        }

        public async Task<List<Session>> GetGroupScheduleAsync(int groupId, DateTime startDate, DateTime endDate)
        {
            return await _context.Sessions
                .Include(s => s.Teacher)
                .Include(s => s.Attendances)
                .ThenInclude(a => a.Student)
                .Where(s => s.GroupId == groupId && 
                           s.Date >= startDate && s.Date <= endDate)
                .OrderBy(s => s.Date)
                .ThenBy(s => s.StartTime)
                .ToListAsync();
        }

        public async Task<Dictionary<string, int>> GetGroupStatisticsSummaryAsync()
        {
            var totalGroups = await _context.Groups.CountAsync();
            var activeGroups = await _context.Groups.CountAsync(g => g.IsActive);
            var groupsWithStudents = await _context.Groups.CountAsync(g => g.Students.Any());
            var totalStudentsInGroups = await _context.Groups.SumAsync(g => g.Students.Count);

            return new Dictionary<string, int>
            {
                ["TotalGroups"] = totalGroups,
                ["ActiveGroups"] = activeGroups,
                ["InactiveGroups"] = totalGroups - activeGroups,
                ["GroupsWithStudents"] = groupsWithStudents,
                ["TotalStudentsInGroups"] = totalStudentsInGroups
            };
        }

        public async Task<bool> ToggleGroupStatusAsync(int groupId)
        {
            var group = await _context.Groups.FindAsync(groupId);
            if (group == null) return false;

            group.IsActive = !group.IsActive;
            group.UpdatedAt = DateTime.Now;
            await _context.SaveChangesAsync();
            return true;
        }

        public async Task<List<Student>> GetAvailableStudentsForGroupAsync(int? excludeGroupId = null)
        {
            var query = _context.Students.Where(s => s.GroupId == null);
            
            if (excludeGroupId.HasValue)
            {
                query = query.Where(s => s.GroupId != excludeGroupId.Value);
            }

            return await query.OrderBy(s => s.Name).ToListAsync();
        }

        public void Dispose()
        {
            _context?.Dispose();
        }
    }
}
