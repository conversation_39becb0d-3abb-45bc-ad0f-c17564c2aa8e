<Page x:Class="EduTrackForWin.Views.DashboardPage"
      xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
      xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
      xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
      xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
      xmlns:local="clr-namespace:EduTrackForWin.Views"
      mc:Ignorable="d" 
      d:DesignHeight="450" d:DesignWidth="800"
      Title="DashboardPage">

    <Page.Resources>
        <!-- Card Style -->
        <Style x:Key="DashboardCardStyle" TargetType="Border" BasedOn="{StaticResource ModernCardStyle}">
            <Setter Property="Background" Value="{StaticResource SurfaceColor}"/>
            <Setter Property="CornerRadius" Value="16"/>
            <Setter Property="Padding" Value="24"/>
            <Setter Property="Margin" Value="12"/>
            <Setter Property="Effect">
                <Setter.Value>
                    <DropShadowEffect Color="{Binding Source={StaticResource ShadowColor}, Path=Color}" Direction="270" ShadowDepth="4"
                                    Opacity="0.1" BlurRadius="16"/>
                </Setter.Value>
            </Setter>
        </Style>

        <!-- Stat Number Style -->
        <Style x:Key="StatNumberStyle" TargetType="TextBlock">
            <Setter Property="FontSize" Value="36"/>
            <Setter Property="FontWeight" Value="Bold"/>
            <Setter Property="HorizontalAlignment" Value="Center"/>
            <Setter Property="Margin" Value="0,10"/>
        </Style>

        <!-- Stat Label Style -->
        <Style x:Key="StatLabelStyle" TargetType="TextBlock">
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="HorizontalAlignment" Value="Center"/>
            <Setter Property="Foreground" Value="{StaticResource TextSecondaryColor}"/>
        </Style>
    </Page.Resources>

    <ScrollViewer VerticalScrollBarVisibility="Auto">
        <StackPanel Margin="20">
            <!-- Welcome Section -->
            <Border Style="{StaticResource DashboardCardStyle}" Background="#E3F2FD">
                <StackPanel>
                    <TextBlock Text="مرحباً بك في نظام EduTrack" FontSize="24" FontWeight="Bold" 
                             HorizontalAlignment="Center" Foreground="#1976D2"/>
                    <TextBlock Text="نظام شامل لإدارة المراكز التعليمية" FontSize="16" 
                             HorizontalAlignment="Center" Foreground="#424242" Margin="0,5,0,0"/>
                </StackPanel>
            </Border>

            <!-- Statistics Cards -->
            <Grid Margin="0,20,0,0">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>

                <!-- Total Students -->
                <Border Grid.Column="0" Style="{StaticResource DashboardCardStyle}" Background="#E3F2FD">
                    <StackPanel>
                        <Grid Width="64" Height="64" HorizontalAlignment="Center" Margin="0,0,0,16">
                            <Ellipse Width="64" Height="64" Fill="#2196F3">
                                <Ellipse.Effect>
                                    <DropShadowEffect Color="#2196F3"
                                                    Direction="270" ShadowDepth="2" Opacity="0.3" BlurRadius="8"/>
                                </Ellipse.Effect>
                            </Ellipse>
                            <TextBlock Text="👥" FontSize="32" HorizontalAlignment="Center"
                                     VerticalAlignment="Center" Foreground="White"/>
                        </Grid>
                        <TextBlock Name="TxtTotalStudents" Text="0" Style="{StaticResource StatNumberStyle}"
                                 Foreground="#2196F3" FontSize="32" FontWeight="Bold"/>
                        <TextBlock Text="إجمالي الطلاب" Style="{StaticResource StatLabelStyle}"
                                 Foreground="#757575"/>
                    </StackPanel>
                </Border>

                <!-- Total Teachers -->
                <Border Grid.Column="1" Style="{StaticResource DashboardCardStyle}" Background="#E8F5E8">
                    <StackPanel>
                        <Grid Width="64" Height="64" HorizontalAlignment="Center" Margin="0,0,0,16">
                            <Ellipse Width="64" Height="64" Fill="#4CAF50">
                                <Ellipse.Effect>
                                    <DropShadowEffect Color="#4CAF50"
                                                    Direction="270" ShadowDepth="2" Opacity="0.3" BlurRadius="8"/>
                                </Ellipse.Effect>
                            </Ellipse>
                            <TextBlock Text="👨‍🏫" FontSize="32" HorizontalAlignment="Center"
                                     VerticalAlignment="Center" Foreground="White"/>
                        </Grid>
                        <TextBlock Name="TxtTotalTeachers" Text="0" Style="{StaticResource StatNumberStyle}"
                                 Foreground="#4CAF50" FontSize="32" FontWeight="Bold"/>
                        <TextBlock Text="إجمالي المعلمين" Style="{StaticResource StatLabelStyle}"
                                 Foreground="#757575"/>
                    </StackPanel>
                </Border>

                <!-- Total Groups -->
                <Border Grid.Column="2" Style="{StaticResource DashboardCardStyle}" Background="#FFF3E0">
                    <StackPanel>
                        <Grid Width="64" Height="64" HorizontalAlignment="Center" Margin="0,0,0,16">
                            <Ellipse Width="64" Height="64" Fill="#FF9800">
                                <Ellipse.Effect>
                                    <DropShadowEffect Color="#FF9800"
                                                    Direction="270" ShadowDepth="2" Opacity="0.3" BlurRadius="8"/>
                                </Ellipse.Effect>
                            </Ellipse>
                            <TextBlock Text="📚" FontSize="32" HorizontalAlignment="Center"
                                     VerticalAlignment="Center" Foreground="White"/>
                        </Grid>
                        <TextBlock Name="TxtTotalGroups" Text="0" Style="{StaticResource StatNumberStyle}"
                                 Foreground="#FF9800" FontSize="32" FontWeight="Bold"/>
                        <TextBlock Text="إجمالي المجموعات" Style="{StaticResource StatLabelStyle}"
                                 Foreground="#757575"/>
                    </StackPanel>
                </Border>

                <!-- Today's Sessions -->
                <Border Grid.Column="3" Style="{StaticResource DashboardCardStyle}" Background="#F3E5F5">
                    <StackPanel>
                        <Grid Width="64" Height="64" HorizontalAlignment="Center" Margin="0,0,0,16">
                            <Ellipse Width="64" Height="64" Fill="#9C27B0">
                                <Ellipse.Effect>
                                    <DropShadowEffect Color="#9C27B0"
                                                    Direction="270" ShadowDepth="2" Opacity="0.3" BlurRadius="8"/>
                                </Ellipse.Effect>
                            </Ellipse>
                            <TextBlock Text="📅" FontSize="32" HorizontalAlignment="Center"
                                     VerticalAlignment="Center" Foreground="White"/>
                        </Grid>
                        <TextBlock Name="TxtTodaySessions" Text="0" Style="{StaticResource StatNumberStyle}"
                                 Foreground="#9C27B0" FontSize="32" FontWeight="Bold"/>
                        <TextBlock Text="حصص اليوم" Style="{StaticResource StatLabelStyle}"
                                 Foreground="#757575"/>
                    </StackPanel>
                </Border>
            </Grid>

            <!-- Financial Overview -->
            <Grid Margin="0,20,0,0">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>

                <!-- Today's Payments -->
                <Border Grid.Column="0" Style="{StaticResource DashboardCardStyle}" Background="#E8F5E8">
                    <StackPanel>
                        <Grid Width="64" Height="64" HorizontalAlignment="Center" Margin="0,0,0,16">
                            <Ellipse Width="64" Height="64" Fill="#4CAF50">
                                <Ellipse.Effect>
                                    <DropShadowEffect Color="#4CAF50"
                                                    Direction="270" ShadowDepth="2" Opacity="0.3" BlurRadius="8"/>
                                </Ellipse.Effect>
                            </Ellipse>
                            <TextBlock Text="💰" FontSize="32" HorizontalAlignment="Center"
                                     VerticalAlignment="Center" Foreground="White"/>
                        </Grid>
                        <TextBlock Name="TxtTodayPayments" Text="0 ج.م" Style="{StaticResource StatNumberStyle}"
                                 Foreground="#4CAF50" FontSize="24" FontWeight="Bold"/>
                        <TextBlock Text="مدفوعات اليوم" Style="{StaticResource StatLabelStyle}"
                                 Foreground="#757575"/>
                    </StackPanel>
                </Border>

                <!-- Outstanding Dues -->
                <Border Grid.Column="1" Style="{StaticResource DashboardCardStyle}" Background="#FFEBEE">
                    <StackPanel>
                        <Grid Width="64" Height="64" HorizontalAlignment="Center" Margin="0,0,0,16">
                            <Ellipse Width="64" Height="64" Fill="#F44336">
                                <Ellipse.Effect>
                                    <DropShadowEffect Color="#F44336"
                                                    Direction="270" ShadowDepth="2" Opacity="0.3" BlurRadius="8"/>
                                </Ellipse.Effect>
                            </Ellipse>
                            <TextBlock Text="⚠️" FontSize="32" HorizontalAlignment="Center"
                                     VerticalAlignment="Center" Foreground="White"/>
                        </Grid>
                        <TextBlock Name="TxtOutstandingDues" Text="0 ج.م" Style="{StaticResource StatNumberStyle}"
                                 Foreground="#F44336" FontSize="24" FontWeight="Bold"/>
                        <TextBlock Text="الديون المستحقة" Style="{StaticResource StatLabelStyle}"
                                 Foreground="#757575"/>
                    </StackPanel>
                </Border>

                <!-- Attendance Rate -->
                <Border Grid.Column="2" Style="{StaticResource DashboardCardStyle}" Background="#E3F2FD">
                    <StackPanel>
                        <Grid Width="64" Height="64" HorizontalAlignment="Center" Margin="0,0,0,16">
                            <Ellipse Width="64" Height="64" Fill="#2196F3">
                                <Ellipse.Effect>
                                    <DropShadowEffect Color="#2196F3"
                                                    Direction="270" ShadowDepth="2" Opacity="0.3" BlurRadius="8"/>
                                </Ellipse.Effect>
                            </Ellipse>
                            <TextBlock Text="📊" FontSize="32" HorizontalAlignment="Center"
                                     VerticalAlignment="Center" Foreground="White"/>
                        </Grid>
                        <TextBlock Name="TxtAttendanceRate" Text="0%" Style="{StaticResource StatNumberStyle}"
                                 Foreground="#2196F3" FontSize="24" FontWeight="Bold"/>
                        <TextBlock Text="نسبة الحضور هذا الشهر" Style="{StaticResource StatLabelStyle}"
                                 Foreground="#757575"/>
                    </StackPanel>
                </Border>
            </Grid>

            <!-- Recent Activities -->
            <Border Style="{StaticResource DashboardCardStyle}" Margin="0,20,0,0">
                <StackPanel>
                    <StackPanel Orientation="Horizontal" Margin="0,0,0,20">
                        <Grid Width="32" Height="32" Margin="0,0,12,0">
                            <Ellipse Width="32" Height="32" Fill="#2196F3"/>
                            <TextBlock Text="📋" FontSize="16" HorizontalAlignment="Center"
                                     VerticalAlignment="Center" Foreground="White"/>
                        </Grid>
                        <TextBlock Text="الأنشطة الحديثة" FontSize="20" FontWeight="SemiBold"
                                 VerticalAlignment="Center"/>
                    </StackPanel>

                    <ListView Name="LstRecentActivities" Height="200" BorderThickness="0"
                            Background="Transparent">
                        <ListView.View>
                            <GridView>
                                <GridViewColumn Header="النشاط" Width="300" DisplayMemberBinding="{Binding Activity}"/>
                                <GridViewColumn Header="التاريخ" Width="150" DisplayMemberBinding="{Binding Date}"/>
                                <GridViewColumn Header="النوع" Width="100" DisplayMemberBinding="{Binding Type}"/>
                            </GridView>
                        </ListView.View>
                    </ListView>
                </StackPanel>
            </Border>

            <!-- Quick Actions -->
            <Border Style="{StaticResource DashboardCardStyle}" Margin="0,20,0,0">
                <StackPanel>
                    <StackPanel Orientation="Horizontal" Margin="0,0,0,20">
                        <Grid Width="32" Height="32" Margin="0,0,12,0">
                            <Ellipse Width="32" Height="32" Fill="#FF9800"/>
                            <TextBlock Text="⚡" FontSize="16" HorizontalAlignment="Center"
                                     VerticalAlignment="Center" Foreground="White"/>
                        </Grid>
                        <TextBlock Text="إجراءات سريعة" FontSize="20" FontWeight="SemiBold"
                                 VerticalAlignment="Center"/>
                    </StackPanel>

                    <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                        <Button Name="BtnAddStudent" Content="👥 إضافة طالب جديد"
                              Background="#2196F3" Foreground="White"
                              Padding="15,10" Margin="10" BorderThickness="0"
                              Click="BtnAddStudent_Click"/>

                        <Button Name="BtnAddTeacher" Content="👨‍🏫 إضافة معلم جديد"
                              Background="#4CAF50" Foreground="White"
                              Padding="15,10" Margin="10" BorderThickness="0"
                              Click="BtnAddTeacher_Click"/>

                        <Button Name="BtnCreateGroup" Content="📚 إنشاء مجموعة جديدة"
                              Background="#FF9800" Foreground="White"
                              Padding="15,10" Margin="10" BorderThickness="0"
                              Click="BtnCreateGroup_Click"/>

                        <Button Name="BtnTakeAttendance" Content="✅ تسجيل الحضور"
                              Background="#9C27B0" Foreground="White"
                              Padding="15,10" Margin="10" BorderThickness="0"
                              Click="BtnTakeAttendance_Click"/>
                    </StackPanel>
                </StackPanel>
            </Border>
        </StackPanel>
    </ScrollViewer>
</Page>
