﻿#pragma checksum "..\..\..\..\Views\ManageGroupStudentsWindow.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "306A1859B889268C14AB1632C41B3EF6217C11B8"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using EduTrackForWin.Views;
using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace EduTrackForWin.Views {
    
    
    /// <summary>
    /// ManageGroupStudentsWindow
    /// </summary>
    public partial class ManageGroupStudentsWindow : System.Windows.Window, System.Windows.Markup.IComponentConnector, System.Windows.Markup.IStyleConnector {
        
        
        #line 105 "..\..\..\..\Views\ManageGroupStudentsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TxtGroupName;
        
        #line default
        #line hidden
        
        
        #line 108 "..\..\..\..\Views\ManageGroupStudentsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TxtStudentCount;
        
        #line default
        #line hidden
        
        
        #line 112 "..\..\..\..\Views\ManageGroupStudentsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DataGrid DgCurrentStudents;
        
        #line default
        #line hidden
        
        
        #line 143 "..\..\..\..\Views\ManageGroupStudentsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BtnRemoveAll;
        
        #line default
        #line hidden
        
        
        #line 161 "..\..\..\..\Views\ManageGroupStudentsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TxtAvailableCount;
        
        #line default
        #line hidden
        
        
        #line 166 "..\..\..\..\Views\ManageGroupStudentsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox TxtSearch;
        
        #line default
        #line hidden
        
        
        #line 208 "..\..\..\..\Views\ManageGroupStudentsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DataGrid DgAvailableStudents;
        
        #line default
        #line hidden
        
        
        #line 239 "..\..\..\..\Views\ManageGroupStudentsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BtnAddSelected;
        
        #line default
        #line hidden
        
        
        #line 241 "..\..\..\..\Views\ManageGroupStudentsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BtnRefresh;
        
        #line default
        #line hidden
        
        
        #line 269 "..\..\..\..\Views\ManageGroupStudentsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BtnSave;
        
        #line default
        #line hidden
        
        
        #line 271 "..\..\..\..\Views\ManageGroupStudentsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BtnCancel;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/EduTrackForWin;component/views/managegroupstudentswindow.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\Views\ManageGroupStudentsWindow.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.TxtGroupName = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 2:
            this.TxtStudentCount = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 3:
            this.DgCurrentStudents = ((System.Windows.Controls.DataGrid)(target));
            return;
            case 5:
            this.BtnRemoveAll = ((System.Windows.Controls.Button)(target));
            
            #line 144 "..\..\..\..\Views\ManageGroupStudentsWindow.xaml"
            this.BtnRemoveAll.Click += new System.Windows.RoutedEventHandler(this.BtnRemoveAll_Click);
            
            #line default
            #line hidden
            return;
            case 6:
            this.TxtAvailableCount = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 7:
            this.TxtSearch = ((System.Windows.Controls.TextBox)(target));
            
            #line 169 "..\..\..\..\Views\ManageGroupStudentsWindow.xaml"
            this.TxtSearch.TextChanged += new System.Windows.Controls.TextChangedEventHandler(this.TxtSearch_TextChanged);
            
            #line default
            #line hidden
            return;
            case 8:
            this.DgAvailableStudents = ((System.Windows.Controls.DataGrid)(target));
            return;
            case 10:
            this.BtnAddSelected = ((System.Windows.Controls.Button)(target));
            
            #line 240 "..\..\..\..\Views\ManageGroupStudentsWindow.xaml"
            this.BtnAddSelected.Click += new System.Windows.RoutedEventHandler(this.BtnAddSelected_Click);
            
            #line default
            #line hidden
            return;
            case 11:
            this.BtnRefresh = ((System.Windows.Controls.Button)(target));
            
            #line 244 "..\..\..\..\Views\ManageGroupStudentsWindow.xaml"
            this.BtnRefresh.Click += new System.Windows.RoutedEventHandler(this.BtnRefresh_Click);
            
            #line default
            #line hidden
            return;
            case 12:
            this.BtnSave = ((System.Windows.Controls.Button)(target));
            
            #line 270 "..\..\..\..\Views\ManageGroupStudentsWindow.xaml"
            this.BtnSave.Click += new System.Windows.RoutedEventHandler(this.BtnSave_Click);
            
            #line default
            #line hidden
            return;
            case 13:
            this.BtnCancel = ((System.Windows.Controls.Button)(target));
            
            #line 274 "..\..\..\..\Views\ManageGroupStudentsWindow.xaml"
            this.BtnCancel.Click += new System.Windows.RoutedEventHandler(this.BtnCancel_Click);
            
            #line default
            #line hidden
            return;
            }
            this._contentLoaded = true;
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        void System.Windows.Markup.IStyleConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 4:
            
            #line 135 "..\..\..\..\Views\ManageGroupStudentsWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.BtnRemoveStudent_Click);
            
            #line default
            #line hidden
            break;
            case 9:
            
            #line 231 "..\..\..\..\Views\ManageGroupStudentsWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.BtnAddStudent_Click);
            
            #line default
            #line hidden
            break;
            }
        }
    }
}

