using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace EduTrackForWin.Models
{
    public class Settings
    {
        [Key]
        public int Id { get; set; }

        [StringLength(200)]
        public string CenterName { get; set; } = "مركز تعليمي";

        [StringLength(500)]
        public string CenterAddress { get; set; } = string.Empty;

        [StringLength(20)]
        public string CenterPhone { get; set; } = string.Empty;

        [StringLength(100)]
        public string CenterEmail { get; set; } = string.Empty;

        [StringLength(500)]
        public string LogoPath { get; set; } = string.Empty;

        [Column(TypeName = "decimal(18,2)")]
        public decimal DefaultMonthlyFee { get; set; } = 0;

        public bool DarkMode { get; set; } = false;

        public bool AutoBackup { get; set; } = true;

        public int BackupIntervalDays { get; set; } = 1;

        [StringLength(500)]
        public string BackupPath { get; set; } = string.Empty;

        public int MaxAbsenceWarning { get; set; } = 3;

        [StringLength(1000)]
        public string ReportHeader { get; set; } = string.Empty;

        [StringLength(1000)]
        public string ReportFooter { get; set; } = string.Empty;

        public DateTime CreatedAt { get; set; } = DateTime.Now;
        public DateTime? UpdatedAt { get; set; }
    }
}
