using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace EduTrackForWin.Models
{
    public enum PaymentType
    {
        Subscription = 1,    // اشتراك
        Discount = 2,        // خصم
        Fine = 3,           // غرامة
        Other = 4           // أخرى
    }

    public class Payment
    {
        [Key]
        public int Id { get; set; }

        [Column(TypeName = "decimal(18,2)")]
        public decimal Amount { get; set; }

        public DateTime PaymentDate { get; set; }

        public PaymentType Type { get; set; } = PaymentType.Subscription;

        [StringLength(200)]
        public string Description { get; set; } = string.Empty;

        [StringLength(100)]
        public string ReceiptNumber { get; set; } = string.Empty;

        [StringLength(50)]
        public string PaymentMethod { get; set; } = "نقدي"; // نقدي، بنكي، إلخ

        [StringLength(500)]
        public string Notes { get; set; } = string.Empty;

        // Foreign Key
        public int StudentId { get; set; }

        // Navigation Properties
        [ForeignKey("StudentId")]
        public virtual Student Student { get; set; } = null!;

        // Computed Properties
        [NotMapped]
        public string TypeName => Type switch
        {
            PaymentType.Subscription => "اشتراك",
            PaymentType.Discount => "خصم",
            PaymentType.Fine => "غرامة",
            PaymentType.Other => "أخرى",
            _ => "غير محدد"
        };

        [NotMapped]
        public string StudentName => Student?.Name ?? "";

        [NotMapped]
        public string FormattedAmount => $"{Amount:C}";

        public DateTime CreatedAt { get; set; } = DateTime.Now;
        public DateTime? UpdatedAt { get; set; }
    }
}
