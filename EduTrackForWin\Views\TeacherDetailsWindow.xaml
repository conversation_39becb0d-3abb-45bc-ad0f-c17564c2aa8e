<Window x:Class="EduTrackForWin.Views.TeacherDetailsWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:EduTrackForWin.Views"
        mc:Ignorable="d"
        Title="تفاصيل المعلم" Height="700" Width="800"
        WindowStartupLocation="CenterOwner">

    <Window.Resources>
        <!-- Card Style -->
        <Style x:Key="CardStyle" TargetType="Border">
            <Setter Property="Background" Value="White"/>
            <Setter Property="CornerRadius" Value="8"/>
            <Setter Property="Padding" Value="20"/>
            <Setter Property="Margin" Value="10"/>
            <Setter Property="Effect">
                <Setter.Value>
                    <DropShadowEffect Color="Gray" Direction="270" ShadowDepth="2" Opacity="0.3" BlurRadius="5"/>
                </Setter.Value>
            </Setter>
        </Style>

        <!-- Info Label Style -->
        <Style x:Key="InfoLabelStyle" TargetType="TextBlock">
            <Setter Property="FontWeight" Value="SemiBold"/>
            <Setter Property="Foreground" Value="#424242"/>
            <Setter Property="Margin" Value="0,5,10,5"/>
        </Style>

        <!-- Info Value Style -->
        <Style x:Key="InfoValueStyle" TargetType="TextBlock">
            <Setter Property="Foreground" Value="#212121"/>
            <Setter Property="Margin" Value="0,5"/>
        </Style>

        <!-- Section Header Style -->
        <Style x:Key="SectionHeaderStyle" TargetType="TextBlock">
            <Setter Property="FontSize" Value="18"/>
            <Setter Property="FontWeight" Value="Bold"/>
            <Setter Property="Foreground" Value="#2196F3"/>
            <Setter Property="Margin" Value="0,0,0,15"/>
        </Style>
    </Window.Resources>

    <Grid Background="#F5F5F5">
        <ScrollViewer VerticalScrollBarVisibility="Auto" Margin="20">
            <StackPanel>
                <!-- Header -->
                <Border Style="{StaticResource CardStyle}" Background="#E3F2FD">
                    <StackPanel>
                        <TextBlock Name="TxtTeacherName" Text="اسم المعلم" FontSize="24" FontWeight="Bold" 
                                 HorizontalAlignment="Center" Foreground="#1976D2"/>
                        <TextBlock Text="تفاصيل المعلم الكاملة" FontSize="14" 
                                 HorizontalAlignment="Center" Foreground="#424242" Margin="0,5,0,0"/>
                    </StackPanel>
                </Border>

                <!-- Basic Information -->
                <Border Style="{StaticResource CardStyle}">
                    <StackPanel>
                        <TextBlock Text="المعلومات الأساسية" Style="{StaticResource SectionHeaderStyle}"/>
                        
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            
                            <StackPanel Grid.Column="0">
                                <StackPanel Orientation="Horizontal">
                                    <TextBlock Text="الاسم:" Style="{StaticResource InfoLabelStyle}"/>
                                    <TextBlock Name="TxtName" Style="{StaticResource InfoValueStyle}"/>
                                </StackPanel>
                                
                                <StackPanel Orientation="Horizontal">
                                    <TextBlock Text="التخصص:" Style="{StaticResource InfoLabelStyle}"/>
                                    <TextBlock Name="TxtSpecialization" Style="{StaticResource InfoValueStyle}"/>
                                </StackPanel>
                                
                                <StackPanel Orientation="Horizontal">
                                    <TextBlock Text="رقم الهاتف:" Style="{StaticResource InfoLabelStyle}"/>
                                    <TextBlock Name="TxtPhone" Style="{StaticResource InfoValueStyle}"/>
                                </StackPanel>
                            </StackPanel>
                            
                            <StackPanel Grid.Column="1">
                                <StackPanel Orientation="Horizontal">
                                    <TextBlock Text="البريد الإلكتروني:" Style="{StaticResource InfoLabelStyle}"/>
                                    <TextBlock Name="TxtEmail" Style="{StaticResource InfoValueStyle}"/>
                                </StackPanel>
                                
                                <StackPanel Orientation="Horizontal">
                                    <TextBlock Text="تاريخ التسجيل:" Style="{StaticResource InfoLabelStyle}"/>
                                    <TextBlock Name="TxtCreatedAt" Style="{StaticResource InfoValueStyle}"/>
                                </StackPanel>
                                
                                <StackPanel Orientation="Horizontal">
                                    <TextBlock Text="عدد المجموعات:" Style="{StaticResource InfoLabelStyle}"/>
                                    <TextBlock Name="TxtTotalGroups" Style="{StaticResource InfoValueStyle}"/>
                                </StackPanel>
                            </StackPanel>
                        </Grid>
                        
                        <StackPanel Margin="0,10,0,0">
                            <TextBlock Text="ملاحظات:" Style="{StaticResource InfoLabelStyle}"/>
                            <TextBlock Name="TxtNotes" Style="{StaticResource InfoValueStyle}" TextWrapping="Wrap"/>
                        </StackPanel>
                    </StackPanel>
                </Border>

                <!-- Salary Information -->
                <Border Style="{StaticResource CardStyle}">
                    <StackPanel>
                        <TextBlock Text="معلومات الراتب" Style="{StaticResource SectionHeaderStyle}"/>
                        
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            
                            <StackPanel Grid.Column="0">
                                <TextBlock Text="نوع الراتب" Style="{StaticResource InfoLabelStyle}"/>
                                <TextBlock Name="TxtSalaryType" Style="{StaticResource InfoValueStyle}" 
                                         FontSize="16" FontWeight="Bold" Foreground="#2196F3"/>
                            </StackPanel>
                            
                            <StackPanel Grid.Column="1">
                                <TextBlock Text="قيمة الراتب" Style="{StaticResource InfoLabelStyle}"/>
                                <TextBlock Name="TxtSalaryAmount" Style="{StaticResource InfoValueStyle}" 
                                         FontSize="16" FontWeight="Bold" Foreground="#4CAF50"/>
                            </StackPanel>
                            
                            <StackPanel Grid.Column="2">
                                <TextBlock Text="الراتب المحسوب" Style="{StaticResource InfoLabelStyle}"/>
                                <TextBlock Name="TxtCalculatedSalary" Style="{StaticResource InfoValueStyle}" 
                                         FontSize="16" FontWeight="Bold" Foreground="#FF9800"/>
                            </StackPanel>
                        </Grid>
                    </StackPanel>
                </Border>

                <!-- Groups Information -->
                <Border Style="{StaticResource CardStyle}">
                    <StackPanel>
                        <TextBlock Text="المجموعات التي يدرسها" Style="{StaticResource SectionHeaderStyle}"/>
                        
                        <DataGrid Name="DgGroups" 
                                AutoGenerateColumns="False" 
                                CanUserAddRows="False" 
                                CanUserDeleteRows="False"
                                IsReadOnly="True"
                                GridLinesVisibility="Horizontal"
                                HeadersVisibility="Column"
                                BorderThickness="0"
                                Background="Transparent"
                                RowBackground="White"
                                AlternatingRowBackground="#F5F5F5"
                                Height="150">
                            
                            <DataGrid.Columns>
                                <DataGridTextColumn Header="اسم المجموعة" Binding="{Binding Name}" Width="200"/>
                                <DataGridTextColumn Header="المادة" Binding="{Binding Subject}" Width="150"/>
                                <DataGridTextColumn Header="الصف" Binding="{Binding Grade}" Width="100"/>
                                <DataGridTextColumn Header="عدد الطلاب" Binding="{Binding StudentCount}" Width="100"/>
                                <DataGridTextColumn Header="الرسوم الشهرية" Binding="{Binding MonthlyFee, StringFormat='{}{0:C}'}" Width="120"/>
                                <DataGridTextColumn Header="القاعة" Binding="{Binding Room}" Width="*"/>
                            </DataGrid.Columns>
                        </DataGrid>
                    </StackPanel>
                </Border>

                <!-- Performance Statistics -->
                <Border Style="{StaticResource CardStyle}">
                    <StackPanel>
                        <TextBlock Text="إحصائيات الأداء" Style="{StaticResource SectionHeaderStyle}"/>
                        
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            
                            <StackPanel Grid.Column="0">
                                <TextBlock Text="حصص هذا الشهر" Style="{StaticResource InfoLabelStyle}"/>
                                <TextBlock Name="TxtSessionsThisMonth" Style="{StaticResource InfoValueStyle}" 
                                         FontSize="18" FontWeight="Bold" Foreground="#2196F3"/>
                            </StackPanel>
                            
                            <StackPanel Grid.Column="1">
                                <TextBlock Text="إجمالي الطلاب" Style="{StaticResource InfoLabelStyle}"/>
                                <TextBlock Name="TxtTotalStudents" Style="{StaticResource InfoValueStyle}" 
                                         FontSize="18" FontWeight="Bold" Foreground="#4CAF50"/>
                            </StackPanel>
                            
                            <StackPanel Grid.Column="2">
                                <TextBlock Text="الإيرادات المتوقعة" Style="{StaticResource InfoLabelStyle}"/>
                                <TextBlock Name="TxtExpectedRevenue" Style="{StaticResource InfoValueStyle}" 
                                         FontSize="18" FontWeight="Bold" Foreground="#FF9800"/>
                            </StackPanel>
                        </Grid>
                    </StackPanel>
                </Border>

                <!-- Recent Sessions -->
                <Border Style="{StaticResource CardStyle}">
                    <StackPanel>
                        <TextBlock Text="آخر الحصص" Style="{StaticResource SectionHeaderStyle}"/>
                        
                        <DataGrid Name="DgRecentSessions" 
                                AutoGenerateColumns="False" 
                                CanUserAddRows="False" 
                                CanUserDeleteRows="False"
                                IsReadOnly="True"
                                GridLinesVisibility="Horizontal"
                                HeadersVisibility="Column"
                                BorderThickness="0"
                                Background="Transparent"
                                RowBackground="White"
                                AlternatingRowBackground="#F5F5F5"
                                Height="150">
                            
                            <DataGrid.Columns>
                                <DataGridTextColumn Header="التاريخ" Binding="{Binding Date, StringFormat='{}{0:yyyy/MM/dd}'}" Width="100"/>
                                <DataGridTextColumn Header="الوقت" Binding="{Binding SessionTime}" Width="120"/>
                                <DataGridTextColumn Header="المجموعة" Binding="{Binding Group.Name}" Width="150"/>
                                <DataGridTextColumn Header="الموضوع" Binding="{Binding Topic}" Width="*"/>
                                <DataGridTemplateColumn Header="الحالة" Width="80">
                                    <DataGridTemplateColumn.CellTemplate>
                                        <DataTemplate>
                                            <Border CornerRadius="10" Padding="8,4">
                                                <Border.Style>
                                                    <Style TargetType="Border">
                                                        <Style.Triggers>
                                                            <DataTrigger Binding="{Binding IsCompleted}" Value="True">
                                                                <Setter Property="Background" Value="#4CAF50"/>
                                                            </DataTrigger>
                                                            <DataTrigger Binding="{Binding IsCompleted}" Value="False">
                                                                <Setter Property="Background" Value="#FF9800"/>
                                                            </DataTrigger>
                                                        </Style.Triggers>
                                                    </Style>
                                                </Border.Style>
                                                <TextBlock Text="{Binding IsCompleted, Converter={StaticResource BoolToStatusConverter}}" 
                                                         Foreground="White" FontSize="10" HorizontalAlignment="Center"/>
                                            </Border>
                                        </DataTemplate>
                                    </DataGridTemplateColumn.CellTemplate>
                                </DataGridTemplateColumn>
                            </DataGrid.Columns>
                        </DataGrid>
                    </StackPanel>
                </Border>

                <!-- Action Buttons -->
                <Border Style="{StaticResource CardStyle}">
                    <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                        <Button Name="BtnEdit" Content="تعديل البيانات"
                              Background="#2196F3" Foreground="White"
                              Padding="20,10" Margin="10" BorderThickness="0"
                              Click="BtnEdit_Click">
                            <Button.Template>
                                <ControlTemplate TargetType="Button">
                                    <Border Background="{TemplateBinding Background}"
                                          CornerRadius="5">
                                        <ContentPresenter HorizontalAlignment="Center"
                                                        VerticalAlignment="Center"
                                                        Margin="{TemplateBinding Padding}"/>
                                    </Border>
                                </ControlTemplate>
                            </Button.Template>
                        </Button>

                        <Button Name="BtnViewSchedule" Content="عرض الجدول"
                              Background="#4CAF50" Foreground="White"
                              Padding="20,10" Margin="10" BorderThickness="0"
                              Click="BtnViewSchedule_Click">
                            <Button.Template>
                                <ControlTemplate TargetType="Button">
                                    <Border Background="{TemplateBinding Background}"
                                          CornerRadius="5">
                                        <ContentPresenter HorizontalAlignment="Center"
                                                        VerticalAlignment="Center"
                                                        Margin="{TemplateBinding Padding}"/>
                                    </Border>
                                </ControlTemplate>
                            </Button.Template>
                        </Button>

                        <Button Name="BtnSalaryReport" Content="تقرير الراتب"
                              Background="#FF9800" Foreground="White"
                              Padding="20,10" Margin="10" BorderThickness="0"
                              Click="BtnSalaryReport_Click">
                            <Button.Template>
                                <ControlTemplate TargetType="Button">
                                    <Border Background="{TemplateBinding Background}"
                                          CornerRadius="5">
                                        <ContentPresenter HorizontalAlignment="Center"
                                                        VerticalAlignment="Center"
                                                        Margin="{TemplateBinding Padding}"/>
                                    </Border>
                                </ControlTemplate>
                            </Button.Template>
                        </Button>

                        <Button Name="BtnClose" Content="إغلاق"
                              Background="#757575" Foreground="White"
                              Padding="20,10" Margin="10" BorderThickness="0"
                              Click="BtnClose_Click">
                            <Button.Template>
                                <ControlTemplate TargetType="Button">
                                    <Border Background="{TemplateBinding Background}"
                                          CornerRadius="5">
                                        <ContentPresenter HorizontalAlignment="Center"
                                                        VerticalAlignment="Center"
                                                        Margin="{TemplateBinding Padding}"/>
                                    </Border>
                                </ControlTemplate>
                            </Button.Template>
                        </Button>
                    </StackPanel>
                </Border>
            </StackPanel>
        </ScrollViewer>
    </Grid>
</Window>
