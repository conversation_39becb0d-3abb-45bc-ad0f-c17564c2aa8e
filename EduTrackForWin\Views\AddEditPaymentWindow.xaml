<Window x:Class="EduTrackForWin.Views.AddEditPaymentWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        mc:Ignorable="d"
        Title="إضافة دفعة جديدة" Height="600" Width="500"
        WindowStartupLocation="CenterScreen"
        ResizeMode="NoResize">

    <Window.Resources>
        <!-- Styles -->
        <Style x:Key="LabelStyle" TargetType="TextBlock">
            <Setter Property="FontWeight" Value="SemiBold"/>
            <Setter Property="Margin" Value="0,10,0,5"/>
            <Setter Property="Foreground" Value="#424242"/>
        </Style>

        <Style x:Key="InputFieldStyle" TargetType="TextBox">
            <Setter Property="Padding" Value="10"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="BorderBrush" Value="#E0E0E0"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="Margin" Value="0,5,0,15"/>
        </Style>

        <Style x:Key="ComboBoxStyle" TargetType="ComboBox">
            <Setter Property="Padding" Value="10"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="BorderBrush" Value="#E0E0E0"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="Margin" Value="0,5,0,15"/>
        </Style>

        <Style x:Key="DatePickerStyle" TargetType="DatePicker">
            <Setter Property="Padding" Value="10"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="BorderBrush" Value="#E0E0E0"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="Margin" Value="0,5,0,15"/>
            <Setter Property="Height" Value="40"/>
        </Style>

        <Style x:Key="PrimaryButtonStyle" TargetType="Button">
            <Setter Property="Background" Value="#4CAF50"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Padding" Value="20,10"/>
            <Setter Property="Margin" Value="10"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="FontWeight" Value="SemiBold"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}" 
                              CornerRadius="5">
                            <ContentPresenter HorizontalAlignment="Center" 
                                            VerticalAlignment="Center"
                                            Margin="{TemplateBinding Padding}"/>
                        </Border>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <Style x:Key="SecondaryButtonStyle" TargetType="Button">
            <Setter Property="Background" Value="#757575"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Padding" Value="20,10"/>
            <Setter Property="Margin" Value="10"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="FontWeight" Value="SemiBold"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}" 
                              CornerRadius="5">
                            <ContentPresenter HorizontalAlignment="Center" 
                                            VerticalAlignment="Center"
                                            Margin="{TemplateBinding Padding}"/>
                        </Border>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>
    </Window.Resources>

    <Grid Background="#F5F5F5">
        <Border Background="White" CornerRadius="10" Margin="20" Padding="30">
            <Border.Effect>
                <DropShadowEffect Color="Gray" Direction="270" ShadowDepth="3" Opacity="0.3" BlurRadius="10"/>
            </Border.Effect>

            <ScrollViewer VerticalScrollBarVisibility="Auto">
                <StackPanel>
                    <!-- Header -->
                    <TextBlock Name="TxtTitle" Text="إضافة دفعة جديدة" 
                             FontSize="24" FontWeight="Bold" 
                             HorizontalAlignment="Center" 
                             Foreground="#4CAF50" 
                             Margin="0,0,0,30"/>

                    <!-- Student -->
                    <TextBlock Text="الطالب *" Style="{StaticResource LabelStyle}"/>
                    <ComboBox Name="CmbStudent" Style="{StaticResource ComboBoxStyle}" 
                            DisplayMemberPath="Name" SelectedValuePath="Id"/>

                    <!-- Payment Type -->
                    <TextBlock Text="نوع الدفعة *" Style="{StaticResource LabelStyle}"/>
                    <ComboBox Name="CmbPaymentType" Style="{StaticResource ComboBoxStyle}">
                        <ComboBoxItem Content="اشتراك شهري" IsSelected="True"/>
                        <ComboBoxItem Content="رسوم تسجيل"/>
                        <ComboBoxItem Content="رسوم إضافية"/>
                        <ComboBoxItem Content="خصم"/>
                        <ComboBoxItem Content="غرامة تأخير"/>
                    </ComboBox>

                    <!-- Amount -->
                    <TextBlock Text="المبلغ *" Style="{StaticResource LabelStyle}"/>
                    <TextBox Name="TxtAmount" Style="{StaticResource InputFieldStyle}" 
                           Text="0"/>

                    <!-- Payment Date -->
                    <TextBlock Text="تاريخ الدفع *" Style="{StaticResource LabelStyle}"/>
                    <DatePicker Name="DpPaymentDate" Style="{StaticResource DatePickerStyle}"/>

                    <!-- Payment Method -->
                    <TextBlock Text="طريقة الدفع *" Style="{StaticResource LabelStyle}"/>
                    <ComboBox Name="CmbPaymentMethod" Style="{StaticResource ComboBoxStyle}">
                        <ComboBoxItem Content="نقداً" IsSelected="True"/>
                        <ComboBoxItem Content="بطاقة ائتمان"/>
                        <ComboBoxItem Content="تحويل بنكي"/>
                        <ComboBoxItem Content="شيك"/>
                        <ComboBoxItem Content="محفظة إلكترونية"/>
                    </ComboBox>

                    <!-- Notes -->
                    <TextBlock Text="ملاحظات" Style="{StaticResource LabelStyle}"/>
                    <TextBox Name="TxtNotes" Style="{StaticResource InputFieldStyle}" 
                           Height="80" TextWrapping="Wrap" AcceptsReturn="True"
                           VerticalScrollBarVisibility="Auto"/>

                    <!-- Buttons -->
                    <StackPanel Orientation="Horizontal" HorizontalAlignment="Center" Margin="0,30,0,0">
                        <Button Name="BtnSave" Content="حفظ" 
                              Style="{StaticResource PrimaryButtonStyle}" 
                              Click="BtnSave_Click"/>
                        <Button Name="BtnCancel" Content="إلغاء" 
                              Style="{StaticResource SecondaryButtonStyle}" 
                              Click="BtnCancel_Click"/>
                    </StackPanel>
                </StackPanel>
            </ScrollViewer>
        </Border>
    </Grid>
</Window>
