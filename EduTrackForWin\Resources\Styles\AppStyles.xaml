<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">

    <!-- Color Palette -->
    <SolidColorBrush x:Key="PrimaryColor" Color="#2E7D32"/>
    <SolidColorBrush x:Key="PrimaryLightColor" Color="#4CAF50"/>
    <SolidColorBrush x:Key="PrimaryDarkColor" Color="#1B5E20"/>
    <SolidColorBrush x:Key="SecondaryColor" Color="#2196F3"/>
    <SolidColorBrush x:Key="SecondaryLightColor" Color="#64B5F6"/>
    <SolidColorBrush x:Key="SecondaryDarkColor" Color="#1976D2"/>
    <SolidColorBrush x:Key="AccentColor" Color="#FF9800"/>
    <SolidColorBrush x:Key="ErrorColor" Color="#F44336"/>
    <SolidColorBrush x:Key="WarningColor" Color="#FF9800"/>
    <SolidColorBrush x:Key="SuccessColor" Color="#4CAF50"/>
    <SolidColorBrush x:Key="InfoColor" Color="#2196F3"/>
    <SolidColorBrush x:Key="SurfaceColor" Color="#FFFFFF"/>
    <SolidColorBrush x:Key="BackgroundColor" Color="#F8F9FA"/>
    <SolidColorBrush x:Key="CardColor" Color="#FFFFFF"/>
    <SolidColorBrush x:Key="TextPrimaryColor" Color="#212121"/>
    <SolidColorBrush x:Key="TextSecondaryColor" Color="#757575"/>
    <SolidColorBrush x:Key="TextHintColor" Color="#BDBDBD"/>
    <SolidColorBrush x:Key="BorderColor" Color="#E0E0E0"/>
    <SolidColorBrush x:Key="DividerColor" Color="#F5F5F5"/>
    <SolidColorBrush x:Key="ShadowColor" Color="#000000"/>
    
    <!-- Gradients -->
    <LinearGradientBrush x:Key="PrimaryGradient" StartPoint="0,0" EndPoint="1,1">
        <GradientStop Color="#4CAF50" Offset="0"/>
        <GradientStop Color="#2E7D32" Offset="1"/>
    </LinearGradientBrush>
    
    <LinearGradientBrush x:Key="SecondaryGradient" StartPoint="0,0" EndPoint="1,1">
        <GradientStop Color="#64B5F6" Offset="0"/>
        <GradientStop Color="#2196F3" Offset="1"/>
    </LinearGradientBrush>
    
    <LinearGradientBrush x:Key="DarkGradient" StartPoint="0,0" EndPoint="1,1">
        <GradientStop Color="#1B5E20" Offset="0"/>
        <GradientStop Color="#2E7D32" Offset="1"/>
    </LinearGradientBrush>

    <!-- Animations -->
    <Storyboard x:Key="FadeInAnimation">
        <DoubleAnimation Storyboard.TargetProperty="Opacity" From="0" To="1" Duration="0:0:0.3"/>
    </Storyboard>
    
    <Storyboard x:Key="SlideInFromLeftAnimation">
        <DoubleAnimation Storyboard.TargetProperty="(UIElement.RenderTransform).(TranslateTransform.X)" 
                         From="-100" To="0" Duration="0:0:0.4">
            <DoubleAnimation.EasingFunction>
                <CubicEase EasingMode="EaseOut"/>
            </DoubleAnimation.EasingFunction>
        </DoubleAnimation>
        <DoubleAnimation Storyboard.TargetProperty="Opacity" From="0" To="1" Duration="0:0:0.4"/>
    </Storyboard>
    
    <Storyboard x:Key="SlideInFromRightAnimation">
        <DoubleAnimation Storyboard.TargetProperty="(UIElement.RenderTransform).(TranslateTransform.X)" 
                         From="100" To="0" Duration="0:0:0.4">
            <DoubleAnimation.EasingFunction>
                <CubicEase EasingMode="EaseOut"/>
            </DoubleAnimation.EasingFunction>
        </DoubleAnimation>
        <DoubleAnimation Storyboard.TargetProperty="Opacity" From="0" To="1" Duration="0:0:0.4"/>
    </Storyboard>
    
    <Storyboard x:Key="ScaleInAnimation">
        <DoubleAnimation Storyboard.TargetProperty="(UIElement.RenderTransform).(ScaleTransform.ScaleX)" 
                         From="0.8" To="1" Duration="0:0:0.3">
            <DoubleAnimation.EasingFunction>
                <BackEase EasingMode="EaseOut" Amplitude="0.3"/>
            </DoubleAnimation.EasingFunction>
        </DoubleAnimation>
        <DoubleAnimation Storyboard.TargetProperty="(UIElement.RenderTransform).(ScaleTransform.ScaleY)" 
                         From="0.8" To="1" Duration="0:0:0.3">
            <DoubleAnimation.EasingFunction>
                <BackEase EasingMode="EaseOut" Amplitude="0.3"/>
            </DoubleAnimation.EasingFunction>
        </DoubleAnimation>
        <DoubleAnimation Storyboard.TargetProperty="Opacity" From="0" To="1" Duration="0:0:0.3"/>
    </Storyboard>
    
    <Storyboard x:Key="RippleAnimation">
        <DoubleAnimation Storyboard.TargetProperty="(UIElement.RenderTransform).(ScaleTransform.ScaleX)" 
                         To="1.5" Duration="0:0:0.3" FillBehavior="Stop"/>
        <DoubleAnimation Storyboard.TargetProperty="(UIElement.RenderTransform).(ScaleTransform.ScaleY)" 
                         To="1.5" Duration="0:0:0.3" FillBehavior="Stop"/>
        <DoubleAnimation Storyboard.TargetProperty="Opacity" From="1" To="0" Duration="0:0:0.3" FillBehavior="Stop"/>
    </Storyboard>
    
    <Storyboard x:Key="PageTransitionAnimation">
        <DoubleAnimation Storyboard.TargetProperty="(UIElement.RenderTransform).(TranslateTransform.X)" 
                         From="0" To="10" Duration="0:0:0.2"/>
        <DoubleAnimation Storyboard.TargetProperty="Opacity" From="1" To="0.8" Duration="0:0:0.2"/>
    </Storyboard>

    <!-- Button Hover Animation -->
    <Storyboard x:Key="ButtonHoverAnimation">
        <DoubleAnimation Storyboard.TargetProperty="(UIElement.RenderTransform).(ScaleTransform.ScaleX)" 
                         To="1.05" Duration="0:0:0.2"/>
        <DoubleAnimation Storyboard.TargetProperty="(UIElement.RenderTransform).(ScaleTransform.ScaleY)" 
                         To="1.05" Duration="0:0:0.2"/>
    </Storyboard>
    
    <Storyboard x:Key="ButtonPressedAnimation">
        <DoubleAnimation Storyboard.TargetProperty="(UIElement.RenderTransform).(ScaleTransform.ScaleX)" 
                         To="0.95" Duration="0:0:0.1"/>
        <DoubleAnimation Storyboard.TargetProperty="(UIElement.RenderTransform).(ScaleTransform.ScaleY)" 
                         To="0.95" Duration="0:0:0.1"/>
    </Storyboard>
    
    <Storyboard x:Key="ButtonRippleAnimation">
        <DoubleAnimation Storyboard.TargetProperty="(UIElement.RenderTransform).(ScaleTransform.ScaleX)" 
                         To="1.5" Duration="0:0:0.3" FillBehavior="Stop"/>
        <DoubleAnimation Storyboard.TargetProperty="(UIElement.RenderTransform).(ScaleTransform.ScaleY)" 
                         To="1.5" Duration="0:0:0.3" FillBehavior="Stop"/>
        <DoubleAnimation Storyboard.TargetProperty="Opacity" From="1" To="0" Duration="0:0:0.3" FillBehavior="Stop"/>
    </Storyboard>

    <!-- Card Hover Animation -->
    <Storyboard x:Key="CardHoverAnimation">
        <DoubleAnimation Storyboard.TargetProperty="(UIElement.Effect).(DropShadowEffect.ShadowDepth)" 
                         To="12" Duration="0:0:0.3"/>
        <DoubleAnimation Storyboard.TargetProperty="(UIElement.Effect).(DropShadowEffect.BlurRadius)" 
                         To="20" Duration="0:0:0.3"/>
        <DoubleAnimation Storyboard.TargetProperty="(UIElement.RenderTransform).(TranslateTransform.Y)" 
                         To="-4" Duration="0:0:0.3"/>
    </Storyboard>
    
    <Storyboard x:Key="CardNormalAnimation">
        <DoubleAnimation Storyboard.TargetProperty="(UIElement.Effect).(DropShadowEffect.ShadowDepth)" 
                         To="5" Duration="0:0:0.3"/>
        <DoubleAnimation Storyboard.TargetProperty="(UIElement.Effect).(DropShadowEffect.BlurRadius)" 
                         To="10" Duration="0:0:0.3"/>
        <DoubleAnimation Storyboard.TargetProperty="(UIElement.RenderTransform).(TranslateTransform.Y)" 
                         To="0" Duration="0:0:0.3"/>
    </Storyboard>

    <!-- Loading Animation -->
    <Storyboard x:Key="LoadingAnimation" RepeatBehavior="Forever">
        <DoubleAnimation Storyboard.TargetProperty="(UIElement.RenderTransform).(RotateTransform.Angle)" 
                         From="0" To="360" Duration="0:0:1"/>
    </Storyboard>

    <!-- Pulse Animation -->
    <Storyboard x:Key="PulseAnimation" RepeatBehavior="Forever" AutoReverse="True">
        <DoubleAnimation Storyboard.TargetProperty="Opacity" From="1" To="0.6" Duration="0:0:1"/>
    </Storyboard>

    <!-- Typography -->
    <Style x:Key="HeadingStyle" TargetType="TextBlock">
        <Setter Property="FontFamily" Value="Segoe UI"/>
        <Setter Property="FontSize" Value="28"/>
        <Setter Property="FontWeight" Value="Bold"/>
        <Setter Property="Foreground" Value="{StaticResource TextPrimaryColor}"/>
        <Setter Property="Margin" Value="0,0,0,16"/>
    </Style>
    
    <Style x:Key="SubHeadingStyle" TargetType="TextBlock">
        <Setter Property="FontFamily" Value="Segoe UI"/>
        <Setter Property="FontSize" Value="20"/>
        <Setter Property="FontWeight" Value="SemiBold"/>
        <Setter Property="Foreground" Value="{StaticResource TextPrimaryColor}"/>
        <Setter Property="Margin" Value="0,0,0,12"/>
    </Style>
    
    <Style x:Key="TitleStyle" TargetType="TextBlock">
        <Setter Property="FontFamily" Value="Segoe UI"/>
        <Setter Property="FontSize" Value="16"/>
        <Setter Property="FontWeight" Value="SemiBold"/>
        <Setter Property="Foreground" Value="{StaticResource TextPrimaryColor}"/>
        <Setter Property="Margin" Value="0,0,0,8"/>
    </Style>
    
    <Style x:Key="BodyStyle" TargetType="TextBlock">
        <Setter Property="FontFamily" Value="Segoe UI"/>
        <Setter Property="FontSize" Value="14"/>
        <Setter Property="FontWeight" Value="Normal"/>
        <Setter Property="Foreground" Value="{StaticResource TextPrimaryColor}"/>
        <Setter Property="LineHeight" Value="20"/>
    </Style>
    
    <Style x:Key="CaptionStyle" TargetType="TextBlock">
        <Setter Property="FontFamily" Value="Segoe UI"/>
        <Setter Property="FontSize" Value="12"/>
        <Setter Property="FontWeight" Value="Normal"/>
        <Setter Property="Foreground" Value="{StaticResource TextSecondaryColor}"/>
    </Style>

    <!-- Modern Card Style -->
    <Style x:Key="ModernCardStyle" TargetType="Border">
        <Setter Property="Background" Value="{StaticResource CardColor}"/>
        <Setter Property="CornerRadius" Value="16"/>
        <Setter Property="Padding" Value="28"/>
        <Setter Property="Margin" Value="12"/>
        <Setter Property="RenderTransform">
            <Setter.Value>
                <TransformGroup>
                    <ScaleTransform/>
                    <SkewTransform/>
                    <RotateTransform/>
                    <TranslateTransform/>
                </TransformGroup>
            </Setter.Value>
        </Setter>
        <Setter Property="Effect">
            <Setter.Value>
                <DropShadowEffect Color="{Binding Source={StaticResource ShadowColor}, Path=Color}" Direction="270" ShadowDepth="5" 
                                Opacity="0.15" BlurRadius="12"/>
            </Setter.Value>
        </Setter>
        <Style.Triggers>
            <EventTrigger RoutedEvent="MouseEnter">
                <BeginStoryboard Storyboard="{StaticResource CardHoverAnimation}"/>
            </EventTrigger>
            <EventTrigger RoutedEvent="MouseLeave">
                <BeginStoryboard Storyboard="{StaticResource CardNormalAnimation}"/>
            </EventTrigger>
            <EventTrigger RoutedEvent="Loaded">
                <BeginStoryboard Storyboard="{StaticResource ScaleInAnimation}"/>
            </EventTrigger>
        </Style.Triggers>
    </Style>

    <!-- Stat Card Style -->
    <Style x:Key="StatCardStyle" TargetType="Border">
        <Setter Property="Background" Value="{StaticResource CardColor}"/>
        <Setter Property="CornerRadius" Value="20"/>
        <Setter Property="Padding" Value="24"/>
        <Setter Property="Margin" Value="12"/>
        <Setter Property="RenderTransform">
            <Setter.Value>
                <TransformGroup>
                    <ScaleTransform/>
                    <SkewTransform/>
                    <RotateTransform/>
                    <TranslateTransform/>
                </TransformGroup>
            </Setter.Value>
        </Setter>
        <Setter Property="Effect">
            <Setter.Value>
                <DropShadowEffect Color="{Binding Source={StaticResource ShadowColor}, Path=Color}" Direction="270" ShadowDepth="6" 
                                Opacity="0.2" BlurRadius="16"/>
            </Setter.Value>
        </Setter>
        <Style.Triggers>
            <EventTrigger RoutedEvent="MouseEnter">
                <BeginStoryboard Storyboard="{StaticResource CardHoverAnimation}"/>
            </EventTrigger>
            <EventTrigger RoutedEvent="MouseLeave">
                <BeginStoryboard Storyboard="{StaticResource CardNormalAnimation}"/>
            </EventTrigger>
            <EventTrigger RoutedEvent="Loaded">
                <BeginStoryboard Storyboard="{StaticResource ScaleInAnimation}"/>
            </EventTrigger>
        </Style.Triggers>
    </Style>

</ResourceDictionary>
