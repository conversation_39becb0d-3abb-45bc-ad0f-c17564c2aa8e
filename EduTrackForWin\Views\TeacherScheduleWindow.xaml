<Window x:Class="EduTrackForWin.Views.TeacherScheduleWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:EduTrackForWin.Views"
        mc:Ignorable="d"
        Title="جدول المعلم" Height="600" Width="800"
        WindowStartupLocation="CenterOwner">

    <Grid Background="#F5F5F5">
        <Border Background="White" CornerRadius="8" Margin="50" Padding="40">
            <Border.Effect>
                <DropShadowEffect Color="Gray" Direction="270" ShadowDepth="2" Opacity="0.3" BlurRadius="5"/>
            </Border.Effect>
            
            <StackPanel HorizontalAlignment="Center" VerticalAlignment="Center">
                <TextBlock Text="📅" FontSize="72" HorizontalAlignment="Center" Margin="0,0,0,20"/>
                <TextBlock Name="TxtTeacherName" Text="جدول المعلم" FontSize="28" FontWeight="Bold" 
                         HorizontalAlignment="Center" Foreground="#2196F3" Margin="0,0,0,10"/>
                <TextBlock Text="عرض الجدول الأسبوعي للمعلم" FontSize="16" 
                         HorizontalAlignment="Center" Foreground="#757575" Margin="0,0,0,20"/>
                <Button Content="إغلاق" Background="#757575" Foreground="White"
                      Padding="20,10" BorderThickness="0"
                      Click="BtnClose_Click">
                    <Button.Template>
                        <ControlTemplate TargetType="Button">
                            <Border Background="{TemplateBinding Background}"
                                  CornerRadius="5">
                                <ContentPresenter HorizontalAlignment="Center"
                                                VerticalAlignment="Center"
                                                Margin="{TemplateBinding Padding}"/>
                            </Border>
                        </ControlTemplate>
                    </Button.Template>
                </Button>
            </StackPanel>
        </Border>
    </Grid>
</Window>
