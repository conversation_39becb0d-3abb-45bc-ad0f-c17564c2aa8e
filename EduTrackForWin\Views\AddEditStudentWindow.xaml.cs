using System.Windows;
using EduTrackForWin.Services;
using EduTrackForWin.Models;
using Microsoft.EntityFrameworkCore;
using EduTrackForWin.Data;

namespace EduTrackForWin.Views
{
    public partial class AddEditStudentWindow : Window
    {
        private readonly StudentService _studentService;
        private readonly EduTrackDbContext _context;
        private Student? _currentStudent;
        private bool _isEditMode;

        public AddEditStudentWindow(Student? student = null)
        {
            try
            {
                InitializeComponent();
                _studentService = new StudentService();
                _context = new EduTrackDbContext();
                _currentStudent = student;
                _isEditMode = student != null;

                InitializeWindow();
                LoadGroups();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تهيئة نافذة الطالب: {ex.Message}", "خطأ",
                              MessageBoxButton.OK, MessageBoxImage.Error);
                throw;
            }
        }

        private void InitializeWindow()
        {
            if (_isEditMode && _currentStudent != null)
            {
                TxtTitle.Text = "تعديل بيانات الطالب";
                Title = "تعديل بيانات الطالب";
                BtnSave.Content = "تحديث";
                LoadStudentData();
            }
            else
            {
                TxtTitle.Text = "إضافة طالب جديد";
                Title = "إضافة طالب جديد";
                BtnSave.Content = "حفظ";
            }
        }

        private async void LoadGroups()
        {
            try
            {
                var groups = await _context.Groups
                    .Include(g => g.Teacher)
                    .Where(g => g.IsActive)
                    .OrderBy(g => g.Name)
                    .ToListAsync();

                // Add empty option
                var groupsList = new List<Group> { new Group { Id = 0, Name = "-- اختر المجموعة --" } };
                groupsList.AddRange(groups);

                CmbGroup.ItemsSource = groupsList;
                CmbGroup.SelectedIndex = 0;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل المجموعات: {ex.Message}", "خطأ", 
                              MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void LoadStudentData()
        {
            if (_currentStudent == null) return;

            TxtName.Text = _currentStudent.Name;
            TxtPhone.Text = _currentStudent.Phone;
            TxtAddress.Text = _currentStudent.Address;
            TxtNotes.Text = _currentStudent.Notes;

            // Set grade
            if (!string.IsNullOrEmpty(_currentStudent.Grade))
            {
                foreach (var item in CmbGrade.Items.Cast<System.Windows.Controls.ComboBoxItem>())
                {
                    if (item.Content.ToString() == _currentStudent.Grade)
                    {
                        CmbGrade.SelectedItem = item;
                        break;
                    }
                }
            }

            // Set group (will be set after groups are loaded)
            if (_currentStudent.GroupId.HasValue)
            {
                CmbGroup.SelectedValue = _currentStudent.GroupId.Value;
            }
        }

        private async void BtnSave_Click(object sender, RoutedEventArgs e)
        {
            if (!ValidateInput())
                return;

            try
            {
                var student = _currentStudent ?? new Student();

                // Update student properties
                student.Name = TxtName.Text.Trim();
                student.Phone = TxtPhone.Text.Trim();
                student.Address = TxtAddress.Text.Trim();
                student.Notes = TxtNotes.Text.Trim();

                // Set grade
                if (CmbGrade.SelectedItem is System.Windows.Controls.ComboBoxItem selectedGrade)
                {
                    student.Grade = selectedGrade.Content.ToString() ?? "";
                }

                // Set group
                if (CmbGroup.SelectedValue is int groupId && groupId > 0)
                {
                    student.GroupId = groupId;
                }
                else
                {
                    student.GroupId = null;
                }

                if (_isEditMode)
                {
                    await _studentService.UpdateStudentAsync(student);
                    MessageBox.Show("تم تحديث بيانات الطالب بنجاح", "نجح", 
                                  MessageBoxButton.OK, MessageBoxImage.Information);
                }
                else
                {
                    await _studentService.AddStudentAsync(student);
                    MessageBox.Show("تم إضافة الطالب بنجاح", "نجح", 
                                  MessageBoxButton.OK, MessageBoxImage.Information);
                }

                DialogResult = true;
                Close();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في حفظ بيانات الطالب: {ex.Message}", "خطأ", 
                              MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private bool ValidateInput()
        {
            if (string.IsNullOrWhiteSpace(TxtName.Text))
            {
                MessageBox.Show("يرجى إدخال اسم الطالب", "خطأ في البيانات", 
                              MessageBoxButton.OK, MessageBoxImage.Warning);
                TxtName.Focus();
                return false;
            }

            if (TxtName.Text.Trim().Length < 2)
            {
                MessageBox.Show("يجب أن يكون اسم الطالب أكثر من حرفين", "خطأ في البيانات", 
                              MessageBoxButton.OK, MessageBoxImage.Warning);
                TxtName.Focus();
                return false;
            }

            return true;
        }

        private void BtnCancel_Click(object sender, RoutedEventArgs e)
        {
            DialogResult = false;
            Close();
        }

        protected override void OnClosed(EventArgs e)
        {
            _context?.Dispose();
            _studentService?.Dispose();
            base.OnClosed(e);
        }
    }
}
