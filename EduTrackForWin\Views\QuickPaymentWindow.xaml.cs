using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using EduTrackForWin.Data;
using EduTrackForWin.Models;
using EduTrackForWin.Services;
using Microsoft.EntityFrameworkCore;

namespace EduTrackForWin.Views
{
    /// <summary>
    /// Interaction logic for QuickPaymentWindow.xaml
    /// </summary>
    public partial class QuickPaymentWindow : Window
    {
        private readonly EduTrackDbContext _context;
        private readonly PaymentService _paymentService;
        private List<Student> _allStudents;
        private Student? _selectedStudent;

        public QuickPaymentWindow()
        {
            try
            {
                InitializeComponent();
                _context = new EduTrackDbContext();
                _paymentService = new PaymentService();
                _allStudents = new List<Student>();
                
                Loaded += QuickPaymentWindow_Loaded;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تهيئة نافذة الدفعة السريعة: {ex.Message}", "خطأ", 
                              MessageBoxButton.OK, MessageBoxImage.Error);
                throw;
            }
        }

        private async void QuickPaymentWindow_Loaded(object sender, RoutedEventArgs e)
        {
            await LoadStudents();
        }

        private async Task LoadStudents()
        {
            try
            {
                _allStudents = await _context.Students
                    .Include(s => s.Group)
                    .Include(s => s.Payments)
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل الطلاب: {ex.Message}", "خطأ", 
                              MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void TxtStudentSearch_TextChanged(object sender, TextChangedEventArgs e)
        {
            try
            {
                if (TxtStudentSearch == null || LstStudentResults == null) return;

                var searchText = TxtStudentSearch.Text?.Trim();
                
                if (string.IsNullOrEmpty(searchText) || searchText == "اكتب اسم الطالب أو رقم الهاتف...")
                {
                    LstStudentResults.Visibility = Visibility.Collapsed;
                    return;
                }

                var filteredStudents = _allStudents
                    .Where(s => s.Name.Contains(searchText, StringComparison.OrdinalIgnoreCase) ||
                               s.Phone.Contains(searchText, StringComparison.OrdinalIgnoreCase))
                    .Take(5)
                    .ToList();

                LstStudentResults.Items.Clear();
                
                if (filteredStudents.Any())
                {
                    foreach (var student in filteredStudents)
                    {
                        var item = new ListBoxItem
                        {
                            Content = $"{student.Name} - {student.Group?.Name} - {student.Phone}",
                            Tag = student
                        };
                        LstStudentResults.Items.Add(item);
                    }
                    LstStudentResults.Visibility = Visibility.Visible;
                }
                else
                {
                    LstStudentResults.Visibility = Visibility.Collapsed;
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في البحث: {ex.Message}", "خطأ", 
                              MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void BtnSearchStudent_Click(object sender, RoutedEventArgs e)
        {
            TxtStudentSearch_TextChanged(sender, null);
        }

        private void LstStudentResults_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            try
            {
                if (LstStudentResults.SelectedItem is ListBoxItem item && item.Tag is Student student)
                {
                    _selectedStudent = student;
                    ShowSelectedStudent(student);
                    LstStudentResults.Visibility = Visibility.Collapsed;
                    TxtStudentSearch.Text = student.Name;
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في اختيار الطالب: {ex.Message}", "خطأ", 
                              MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void ShowSelectedStudent(Student student)
        {
            try
            {
                if (SelectedStudentPanel == null || TxtSelectedStudent == null || 
                    TxtStudentInfo == null || TxtStudentDue == null) return;

                SelectedStudentPanel.Visibility = Visibility.Visible;
                TxtSelectedStudent.Text = student.Name;
                TxtStudentInfo.Text = $"المجموعة: {student.Group?.Name} | الهاتف: {student.Phone}";
                TxtStudentDue.Text = $"المبلغ المستحق: {student.TotalDue:C}";
                
                // Enable amount buttons
                if (BtnFullAmount != null) BtnFullAmount.IsEnabled = student.TotalDue > 0;
                if (BtnMonthlyFee != null) BtnMonthlyFee.IsEnabled = student.Group?.MonthlyFee > 0;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في عرض بيانات الطالب: {ex.Message}", "خطأ", 
                              MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void BtnFullAmount_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (_selectedStudent != null && TxtAmount != null)
                {
                    TxtAmount.Text = _selectedStudent.TotalDue.ToString("F2");
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تعيين المبلغ الكامل: {ex.Message}", "خطأ", 
                              MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void BtnMonthlyFee_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (_selectedStudent?.Group != null && TxtAmount != null)
                {
                    TxtAmount.Text = _selectedStudent.Group.MonthlyFee.ToString("F2");
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تعيين الاشتراك الشهري: {ex.Message}", "خطأ", 
                              MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async void BtnSave_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (!ValidateInput())
                    return;

                var payment = new Payment
                {
                    StudentId = _selectedStudent!.Id,
                    Type = PaymentType.Subscription,
                    Amount = decimal.Parse(TxtAmount!.Text),
                    PaymentDate = DateTime.Now,
                    PaymentMethod = ((ComboBoxItem)CmbPaymentMethod!.SelectedItem).Content.ToString()!,
                    Notes = TxtNotes?.Text?.Trim() ?? "",
                    CreatedAt = DateTime.Now
                };

                await _paymentService.AddPaymentAsync(payment);
                
                MessageBox.Show("تم تسجيل الدفعة بنجاح", "نجح", 
                              MessageBoxButton.OK, MessageBoxImage.Information);
                
                DialogResult = true;
                Close();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في حفظ الدفعة: {ex.Message}", "خطأ", 
                              MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void BtnCancel_Click(object sender, RoutedEventArgs e)
        {
            DialogResult = false;
            Close();
        }

        private bool ValidateInput()
        {
            try
            {
                if (_selectedStudent == null)
                {
                    MessageBox.Show("يرجى اختيار الطالب", "خطأ في البيانات", 
                                  MessageBoxButton.OK, MessageBoxImage.Warning);
                    TxtStudentSearch?.Focus();
                    return false;
                }

                if (TxtAmount == null || !decimal.TryParse(TxtAmount.Text, out var amount) || amount <= 0)
                {
                    MessageBox.Show("يرجى إدخال مبلغ صحيح أكبر من صفر", "خطأ في البيانات", 
                                  MessageBoxButton.OK, MessageBoxImage.Warning);
                    TxtAmount?.Focus();
                    return false;
                }

                if (CmbPaymentMethod?.SelectedItem == null)
                {
                    MessageBox.Show("يرجى اختيار طريقة الدفع", "خطأ في البيانات", 
                                  MessageBoxButton.OK, MessageBoxImage.Warning);
                    CmbPaymentMethod?.Focus();
                    return false;
                }

                return true;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في التحقق من البيانات: {ex.Message}", "خطأ", 
                              MessageBoxButton.OK, MessageBoxImage.Error);
                return false;
            }
        }
    }
}
