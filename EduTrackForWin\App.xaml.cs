﻿using System.Configuration;
using System.Data;
using System.Windows;
using EduTrackForWin.Data;
using EduTrackForWin.Services;

namespace EduTrackForWin;

/// <summary>
/// Interaction logic for App.xaml
/// </summary>
public partial class App : Application
{
    protected override async void OnStartup(StartupEventArgs e)
    {
        try
        {
            // Initialize database
            var databaseService = new DatabaseService();
            await databaseService.InitializeDatabaseAsync();

            base.OnStartup(e);
        }
        catch (Exception ex)
        {
            MessageBox.Show($"خطأ في تهيئة التطبيق: {ex.Message}", "خطأ",
                          MessageBoxButton.OK, MessageBoxImage.Error);
            Shutdown();
        }
    }
}

