@echo off
echo ========================================
echo    نظام إدارة المراكز التعليمية
echo    EduTrack for Windows
echo    أداة النشر والتجميع
echo ========================================
echo.

echo [1/5] تنظيف المشروع...
dotnet clean --configuration Release
if %errorlevel% neq 0 (
    echo خطأ في تنظيف المشروع!
    pause
    exit /b 1
)

echo [2/5] استعادة الحزم...
dotnet restore
if %errorlevel% neq 0 (
    echo خطأ في استعادة الحزم!
    pause
    exit /b 1
)

echo [3/5] بناء المشروع...
dotnet build --configuration Release --no-restore
if %errorlevel% neq 0 (
    echo خطأ في بناء المشروع!
    pause
    exit /b 1
)

echo [4/5] نشر التطبيق...
dotnet publish --configuration Release --output "./publish" --self-contained true --runtime win-x64
if %errorlevel% neq 0 (
    echo خطأ في نشر التطبيق!
    pause
    exit /b 1
)

echo [5/5] إنشاء ملفات التوثيق...
copy README.md "./publish/" >nul 2>&1
copy SETUP.md "./publish/" >nul 2>&1
copy USER_GUIDE.md "./publish/" >nul 2>&1

echo.
echo ========================================
echo تم النشر بنجاح!
echo مجلد النشر: ./publish
echo ========================================
echo.
echo يمكنك الآن:
echo 1. نسخ مجلد publish إلى أي جهاز Windows
echo 2. تشغيل EduTrackForWin.exe مباشرة
echo 3. لا يحتاج تثبيت .NET منفصل
echo.
pause
