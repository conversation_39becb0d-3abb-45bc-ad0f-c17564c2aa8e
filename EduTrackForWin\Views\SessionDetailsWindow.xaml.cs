using System.Windows;
using EduTrackForWin.Models;
using EduTrackForWin.Data;
using Microsoft.EntityFrameworkCore;

namespace EduTrackForWin.Views
{
    public partial class SessionDetailsWindow : Window
    {
        private readonly Session _session;
        private readonly EduTrackDbContext _context;

        public SessionDetailsWindow(Session session)
        {
            InitializeComponent();
            _session = session;
            _context = new EduTrackDbContext();
            LoadSessionDetails();
        }

        private async void LoadSessionDetails()
        {
            // Basic Information
            TxtSessionTitle.Text = $"حصة {_session.Group?.Name ?? "غير محدد"}";
            TxtSessionDate.Text = $"{_session.Date:yyyy/MM/dd} - {_session.SessionTime}";
            
            TxtDate.Text = _session.Date.ToString("yyyy/MM/dd");
            TxtStartTime.Text = _session.StartTime.ToString(@"hh\:mm");
            TxtEndTime.Text = _session.EndTime.ToString(@"hh\:mm");
            TxtDuration.Text = $"{_session.Duration.TotalMinutes} دقيقة";
            
            TxtGroup.Text = _session.Group?.Name ?? "غير محدد";
            TxtTeacher.Text = _session.Teacher?.Name ?? "غير محدد";
            TxtRoom.Text = string.IsNullOrEmpty(_session.Room) ? "غير محدد" : _session.Room;
            TxtStatus.Text = _session.IsCompleted ? "مكتملة" : "قيد الانتظار";
            
            // Set status color
            if (_session.IsCompleted)
            {
                TxtStatus.Foreground = System.Windows.Media.Brushes.Green;
            }
            else
            {
                TxtStatus.Foreground = System.Windows.Media.Brushes.Orange;
            }
            
            TxtTopic.Text = string.IsNullOrEmpty(_session.Topic) ? "لم يتم تحديد موضوع" : _session.Topic;
            TxtNotes.Text = string.IsNullOrEmpty(_session.Notes) ? "لا توجد ملاحظات" : _session.Notes;

            // Load attendance data
            await LoadAttendanceData();
        }

        private async Task LoadAttendanceData()
        {
            try
            {
                // Load session with attendance data
                var sessionWithAttendance = await _context.Sessions
                    .Include(s => s.Attendances)
                    .ThenInclude(a => a.Student)
                    .Include(s => s.Group)
                    .ThenInclude(g => g.Students)
                    .FirstOrDefaultAsync(s => s.Id == _session.Id);

                if (sessionWithAttendance != null)
                {
                    var totalStudents = sessionWithAttendance.Group?.Students?.Count ?? 0;
                    var presentStudents = sessionWithAttendance.Attendances?.Count(a => a.IsPresent) ?? 0;
                    var attendanceRate = totalStudents > 0 ? (double)presentStudents / totalStudents * 100 : 0;

                    TxtTotalStudents.Text = totalStudents.ToString();
                    TxtPresentStudents.Text = presentStudents.ToString();
                    TxtAttendanceRate.Text = $"{attendanceRate:F1}%";

                    // Load attendance list
                    if (sessionWithAttendance.Attendances != null && sessionWithAttendance.Attendances.Any())
                    {
                        DgAttendance.ItemsSource = sessionWithAttendance.Attendances.ToList();
                    }
                    else
                    {
                        // If no attendance recorded yet, show all students as not recorded
                        var students = sessionWithAttendance.Group?.Students?.ToList() ?? new List<Student>();
                        var attendanceList = students.Select(s => new
                        {
                            StudentName = s.Name,
                            IsPresent = false,
                            Status = "لم يتم التسجيل",
                            Notes = ""
                        }).ToList();
                        
                        DgAttendance.ItemsSource = attendanceList;
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل بيانات الحضور: {ex.Message}", "خطأ", 
                              MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void BtnEdit_Click(object sender, RoutedEventArgs e)
        {
            var editWindow = new AddEditSessionWindow(_session);
            if (editWindow.ShowDialog() == true)
            {
                // Refresh the data
                LoadSessionDetails();
            }
        }

        private async void BtnTakeAttendance_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // Load session with group and students
                var sessionWithGroup = await _context.Sessions
                    .Include(s => s.Group)
                    .ThenInclude(g => g.Students)
                    .FirstOrDefaultAsync(s => s.Id == _session.Id);

                if (sessionWithGroup?.Group?.Students == null || !sessionWithGroup.Group.Students.Any())
                {
                    MessageBox.Show("لا توجد طلاب مسجلين في هذه المجموعة", "تنبيه", 
                                  MessageBoxButton.OK, MessageBoxImage.Information);
                    return;
                }

                var attendanceWindow = new TakeAttendanceWindow(sessionWithGroup);
                if (attendanceWindow.ShowDialog() == true)
                {
                    // Refresh attendance data
                    await LoadAttendanceData();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في فتح نافذة تسجيل الحضور: {ex.Message}", "خطأ", 
                              MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async void BtnMarkComplete_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var session = await _context.Sessions.FindAsync(_session.Id);
                if (session != null)
                {
                    session.IsCompleted = !session.IsCompleted;
                    session.UpdatedAt = DateTime.Now;
                    
                    await _context.SaveChangesAsync();
                    
                    var status = session.IsCompleted ? "مكتملة" : "قيد الانتظار";
                    MessageBox.Show($"تم تغيير حالة الحصة إلى: {status}", "نجح", 
                                  MessageBoxButton.OK, MessageBoxImage.Information);
                    
                    // Update the session object and refresh display
                    _session.IsCompleted = session.IsCompleted;
                    LoadSessionDetails();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحديث حالة الحصة: {ex.Message}", "خطأ", 
                              MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void BtnClose_Click(object sender, RoutedEventArgs e)
        {
            Close();
        }

        protected override void OnClosed(EventArgs e)
        {
            _context?.Dispose();
            base.OnClosed(e);
        }
    }
}
