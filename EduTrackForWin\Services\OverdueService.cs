using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using EduTrackForWin.Data;
using EduTrackForWin.Models;
using Microsoft.EntityFrameworkCore;

namespace EduTrackForWin.Services
{
    public class OverdueService
    {
        private readonly EduTrackDbContext _context;

        public OverdueService()
        {
            _context = new EduTrackDbContext();
        }

        // Get all overdue students
        public async Task<List<OverdueStudent>> GetOverdueStudentsAsync()
        {
            var students = await _context.Students
                .Include(s => s.Group)
                .Include(s => s.Payments)
                .ToListAsync();

            var overdueStudents = new List<OverdueStudent>();

            foreach (var student in students)
            {
                var overdueInfo = CalculateOverdueInfo(student);
                if (overdueInfo.IsOverdue)
                {
                    overdueStudents.Add(overdueInfo);
                }
            }

            return overdueStudents.OrderByDescending(o => o.OverdueDays).ToList();
        }

        // Calculate overdue information for a student
        public OverdueStudent CalculateOverdueInfo(Student student)
        {
            var overdueInfo = new OverdueStudent
            {
                Student = student,
                StudentId = student.Id,
                StudentName = student.Name,
                GroupName = student.Group?.Name ?? "غير محدد",
                Phone = student.Phone,
                TotalDue = student.TotalDue,
                IsOverdue = false,
                OverdueDays = 0,
                LastPaymentDate = null,
                NextDueDate = null,
                LateFee = 0
            };

            // Get last payment date
            var lastPayment = student.Payments?.OrderByDescending(p => p.PaymentDate).FirstOrDefault();
            overdueInfo.LastPaymentDate = lastPayment?.PaymentDate;

            // Calculate next due date based on group's billing cycle
            if (student.Group != null)
            {
                try
                {
                    var billingDay = student.Group.BillingDay ?? 1; // Default to 1st of month
                    var today = DateTime.Today;

                    // Calculate next due date
                    var nextDueDate = new DateTime(today.Year, today.Month, billingDay);
                    if (nextDueDate <= today)
                    {
                        nextDueDate = nextDueDate.AddMonths(1);
                    }
                    overdueInfo.NextDueDate = nextDueDate;

                    // Check if student is overdue
                    var currentMonthDue = new DateTime(today.Year, today.Month, billingDay);
                    if (today > currentMonthDue && student.TotalDue > 0)
                    {
                        overdueInfo.IsOverdue = true;
                        overdueInfo.OverdueDays = (today - currentMonthDue).Days;

                        // Calculate late fee (e.g., 5% after 7 days, 10% after 15 days)
                        if (overdueInfo.OverdueDays > 15)
                        {
                            overdueInfo.LateFee = student.TotalDue * 0.10m; // 10%
                        }
                        else if (overdueInfo.OverdueDays > 7)
                        {
                            overdueInfo.LateFee = student.TotalDue * 0.05m; // 5%
                        }
                    }
                }
                catch
                {
                    // If there's an error with billing day calculation, use simple overdue check
                    if (student.TotalDue > 0)
                    {
                        overdueInfo.IsOverdue = true;
                        overdueInfo.OverdueDays = 1; // Default to 1 day overdue
                    }
                }
            }

            return overdueInfo;
        }

        // Get overdue students by severity
        public async Task<OverdueSummary> GetOverdueSummaryAsync()
        {
            var overdueStudents = await GetOverdueStudentsAsync();
            
            var summary = new OverdueSummary
            {
                TotalOverdueStudents = overdueStudents.Count,
                TotalOverdueAmount = overdueStudents.Sum(o => o.TotalDue),
                TotalLateFees = overdueStudents.Sum(o => o.LateFee),
                
                // Categorize by severity
                CriticalOverdue = overdueStudents.Where(o => o.OverdueDays > 30).ToList(),
                HighOverdue = overdueStudents.Where(o => o.OverdueDays > 15 && o.OverdueDays <= 30).ToList(),
                MediumOverdue = overdueStudents.Where(o => o.OverdueDays > 7 && o.OverdueDays <= 15).ToList(),
                LowOverdue = overdueStudents.Where(o => o.OverdueDays <= 7).ToList()
            };

            return summary;
        }

        // Get students due this month
        public async Task<List<Student>> GetStudentsDueThisMonthAsync()
        {
            var today = DateTime.Today;
            var students = await _context.Students
                .Include(s => s.Group)
                .Include(s => s.Payments)
                .Where(s => s.Group != null)
                .ToListAsync();

            var dueStudents = new List<Student>();

            foreach (var student in students)
            {
                var billingDay = student.Group.BillingDay ?? 1;
                var thisMonthDue = new DateTime(today.Year, today.Month, billingDay);
                
                // Check if student hasn't paid for this month
                var thisMonthPayments = student.Payments?
                    .Where(p => p.PaymentDate.Year == today.Year && 
                               p.PaymentDate.Month == today.Month &&
                               p.Type == PaymentType.Subscription)
                    .Sum(p => p.Amount) ?? 0;

                if (thisMonthPayments < student.Group.MonthlyFee)
                {
                    dueStudents.Add(student);
                }
            }

            return dueStudents;
        }

        // Generate overdue report
        public async Task<string> GenerateOverdueReportAsync()
        {
            var summary = await GetOverdueSummaryAsync();
            
            var report = "تقرير المتأخرات المالية\n";
            report += "===================\n\n";
            report += $"تاريخ التقرير: {DateTime.Now:yyyy/MM/dd HH:mm}\n\n";
            
            report += "ملخص المتأخرات:\n";
            report += "---------------\n";
            report += $"إجمالي الطلاب المتأخرين: {summary.TotalOverdueStudents}\n";
            report += $"إجمالي المبالغ المتأخرة: {summary.TotalOverdueAmount:C}\n";
            report += $"إجمالي غرامات التأخير: {summary.TotalLateFees:C}\n\n";
            
            report += "تصنيف المتأخرات:\n";
            report += "----------------\n";
            report += $"متأخرات حرجة (أكثر من 30 يوم): {summary.CriticalOverdue.Count}\n";
            report += $"متأخرات عالية (15-30 يوم): {summary.HighOverdue.Count}\n";
            report += $"متأخرات متوسطة (7-15 يوم): {summary.MediumOverdue.Count}\n";
            report += $"متأخرات منخفضة (أقل من 7 أيام): {summary.LowOverdue.Count}\n\n";
            
            // Add detailed list
            if (summary.CriticalOverdue.Any())
            {
                report += "المتأخرات الحرجة:\n";
                report += "----------------\n";
                foreach (var student in summary.CriticalOverdue.Take(10))
                {
                    report += $"• {student.StudentName} - {student.TotalDue:C} - {student.OverdueDays} يوم\n";
                }
                if (summary.CriticalOverdue.Count > 10)
                {
                    report += $"... و {summary.CriticalOverdue.Count - 10} طلاب آخرين\n";
                }
                report += "\n";
            }

            return report;
        }

        // Apply late fees automatically
        public async Task<int> ApplyLateFees()
        {
            var overdueStudents = await GetOverdueStudentsAsync();
            var feesApplied = 0;

            foreach (var overdueStudent in overdueStudents.Where(o => o.LateFee > 0))
            {
                try
                {
                    // Check if late fee already applied this month
                    var today = DateTime.Today;
                    var existingLateFee = await _context.Payments
                        .AnyAsync(p => p.StudentId == overdueStudent.StudentId &&
                                      p.Type == PaymentType.Fine &&
                                      p.PaymentDate.Year == today.Year &&
                                      p.PaymentDate.Month == today.Month);

                    if (!existingLateFee)
                    {
                        var lateFeePayment = new Payment
                        {
                            StudentId = overdueStudent.StudentId,
                            Type = PaymentType.Fine,
                            Amount = -overdueStudent.LateFee, // Negative amount for fee
                            PaymentDate = today,
                            PaymentMethod = "تلقائي",
                            Notes = $"غرامة تأخير - {overdueStudent.OverdueDays} يوم",
                            CreatedAt = DateTime.Now
                        };

                        _context.Payments.Add(lateFeePayment);
                        feesApplied++;
                    }
                }
                catch (Exception ex)
                {
                    // Log error but continue with other students
                    System.Diagnostics.Debug.WriteLine($"Error applying late fee for student {overdueStudent.StudentName}: {ex.Message}");
                }
            }

            if (feesApplied > 0)
            {
                await _context.SaveChangesAsync();
            }

            return feesApplied;
        }

        public void Dispose()
        {
            _context?.Dispose();
        }
    }

    // Helper classes
    public class OverdueStudent
    {
        public Student Student { get; set; }
        public int StudentId { get; set; }
        public string StudentName { get; set; }
        public string GroupName { get; set; }
        public string Phone { get; set; }
        public decimal TotalDue { get; set; }
        public bool IsOverdue { get; set; }
        public int OverdueDays { get; set; }
        public DateTime? LastPaymentDate { get; set; }
        public DateTime? NextDueDate { get; set; }
        public decimal LateFee { get; set; }
        
        public string SeverityLevel
        {
            get
            {
                if (OverdueDays > 30) return "حرج";
                if (OverdueDays > 15) return "عالي";
                if (OverdueDays > 7) return "متوسط";
                return "منخفض";
            }
        }
    }

    public class OverdueSummary
    {
        public int TotalOverdueStudents { get; set; }
        public decimal TotalOverdueAmount { get; set; }
        public decimal TotalLateFees { get; set; }
        public List<OverdueStudent> CriticalOverdue { get; set; } = new();
        public List<OverdueStudent> HighOverdue { get; set; } = new();
        public List<OverdueStudent> MediumOverdue { get; set; } = new();
        public List<OverdueStudent> LowOverdue { get; set; } = new();
    }
}
