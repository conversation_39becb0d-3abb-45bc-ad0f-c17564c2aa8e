using System.Collections.ObjectModel;
using System.Windows;
using System.Windows.Controls;
using EduTrackForWin.Models;
using EduTrackForWin.Data;
using EduTrackForWin.Services;
using Microsoft.EntityFrameworkCore;

namespace EduTrackForWin.Views
{
    public partial class SchedulesPage : Page
    {
        private readonly EduTrackDbContext _context;
        private readonly ScheduleService _scheduleService;
        private ObservableCollection<Schedule> _schedules;
        private List<Schedule> _allSchedules;

        public SchedulesPage()
        {
            try
            {
                InitializeComponent();
                _context = new EduTrackDbContext();
                _scheduleService = new ScheduleService();
                _schedules = new ObservableCollection<Schedule>();
                _allSchedules = new List<Schedule>();

                // إضافة حدث Loaded
                Loaded += SchedulesPage_Loaded;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تهيئة صفحة الجداول الزمنية: {ex.Message}\n\nتفاصيل الخطأ: {ex.StackTrace}",
                              "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
                throw;
            }
        }

        private void SchedulesPage_Loaded(object sender, RoutedEventArgs e)
        {
            try
            {
                if (DgSchedules != null) DgSchedules.ItemsSource = _schedules;

                LoadData();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل صفحة الجداول الزمنية: {ex.Message}", "خطأ",
                              MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async void LoadData()
        {
            await LoadGroups();
            await LoadSchedules();
        }

        private async Task LoadGroups()
        {
            try
            {
                var groups = await _context.Groups
                    .Include(g => g.Teacher)
                    .Where(g => g.IsActive)
                    .OrderBy(g => g.Name)
                    .ToListAsync();

                if (CmbGroupFilter != null)
                {
                    CmbGroupFilter.Items.Clear();
                    CmbGroupFilter.Items.Add(new ComboBoxItem { Content = "جميع المجموعات", Tag = 0 });

                    foreach (var group in groups)
                    {
                        CmbGroupFilter.Items.Add(new ComboBoxItem { Content = group.Name, Tag = group.Id });
                    }

                    CmbGroupFilter.SelectedIndex = 0;
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل المجموعات: {ex.Message}", "خطأ",
                              MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async Task LoadSchedules()
        {
            try
            {
                _allSchedules = await _scheduleService.GetAllSchedulesAsync();
                RefreshSchedulesList();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل الجداول الزمنية: {ex.Message}", "خطأ",
                              MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void RefreshSchedulesList()
        {
            _schedules.Clear();

            var filteredSchedules = _allSchedules.AsEnumerable();

            // تطبيق فلتر المجموعة
            if (CmbGroupFilter.SelectedItem is ComboBoxItem selectedGroup &&
                selectedGroup.Tag is int groupId && groupId > 0)
            {
                filteredSchedules = filteredSchedules.Where(s => s.GroupId == groupId);
            }

            // تطبيق فلتر الحالة
            var today = DateTime.Today;
            if (RbActiveSchedules.IsChecked == true)
            {
                filteredSchedules = filteredSchedules.Where(s => s.EndDate >= today);
            }
            else if (RbExpiredSchedules.IsChecked == true)
            {
                filteredSchedules = filteredSchedules.Where(s => s.EndDate < today);
            }

            foreach (var schedule in filteredSchedules)
            {
                _schedules.Add(schedule);
            }
        }

        private void CmbGroupFilter_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            if (IsLoaded)
                RefreshSchedulesList();
        }

        private void FilterStatus_Changed(object sender, RoutedEventArgs e)
        {
            if (IsLoaded)
                RefreshSchedulesList();
        }

        private void BtnRefresh_Click(object sender, RoutedEventArgs e)
        {
            LoadData();
        }

        private void BtnAddSchedule_Click(object sender, RoutedEventArgs e)
        {
            var addWindow = new AddEditScheduleWindow();
            if (addWindow.ShowDialog() == true)
            {
                LoadSchedules();
            }
        }

        private void BtnViewSchedule_Click(object sender, RoutedEventArgs e)
        {
            if (sender is Button button && button.Tag is int scheduleId)
            {
                var schedule = _allSchedules.FirstOrDefault(s => s.Id == scheduleId);
                if (schedule != null)
                {
                    var viewWindow = new ScheduleDetailsWindow(schedule);
                    viewWindow.ShowDialog();
                }
            }
        }

        private void BtnEditSchedule_Click(object sender, RoutedEventArgs e)
        {
            if (sender is Button button && button.Tag is int scheduleId)
            {
                var schedule = _allSchedules.FirstOrDefault(s => s.Id == scheduleId);
                if (schedule != null)
                {
                    var editWindow = new AddEditScheduleWindow(schedule);
                    if (editWindow.ShowDialog() == true)
                    {
                        LoadSchedules();
                    }
                }
            }
        }

        private async void BtnRegenerateSchedule_Click(object sender, RoutedEventArgs e)
        {
            if (sender is Button button && button.Tag is int scheduleId)
            {
                var schedule = _allSchedules.FirstOrDefault(s => s.Id == scheduleId);
                if (schedule != null)
                {
                    var result = MessageBox.Show(
                        "هل أنت متأكد من تحديث الحصص لهذا الجدول الزمني؟\n" +
                        "سيتم حذف جميع الحصص المستقبلية وإعادة إنشائها.",
                        "تأكيد التحديث",
                        MessageBoxButton.YesNo,
                        MessageBoxImage.Warning);

                    if (result == MessageBoxResult.Yes)
                    {
                        try
                        {
                            var success = await _scheduleService.RegenerateSessionsAsync(scheduleId);
                            if (success)
                            {
                                MessageBox.Show("تم تحديث الحصص بنجاح", "نجح",
                                              MessageBoxButton.OK, MessageBoxImage.Information);
                            }
                        }
                        catch (Exception ex)
                        {
                            MessageBox.Show($"خطأ في تحديث الحصص: {ex.Message}", "خطأ",
                                          MessageBoxButton.OK, MessageBoxImage.Error);
                        }
                    }
                }
            }
        }

        private async void BtnDeleteSchedule_Click(object sender, RoutedEventArgs e)
        {
            if (sender is Button button && button.Tag is int scheduleId)
            {
                var schedule = _allSchedules.FirstOrDefault(s => s.Id == scheduleId);
                if (schedule != null)
                {
                    var result = MessageBox.Show(
                        $"هل أنت متأكد من حذف الجدول الزمني؟\n" +
                        $"الاسم: {schedule.Name}\n" +
                        $"المجموعة: {schedule.Group?.Name}\n" +
                        $"الفترة: {schedule.StartDate:yyyy/MM/dd} - {schedule.EndDate:yyyy/MM/dd}\n\n" +
                        "سيتم حذف جميع الحصص المستقبلية المرتبطة بهذا الجدول.",
                        "تأكيد الحذف",
                        MessageBoxButton.YesNo,
                        MessageBoxImage.Warning);

                    if (result == MessageBoxResult.Yes)
                    {
                        try
                        {
                            var success = await _scheduleService.DeleteScheduleAsync(scheduleId);
                            if (success)
                            {
                                LoadSchedules();
                                MessageBox.Show("تم حذف الجدول الزمني بنجاح", "نجح",
                                              MessageBoxButton.OK, MessageBoxImage.Information);
                            }
                        }
                        catch (Exception ex)
                        {
                            MessageBox.Show($"خطأ في حذف الجدول الزمني: {ex.Message}", "خطأ",
                                          MessageBoxButton.OK, MessageBoxImage.Error);
                        }
                    }
                }
            }
        }

        private void Page_Unloaded(object sender, RoutedEventArgs e)
        {
            _context?.Dispose();
            _scheduleService?.Dispose();
        }
    }
}