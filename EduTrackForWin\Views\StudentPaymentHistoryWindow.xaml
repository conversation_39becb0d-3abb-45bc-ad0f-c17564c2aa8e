<Window x:Class="EduTrackForWin.Views.StudentPaymentHistoryWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        mc:Ignorable="d"
        Title="سجل مدفوعات الطالب" Height="600" Width="800"
        WindowStartupLocation="CenterScreen">

    <Window.Resources>
        <!-- Styles -->
        <Style x:Key="HeaderStyle" TargetType="TextBlock">
            <Setter Property="FontSize" Value="20"/>
            <Setter Property="FontWeight" Value="Bold"/>
            <Setter Property="Foreground" Value="#2E7D32"/>
            <Setter Property="Margin" Value="0,0,0,15"/>
        </Style>

        <Style x:Key="InfoStyle" TargetType="TextBlock">
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="Foreground" Value="#424242"/>
            <Setter Property="Margin" Value="0,0,0,5"/>
        </Style>

        <Style x:Key="StatCardStyle" TargetType="Border">
            <Setter Property="Background" Value="White"/>
            <Setter Property="CornerRadius" Value="8"/>
            <Setter Property="Padding" Value="15"/>
            <Setter Property="Margin" Value="5"/>
            <Setter Property="Effect">
                <Setter.Value>
                    <DropShadowEffect Color="Gray" Direction="270" ShadowDepth="2" Opacity="0.2" BlurRadius="8"/>
                </Setter.Value>
            </Setter>
        </Style>

        <Style x:Key="PrimaryButtonStyle" TargetType="Button">
            <Setter Property="Background" Value="#4CAF50"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Padding" Value="15,8"/>
            <Setter Property="Margin" Value="5"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}" 
                              CornerRadius="5">
                            <ContentPresenter HorizontalAlignment="Center" 
                                            VerticalAlignment="Center"
                                            Margin="{TemplateBinding Padding}"/>
                        </Border>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>
    </Window.Resources>

    <Grid Background="#F5F5F5">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <Border Grid.Row="0" Background="White" Padding="20" Margin="20,20,20,10">
            <Border.Effect>
                <DropShadowEffect Color="Gray" Direction="270" ShadowDepth="1" Opacity="0.2" BlurRadius="5"/>
            </Border.Effect>
            
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <StackPanel Grid.Column="0">
                    <TextBlock Name="TxtStudentName" Text="سجل مدفوعات الطالب" Style="{StaticResource HeaderStyle}"/>
                    <TextBlock Name="TxtStudentInfo" Style="{StaticResource InfoStyle}"/>
                </StackPanel>

                <StackPanel Grid.Column="1" Orientation="Horizontal">
                    <Button Name="BtnAddPayment" Content="إضافة دفعة" 
                          Style="{StaticResource PrimaryButtonStyle}" Click="BtnAddPayment_Click"/>
                    <Button Name="BtnPrintReport" Content="طباعة التقرير" 
                          Background="#2196F3" Foreground="White" BorderThickness="0" 
                          Padding="15,8" Margin="5" Cursor="Hand"
                          Click="BtnPrintReport_Click"/>
                </StackPanel>
            </Grid>
        </Border>

        <!-- Statistics -->
        <Border Grid.Row="1" Background="White" Padding="20" Margin="20,10,20,10">
            <Border.Effect>
                <DropShadowEffect Color="Gray" Direction="270" ShadowDepth="1" Opacity="0.2" BlurRadius="5"/>
            </Border.Effect>
            
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>

                <!-- Total Paid -->
                <Border Grid.Column="0" Style="{StaticResource StatCardStyle}" Background="#E8F5E8">
                    <StackPanel>
                        <TextBlock Text="💰" FontSize="24" HorizontalAlignment="Center" Margin="0,0,0,5"/>
                        <TextBlock Text="إجمالي المدفوع" FontWeight="Bold" HorizontalAlignment="Center" 
                                 Foreground="#2E7D32" FontSize="12"/>
                        <TextBlock Name="TxtTotalPaid" Text="0 ج.م" FontWeight="Bold" 
                                 HorizontalAlignment="Center" Foreground="#1B5E20" FontSize="16" Margin="0,5,0,0"/>
                    </StackPanel>
                </Border>

                <!-- Total Due -->
                <Border Grid.Column="1" Style="{StaticResource StatCardStyle}" Background="#FFF3E0">
                    <StackPanel>
                        <TextBlock Text="⚠️" FontSize="24" HorizontalAlignment="Center" Margin="0,0,0,5"/>
                        <TextBlock Text="المبلغ المستحق" FontWeight="Bold" HorizontalAlignment="Center" 
                                 Foreground="#F57C00" FontSize="12"/>
                        <TextBlock Name="TxtTotalDue" Text="0 ج.م" FontWeight="Bold" 
                                 HorizontalAlignment="Center" Foreground="#E65100" FontSize="16" Margin="0,5,0,0"/>
                    </StackPanel>
                </Border>

                <!-- Payment Count -->
                <Border Grid.Column="2" Style="{StaticResource StatCardStyle}" Background="#E3F2FD">
                    <StackPanel>
                        <TextBlock Text="📊" FontSize="24" HorizontalAlignment="Center" Margin="0,0,0,5"/>
                        <TextBlock Text="عدد المدفوعات" FontWeight="Bold" HorizontalAlignment="Center" 
                                 Foreground="#1976D2" FontSize="12"/>
                        <TextBlock Name="TxtPaymentCount" Text="0" FontWeight="Bold" 
                                 HorizontalAlignment="Center" Foreground="#0D47A1" FontSize="16" Margin="0,5,0,0"/>
                    </StackPanel>
                </Border>

                <!-- Last Payment -->
                <Border Grid.Column="3" Style="{StaticResource StatCardStyle}" Background="#F3E5F5">
                    <StackPanel>
                        <TextBlock Text="📅" FontSize="24" HorizontalAlignment="Center" Margin="0,0,0,5"/>
                        <TextBlock Text="آخر دفعة" FontWeight="Bold" HorizontalAlignment="Center" 
                                 Foreground="#7B1FA2" FontSize="12"/>
                        <TextBlock Name="TxtLastPayment" Text="لا يوجد" FontWeight="Bold" 
                                 HorizontalAlignment="Center" Foreground="#4A148C" FontSize="16" Margin="0,5,0,0"/>
                    </StackPanel>
                </Border>
            </Grid>
        </Border>

        <!-- Payments DataGrid -->
        <Border Grid.Row="2" Background="White" Padding="20" Margin="20,10,20,10">
            <Border.Effect>
                <DropShadowEffect Color="Gray" Direction="270" ShadowDepth="1" Opacity="0.2" BlurRadius="5"/>
            </Border.Effect>
            
            <DataGrid Name="DgPayments" 
                    AutoGenerateColumns="False" 
                    CanUserAddRows="False"
                    CanUserDeleteRows="False"
                    IsReadOnly="True"
                    GridLinesVisibility="Horizontal"
                    HeadersVisibility="Column"
                    AlternatingRowBackground="#F9F9F9"
                    RowBackground="White"
                    BorderBrush="#E0E0E0"
                    BorderThickness="1">
                
                <DataGrid.Columns>
                    <!-- Payment Date -->
                    <DataGridTextColumn Header="تاريخ الدفع" Width="120">
                        <DataGridTextColumn.Binding>
                            <Binding Path="PaymentDate" StringFormat="{}{0:yyyy/MM/dd}"/>
                        </DataGridTextColumn.Binding>
                    </DataGridTextColumn>
                    
                    <!-- Payment Type -->
                    <DataGridTextColumn Header="نوع الدفعة" Binding="{Binding TypeName}" Width="120"/>
                    
                    <!-- Amount -->
                    <DataGridTextColumn Header="المبلغ" Width="100">
                        <DataGridTextColumn.Binding>
                            <Binding Path="Amount" StringFormat="{}{0:C}"/>
                        </DataGridTextColumn.Binding>
                    </DataGridTextColumn>
                    
                    <!-- Payment Method -->
                    <DataGridTextColumn Header="طريقة الدفع" Binding="{Binding PaymentMethod}" Width="120"/>
                    
                    <!-- Notes -->
                    <DataGridTextColumn Header="ملاحظات" Binding="{Binding Notes}" Width="*"/>
                    
                    <!-- Actions -->
                    <DataGridTemplateColumn Header="الإجراءات" Width="120">
                        <DataGridTemplateColumn.CellTemplate>
                            <DataTemplate>
                                <StackPanel Orientation="Horizontal">
                                    <Button Content="تعديل" 
                                          Background="#FF9800" 
                                          Foreground="White" 
                                          BorderThickness="0" 
                                          Padding="8,4" 
                                          Margin="2"
                                          Cursor="Hand"
                                          Click="BtnEditPayment_Click"/>
                                    <Button Content="حذف" 
                                          Background="#F44336" 
                                          Foreground="White" 
                                          BorderThickness="0" 
                                          Padding="8,4" 
                                          Margin="2"
                                          Cursor="Hand"
                                          Click="BtnDeletePayment_Click"/>
                                </StackPanel>
                            </DataTemplate>
                        </DataGridTemplateColumn.CellTemplate>
                    </DataGridTemplateColumn>
                </DataGrid.Columns>
            </DataGrid>
        </Border>

        <!-- Footer -->
        <Border Grid.Row="3" Background="White" Padding="20" Margin="20,10,20,20">
            <Border.Effect>
                <DropShadowEffect Color="Gray" Direction="270" ShadowDepth="1" Opacity="0.2" BlurRadius="5"/>
            </Border.Effect>
            
            <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                <Button Name="BtnClose" Content="إغلاق" 
                      Background="#757575" Foreground="White" BorderThickness="0" 
                      Padding="20,10" Margin="10" FontSize="14" FontWeight="SemiBold" Cursor="Hand"
                      Click="BtnClose_Click"/>
            </StackPanel>
        </Border>
    </Grid>
</Window>
