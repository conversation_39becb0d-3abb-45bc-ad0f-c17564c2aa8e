using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using EduTrackForWin.Data;
using EduTrackForWin.Models;
using EduTrackForWin.Services;
using Microsoft.EntityFrameworkCore;

namespace EduTrackForWin.Views
{
    /// <summary>
    /// Interaction logic for StudentPaymentHistoryWindow.xaml
    /// </summary>
    public partial class StudentPaymentHistoryWindow : Window
    {
        private readonly EduTrackDbContext _context;
        private readonly PaymentService _paymentService;
        private readonly Student _student;
        private List<Payment> _payments;

        public StudentPaymentHistoryWindow(Student student)
        {
            try
            {
                InitializeComponent();
                _context = new EduTrackDbContext();
                _paymentService = new PaymentService();
                _student = student ?? throw new ArgumentNullException(nameof(student));
                _payments = new List<Payment>();

                InitializeWindow();
                Loaded += StudentPaymentHistoryWindow_Loaded;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تهيئة نافذة سجل المدفوعات: {ex.Message}", "خطأ", 
                              MessageBoxButton.OK, MessageBoxImage.Error);
                throw;
            }
        }

        private void InitializeWindow()
        {
            try
            {
                Title = $"سجل مدفوعات الطالب - {_student.Name}";
                
                if (TxtStudentName != null) 
                    TxtStudentName.Text = $"📋 سجل مدفوعات الطالب: {_student.Name}";
                
                if (TxtStudentInfo != null)
                    TxtStudentInfo.Text = $"المجموعة: {_student.Group?.Name ?? "غير محدد"} | الهاتف: {_student.Phone}";
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تهيئة النافذة: {ex.Message}", "خطأ", 
                              MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async void StudentPaymentHistoryWindow_Loaded(object sender, RoutedEventArgs e)
        {
            await LoadData();
        }

        private async Task LoadData()
        {
            try
            {
                await LoadPayments();
                LoadStatistics();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل البيانات: {ex.Message}", "خطأ", 
                              MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async Task LoadPayments()
        {
            try
            {
                _payments = await _paymentService.GetPaymentsByStudentAsync(_student.Id);
                
                if (DgPayments != null)
                {
                    DgPayments.ItemsSource = _payments;
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل المدفوعات: {ex.Message}", "خطأ", 
                              MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void LoadStatistics()
        {
            try
            {
                // Total Paid
                var totalPaid = _payments.Sum(p => p.Amount);
                if (TxtTotalPaid != null) TxtTotalPaid.Text = $"{totalPaid:C}";

                // Total Due
                if (TxtTotalDue != null) TxtTotalDue.Text = $"{_student.TotalDue:C}";

                // Payment Count
                if (TxtPaymentCount != null) TxtPaymentCount.Text = _payments.Count.ToString();

                // Last Payment
                var lastPayment = _payments.OrderByDescending(p => p.PaymentDate).FirstOrDefault();
                if (TxtLastPayment != null)
                {
                    TxtLastPayment.Text = lastPayment != null 
                        ? lastPayment.PaymentDate.ToString("yyyy/MM/dd")
                        : "لا يوجد";
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل الإحصائيات: {ex.Message}", "خطأ", 
                              MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async void BtnAddPayment_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var paymentWindow = new AddEditPaymentWindow();
                
                // Pre-select the student
                paymentWindow.Loaded += (s, args) =>
                {
                    if (paymentWindow.CmbStudent != null)
                    {
                        paymentWindow.CmbStudent.SelectedValue = _student.Id;
                        paymentWindow.CmbStudent.IsEnabled = false; // Lock the student selection
                    }
                };

                if (paymentWindow.ShowDialog() == true)
                {
                    await LoadData();
                    MessageBox.Show("تم إضافة الدفعة بنجاح", "نجح", 
                                  MessageBoxButton.OK, MessageBoxImage.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في إضافة الدفعة: {ex.Message}", "خطأ", 
                              MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async void BtnEditPayment_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (DgPayments.SelectedItem is Payment selectedPayment)
                {
                    var editWindow = new AddEditPaymentWindow(selectedPayment);
                    if (editWindow.ShowDialog() == true)
                    {
                        await LoadData();
                        MessageBox.Show("تم تحديث الدفعة بنجاح", "نجح", 
                                      MessageBoxButton.OK, MessageBoxImage.Information);
                    }
                }
                else
                {
                    MessageBox.Show("يرجى اختيار دفعة لتعديلها", "تنبيه", 
                                  MessageBoxButton.OK, MessageBoxImage.Warning);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تعديل الدفعة: {ex.Message}", "خطأ", 
                              MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async void BtnDeletePayment_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (DgPayments.SelectedItem is Payment selectedPayment)
                {
                    var result = MessageBox.Show(
                        $"هل أنت متأكد من حذف دفعة بمبلغ {selectedPayment.Amount:C} بتاريخ {selectedPayment.PaymentDate:yyyy/MM/dd}؟\n\nهذا الإجراء لا يمكن التراجع عنه.",
                        "تأكيد الحذف", 
                        MessageBoxButton.YesNo, 
                        MessageBoxImage.Warning);

                    if (result == MessageBoxResult.Yes)
                    {
                        await _paymentService.DeletePaymentAsync(selectedPayment.Id);
                        await LoadData();
                        
                        MessageBox.Show("تم حذف الدفعة بنجاح", "تم الحذف", 
                                      MessageBoxButton.OK, MessageBoxImage.Information);
                    }
                }
                else
                {
                    MessageBox.Show("يرجى اختيار دفعة لحذفها", "تنبيه", 
                                  MessageBoxButton.OK, MessageBoxImage.Warning);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في حذف الدفعة: {ex.Message}", "خطأ", 
                              MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void BtnPrintReport_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var report = GeneratePaymentReport();
                MessageBox.Show(report, $"تقرير مدفوعات الطالب - {_student.Name}", 
                              MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في إنشاء التقرير: {ex.Message}", "خطأ", 
                              MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private string GeneratePaymentReport()
        {
            var report = $"تقرير مدفوعات الطالب\n";
            report += $"========================\n\n";
            report += $"اسم الطالب: {_student.Name}\n";
            report += $"المجموعة: {_student.Group?.Name ?? "غير محدد"}\n";
            report += $"الهاتف: {_student.Phone}\n";
            report += $"تاريخ التقرير: {DateTime.Now:yyyy/MM/dd HH:mm}\n\n";
            
            report += $"الإحصائيات:\n";
            report += $"----------\n";
            report += $"إجمالي المدفوع: {_payments.Sum(p => p.Amount):C}\n";
            report += $"المبلغ المستحق: {_student.TotalDue:C}\n";
            report += $"عدد المدفوعات: {_payments.Count}\n\n";
            
            if (_payments.Any())
            {
                report += $"تفاصيل المدفوعات:\n";
                report += $"----------------\n";
                foreach (var payment in _payments.OrderByDescending(p => p.PaymentDate))
                {
                    report += $"• {payment.PaymentDate:yyyy/MM/dd} - {payment.TypeName} - {payment.Amount:C} - {payment.PaymentMethod}\n";
                    if (!string.IsNullOrEmpty(payment.Notes))
                    {
                        report += $"  ملاحظات: {payment.Notes}\n";
                    }
                }
            }
            else
            {
                report += "لا توجد مدفوعات مسجلة لهذا الطالب.\n";
            }

            return report;
        }

        private void BtnClose_Click(object sender, RoutedEventArgs e)
        {
            Close();
        }
    }
}
