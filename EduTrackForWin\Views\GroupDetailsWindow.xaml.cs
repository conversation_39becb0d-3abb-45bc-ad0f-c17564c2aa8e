using System.Windows;
using EduTrackForWin.Models;
using EduTrackForWin.Data;
using Microsoft.EntityFrameworkCore;

namespace EduTrackForWin.Views
{
    public partial class GroupDetailsWindow : Window
    {
        private readonly Group _group;
        private readonly EduTrackDbContext _context;

        public GroupDetailsWindow(Group group)
        {
            InitializeComponent();
            _group = group;
            _context = new EduTrackDbContext();
            LoadGroupDetails();
        }

        private async void LoadGroupDetails()
        {
            // Basic Information
            TxtGroupName.Text = _group.Name;
            TxtName.Text = _group.Name;
            TxtSubject.Text = string.IsNullOrEmpty(_group.Subject) ? "غير محدد" : _group.Subject;
            TxtGrade.Text = string.IsNullOrEmpty(_group.Grade) ? "غير محدد" : _group.Grade;
            TxtRoom.Text = string.IsNullOrEmpty(_group.Room) ? "غير محدد" : _group.Room;
            TxtTeacher.Text = _group.Teacher?.Name ?? "غير محدد";
            TxtMonthlyFee.Text = $"{_group.MonthlyFee:C}";
            TxtDescription.Text = string.IsNullOrEmpty(_group.Description) ? "لا يوجد وصف" : _group.Description;

            // Status
            TxtStatus.Text = _group.IsActive ? "نشطة" : "غير نشطة";
            TxtStatus.Foreground = _group.IsActive ? 
                System.Windows.Media.Brushes.Green : 
                System.Windows.Media.Brushes.Red;

            // Load detailed data
            await LoadDetailedData();
        }

        private async Task LoadDetailedData()
        {
            try
            {
                // Load group with all related data
                var groupWithData = await _context.Groups
                    .Include(g => g.Students)
                    .ThenInclude(s => s.Payments)
                    .Include(g => g.Sessions)
                    .Include(g => g.Teacher)
                    .FirstOrDefaultAsync(g => g.Id == _group.Id);

                if (groupWithData != null)
                {
                    // Student count
                    var studentCount = groupWithData.Students?.Count ?? 0;
                    TxtStudentCount.Text = studentCount.ToString();

                    // Financial Summary
                    var expectedRevenue = studentCount * groupWithData.MonthlyFee;
                    TxtExpectedRevenue.Text = $"{expectedRevenue:C}";

                    var totalPayments = groupWithData.Students?.Sum(s => s.TotalPaid) ?? 0;
                    TxtTotalPayments.Text = $"{totalPayments:C}";

                    var outstandingAmount = groupWithData.Students?.Sum(s => s.TotalDue) ?? 0;
                    TxtOutstandingAmount.Text = $"{outstandingAmount:C}";

                    // Students List
                    if (groupWithData.Students != null && groupWithData.Students.Any())
                    {
                        DgStudents.ItemsSource = groupWithData.Students.OrderBy(s => s.Name).ToList();
                    }

                    // Recent Sessions
                    var recentSessions = groupWithData.Sessions?
                        .OrderByDescending(s => s.Date)
                        .ThenByDescending(s => s.StartTime)
                        .Take(10)
                        .ToList();

                    if (recentSessions != null && recentSessions.Any())
                    {
                        DgSessions.ItemsSource = recentSessions;
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل البيانات التفصيلية: {ex.Message}", "خطأ", 
                              MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void BtnEdit_Click(object sender, RoutedEventArgs e)
        {
            var editWindow = new AddEditGroupWindow(_group);
            if (editWindow.ShowDialog() == true)
            {
                // Refresh the data
                LoadGroupDetails();
            }
        }

        private void BtnManageStudents_Click(object sender, RoutedEventArgs e)
        {
            var manageWindow = new ManageGroupStudentsWindow(_group);
            if (manageWindow.ShowDialog() == true)
            {
                // Refresh the data
                LoadGroupDetails();
            }
        }

        private void BtnAddSession_Click(object sender, RoutedEventArgs e)
        {
            // Create a new session for this group
            var newSession = new Session
            {
                GroupId = _group.Id,
                TeacherId = _group.TeacherId,
                Date = DateTime.Today,
                StartTime = new TimeSpan(9, 0, 0),
                EndTime = new TimeSpan(10, 0, 0)
            };

            var addSessionWindow = new AddEditSessionWindow(newSession);
            if (addSessionWindow.ShowDialog() == true)
            {
                // Refresh the data
                LoadGroupDetails();
            }
        }

        private async void BtnViewSchedule_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // Load group's schedule for current week
                var startOfWeek = DateTime.Today.AddDays(-(int)DateTime.Today.DayOfWeek);
                var endOfWeek = startOfWeek.AddDays(7);

                var weekSessions = await _context.Sessions
                    .Include(s => s.Teacher)
                    .Where(s => s.GroupId == _group.Id && 
                               s.Date >= startOfWeek && s.Date < endOfWeek)
                    .OrderBy(s => s.Date)
                    .ThenBy(s => s.StartTime)
                    .ToListAsync();

                var scheduleWindow = new GroupScheduleWindow(_group, weekSessions);
                scheduleWindow.ShowDialog();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في عرض الجدول: {ex.Message}", "خطأ", 
                              MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void BtnFinancialReport_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var financialReportWindow = new GroupFinancialReportWindow(_group);
                financialReportWindow.ShowDialog();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في عرض التقرير المالي: {ex.Message}", "خطأ", 
                              MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void BtnClose_Click(object sender, RoutedEventArgs e)
        {
            Close();
        }

        protected override void OnClosed(EventArgs e)
        {
            _context?.Dispose();
            base.OnClosed(e);
        }
    }
}
