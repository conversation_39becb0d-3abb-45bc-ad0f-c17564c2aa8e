<Page x:Class="EduTrackForWin.Views.StudentPaymentsPage"
      xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
      xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
      xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
      xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
      xmlns:local="clr-namespace:EduTrackForWin.Views"
      mc:Ignorable="d" 
      d:DesignHeight="800" d:DesignWidth="1200"
      Title="StudentPaymentsPage">

    <Page.Resources>
        <!-- Styles -->
        <Style x:Key="HeaderStyle" TargetType="TextBlock">
            <Setter Property="FontSize" Value="24"/>
            <Setter Property="FontWeight" Value="Bold"/>
            <Setter Property="Foreground" Value="#2E7D32"/>
            <Setter Property="Margin" Value="0,0,0,20"/>
        </Style>

        <Style x:Key="SubHeaderStyle" TargetType="TextBlock">
            <Setter Property="FontSize" Value="16"/>
            <Setter Property="FontWeight" Value="SemiBold"/>
            <Setter Property="Foreground" Value="#424242"/>
            <Setter Property="Margin" Value="0,0,0,10"/>
        </Style>

        <Style x:Key="StudentCardStyle" TargetType="Border">
            <Setter Property="Background" Value="White"/>
            <Setter Property="CornerRadius" Value="8"/>
            <Setter Property="Padding" Value="15"/>
            <Setter Property="Margin" Value="5"/>
            <Setter Property="Effect">
                <Setter.Value>
                    <DropShadowEffect Color="Gray" Direction="270" ShadowDepth="2" Opacity="0.2" BlurRadius="8"/>
                </Setter.Value>
            </Setter>
        </Style>

        <Style x:Key="PrimaryButtonStyle" TargetType="Button">
            <Setter Property="Background" Value="#4CAF50"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Padding" Value="15,8"/>
            <Setter Property="Margin" Value="5"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}" 
                              CornerRadius="5">
                            <ContentPresenter HorizontalAlignment="Center" 
                                            VerticalAlignment="Center"
                                            Margin="{TemplateBinding Padding}"/>
                        </Border>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <Style x:Key="SecondaryButtonStyle" TargetType="Button">
            <Setter Property="Background" Value="#2196F3"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Padding" Value="15,8"/>
            <Setter Property="Margin" Value="5"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}" 
                              CornerRadius="5">
                            <ContentPresenter HorizontalAlignment="Center" 
                                            VerticalAlignment="Center"
                                            Margin="{TemplateBinding Padding}"/>
                        </Border>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <Style x:Key="WarningButtonStyle" TargetType="Button">
            <Setter Property="Background" Value="#FF9800"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Padding" Value="15,8"/>
            <Setter Property="Margin" Value="5"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}" 
                              CornerRadius="5">
                            <ContentPresenter HorizontalAlignment="Center" 
                                            VerticalAlignment="Center"
                                            Margin="{TemplateBinding Padding}"/>
                        </Border>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <Style x:Key="FilterComboBoxStyle" TargetType="ComboBox">
            <Setter Property="Padding" Value="8"/>
            <Setter Property="Margin" Value="5"/>
            <Setter Property="MinWidth" Value="150"/>
        </Style>

        <Style x:Key="SearchTextBoxStyle" TargetType="TextBox">
            <Setter Property="Padding" Value="8"/>
            <Setter Property="Margin" Value="5"/>
            <Setter Property="MinWidth" Value="200"/>
        </Style>
    </Page.Resources>

    <Grid Background="#F5F5F5">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <Border Grid.Row="0" Background="White" Padding="20" Margin="20,20,20,10">
            <Border.Effect>
                <DropShadowEffect Color="Gray" Direction="270" ShadowDepth="1" Opacity="0.2" BlurRadius="5"/>
            </Border.Effect>
            
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <StackPanel Grid.Column="0">
                    <TextBlock Text="💳 تسجيل دفع الطلاب" Style="{StaticResource HeaderStyle}"/>
                    <TextBlock Text="إدارة مدفوعات واشتراكات الطلاب" 
                             Style="{StaticResource SubHeaderStyle}"/>
                </StackPanel>

                <StackPanel Grid.Column="1" Orientation="Horizontal">
                    <Button Name="BtnQuickPayment" Content="دفعة سريعة" 
                          Style="{StaticResource PrimaryButtonStyle}" Click="BtnQuickPayment_Click"/>
                    <Button Name="BtnBulkPayment" Content="دفع جماعي" 
                          Style="{StaticResource SecondaryButtonStyle}" Click="BtnBulkPayment_Click"/>
                    <Button Name="BtnPaymentReminder" Content="تذكير بالدفع" 
                          Style="{StaticResource WarningButtonStyle}" Click="BtnPaymentReminder_Click"/>
                </StackPanel>
            </Grid>
        </Border>

        <!-- Filters and Search -->
        <Border Grid.Row="1" Background="White" Padding="20" Margin="20,10,20,10">
            <Border.Effect>
                <DropShadowEffect Color="Gray" Direction="270" ShadowDepth="1" Opacity="0.2" BlurRadius="5"/>
            </Border.Effect>
            
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <TextBlock Grid.Column="0" Text="فلترة:" VerticalAlignment="Center" 
                         FontWeight="SemiBold" Margin="0,0,10,0"/>
                
                <ComboBox Name="CmbGroupFilter" Grid.Column="1" 
                        Style="{StaticResource FilterComboBoxStyle}"
                        SelectionChanged="Filter_Changed">
                    <ComboBoxItem Content="جميع المجموعات" IsSelected="True"/>
                </ComboBox>

                <ComboBox Name="CmbPaymentStatusFilter" Grid.Column="2" 
                        Style="{StaticResource FilterComboBoxStyle}"
                        SelectionChanged="Filter_Changed">
                    <ComboBoxItem Content="جميع الحالات" IsSelected="True"/>
                    <ComboBoxItem Content="مدفوع"/>
                    <ComboBoxItem Content="متأخر"/>
                    <ComboBoxItem Content="جزئي"/>
                </ComboBox>

                <ComboBox Name="CmbMonthFilter" Grid.Column="3" 
                        Style="{StaticResource FilterComboBoxStyle}"
                        SelectionChanged="Filter_Changed">
                    <ComboBoxItem Content="الشهر الحالي" IsSelected="True"/>
                </ComboBox>

                <TextBox Name="TxtSearch" Grid.Column="4" 
                       Style="{StaticResource SearchTextBoxStyle}"
                       TextChanged="TxtSearch_TextChanged"
                       Text="البحث عن طالب..."
                       GotFocus="TxtSearch_GotFocus"
                       LostFocus="TxtSearch_LostFocus"/>

                <Button Name="BtnRefresh" Grid.Column="5" Content="تحديث" 
                      Style="{StaticResource SecondaryButtonStyle}" Click="BtnRefresh_Click"/>
            </Grid>
        </Border>

        <!-- Students List -->
        <Border Grid.Row="2" Background="White" Padding="20" Margin="20,10,20,20">
            <Border.Effect>
                <DropShadowEffect Color="Gray" Direction="270" ShadowDepth="1" Opacity="0.2" BlurRadius="5"/>
            </Border.Effect>
            
            <ScrollViewer VerticalScrollBarVisibility="Auto">
                <ItemsControl Name="StudentsItemsControl">
                    <ItemsControl.ItemsPanel>
                        <ItemsPanelTemplate>
                            <UniformGrid Columns="2"/>
                        </ItemsPanelTemplate>
                    </ItemsControl.ItemsPanel>
                    
                    <ItemsControl.ItemTemplate>
                        <DataTemplate>
                            <!-- Student Payment Card Template will be defined in code-behind -->
                        </DataTemplate>
                    </ItemsControl.ItemTemplate>
                </ItemsControl>
            </ScrollViewer>
        </Border>
    </Grid>
</Page>
