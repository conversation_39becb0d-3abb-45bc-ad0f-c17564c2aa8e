using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using EduTrackForWin.Data;
using EduTrackForWin.Models;
using EduTrackForWin.Services;
using Microsoft.EntityFrameworkCore;

namespace EduTrackForWin.Views
{
    /// <summary>
    /// Interaction logic for FinancePage.xaml
    /// </summary>
    public partial class FinancePage : Page
    {
        private readonly EduTrackDbContext _context;
        private readonly PaymentService _paymentService;
        private readonly StudentService _studentService;
        private ObservableCollection<Payment> _payments;
        private List<Payment> _allPayments;
        private List<Student> _students;

        public FinancePage()
        {
            try
            {
                InitializeComponent();
                _context = new EduTrackDbContext();
                _paymentService = new PaymentService();
                _studentService = new StudentService();
                _payments = new ObservableCollection<Payment>();
                _allPayments = new List<Payment>();
                _students = new List<Student>();

                DgPayments.ItemsSource = _payments;

                Loaded += FinancePage_Loaded;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تهيئة صفحة النظام المالي: {ex.Message}", "خطأ",
                              MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async void FinancePage_Loaded(object sender, RoutedEventArgs e)
        {
            await LoadData();
        }

        private async Task LoadData()
        {
            try
            {
                await LoadStudents();
                await LoadPayments();
                await LoadStatistics();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل البيانات: {ex.Message}", "خطأ",
                              MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async Task LoadStudents()
        {
            try
            {
                _students = await _studentService.GetAllStudentsAsync();

                if (CmbStudentFilter != null)
                {
                    CmbStudentFilter.Items.Clear();
                    CmbStudentFilter.Items.Add(new ComboBoxItem { Content = "جميع الطلاب", Tag = 0 });

                    foreach (var student in _students)
                    {
                        CmbStudentFilter.Items.Add(new ComboBoxItem { Content = student.Name, Tag = student.Id });
                    }

                    CmbStudentFilter.SelectedIndex = 0;
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل الطلاب: {ex.Message}", "خطأ",
                              MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async Task LoadPayments()
        {
            try
            {
                _allPayments = await _paymentService.GetAllPaymentsAsync();
                RefreshPaymentsList();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل المدفوعات: {ex.Message}", "خطأ",
                              MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void RefreshPaymentsList()
        {
            try
            {
                var filteredPayments = _allPayments.AsEnumerable();

                // Filter by student
                if (CmbStudentFilter?.SelectedItem is ComboBoxItem studentItem &&
                    studentItem.Tag is int studentId && studentId > 0)
                {
                    filteredPayments = filteredPayments.Where(p => p.StudentId == studentId);
                }

                // Filter by payment type
                if (CmbPaymentTypeFilter?.SelectedItem is ComboBoxItem typeItem &&
                    typeItem.Content.ToString() != "جميع الأنواع")
                {
                    var paymentTypeName = typeItem.Content.ToString();
                    filteredPayments = filteredPayments.Where(p => p.TypeName == paymentTypeName);
                }

                // Filter by date
                if (DpDateFilter?.SelectedDate.HasValue == true)
                {
                    var selectedDate = DpDateFilter.SelectedDate.Value.Date;
                    filteredPayments = filteredPayments.Where(p => p.PaymentDate.Date == selectedDate);
                }

                // Apply search filter
                if (TxtSearch != null && !string.IsNullOrWhiteSpace(TxtSearch.Text) &&
                    TxtSearch.Text != "البحث في المدفوعات...")
                {
                    var searchTerm = TxtSearch.Text.ToLower();
                    filteredPayments = filteredPayments.Where(p =>
                        p.Student?.Name?.ToLower().Contains(searchTerm) == true ||
                        p.TypeName?.ToLower().Contains(searchTerm) == true ||
                        p.PaymentMethod?.ToLower().Contains(searchTerm) == true ||
                        p.Notes?.ToLower().Contains(searchTerm) == true);
                }

                // Sort by date (newest first)
                var sortedPayments = filteredPayments
                    .OrderByDescending(p => p.PaymentDate)
                    .ToList();

                _payments.Clear();
                foreach (var payment in sortedPayments)
                {
                    _payments.Add(payment);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تصفية المدفوعات: {ex.Message}", "خطأ",
                              MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async Task LoadStatistics()
        {
            try
            {
                using var context = new EduTrackDbContext();

                // Total Revenue
                var totalRevenue = 0m;
                try
                {
                    totalRevenue = await context.Payments.SumAsync(p => p.Amount);
                }
                catch
                {
                    totalRevenue = 0m;
                }
                if (TxtTotalRevenue != null) TxtTotalRevenue.Text = $"{totalRevenue:C}";

                // Monthly Revenue
                var monthlyRevenue = 0m;
                try
                {
                    var thisMonth = new DateTime(DateTime.Now.Year, DateTime.Now.Month, 1);
                    monthlyRevenue = await context.Payments
                        .Where(p => p.PaymentDate >= thisMonth)
                        .SumAsync(p => p.Amount);
                }
                catch
                {
                    monthlyRevenue = 0m;
                }
                if (TxtMonthlyRevenue != null) TxtMonthlyRevenue.Text = $"{monthlyRevenue:C}";

                // Outstanding Dues - Calculate on client side
                var outstandingDues = 0m;
                try
                {
                    var students = await context.Students
                        .Include(s => s.Payments)
                        .Include(s => s.Group)
                        .ToListAsync();

                    outstandingDues = students.Sum(s => s.TotalDue);
                }
                catch
                {
                    outstandingDues = 0m;
                }
                if (TxtOutstandingDues != null) TxtOutstandingDues.Text = $"{outstandingDues:C}";

                // Today's Payments
                var todayPayments = 0m;
                try
                {
                    var today = DateTime.Today;
                    todayPayments = await context.Payments
                        .Where(p => p.PaymentDate.Date == today)
                        .SumAsync(p => p.Amount);
                }
                catch
                {
                    todayPayments = 0m;
                }
                if (TxtTodayPayments != null) TxtTodayPayments.Text = $"{todayPayments:C}";
            }
            catch (Exception ex)
            {
                // Set default values if everything fails
                if (TxtTotalRevenue != null) TxtTotalRevenue.Text = "0 ج.م";
                if (TxtMonthlyRevenue != null) TxtMonthlyRevenue.Text = "0 ج.م";
                if (TxtOutstandingDues != null) TxtOutstandingDues.Text = "0 ج.م";
                if (TxtTodayPayments != null) TxtTodayPayments.Text = "0 ج.م";

                System.Diagnostics.Debug.WriteLine($"خطأ في تحميل الإحصائيات: {ex.Message}");
            }
        }

        // Event Handlers
        private async void BtnAddPayment_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var addPaymentWindow = new AddEditPaymentWindow();
                if (addPaymentWindow.ShowDialog() == true)
                {
                    await LoadPayments();
                    await LoadStatistics();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في فتح نافذة إضافة الدفعة: {ex.Message}", "خطأ",
                              MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void BtnGenerateReport_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                MessageBox.Show("سيتم إضافة ميزة التقارير في التحديث القادم", "التقارير",
                              MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في إنشاء التقرير: {ex.Message}", "خطأ",
                              MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async void Filter_Changed(object sender, SelectionChangedEventArgs e)
        {
            if (_allPayments != null)
            {
                RefreshPaymentsList();
            }
        }

        private void TxtSearch_TextChanged(object sender, TextChangedEventArgs e)
        {
            if (_allPayments != null)
            {
                RefreshPaymentsList();
            }
        }

        private async void BtnRefresh_Click(object sender, RoutedEventArgs e)
        {
            await LoadData();
        }

        private void BtnViewPayment_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (DgPayments.SelectedItem is Payment selectedPayment)
                {
                    var viewWindow = new AddEditPaymentWindow(selectedPayment, true); // true for view-only mode
                    viewWindow.ShowDialog();
                }
                else
                {
                    MessageBox.Show("يرجى اختيار دفعة لعرضها", "تنبيه",
                                  MessageBoxButton.OK, MessageBoxImage.Warning);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في عرض الدفعة: {ex.Message}", "خطأ",
                              MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async void BtnEditPayment_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (DgPayments.SelectedItem is Payment selectedPayment)
                {
                    var editWindow = new AddEditPaymentWindow(selectedPayment);
                    if (editWindow.ShowDialog() == true)
                    {
                        await LoadPayments();
                        await LoadStatistics();
                    }
                }
                else
                {
                    MessageBox.Show("يرجى اختيار دفعة لتعديلها", "تنبيه",
                                  MessageBoxButton.OK, MessageBoxImage.Warning);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تعديل الدفعة: {ex.Message}", "خطأ",
                              MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async void BtnDeletePayment_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (DgPayments.SelectedItem is Payment selectedPayment)
                {
                    var result = MessageBox.Show(
                        $"هل أنت متأكد من حذف دفعة {selectedPayment.Student?.Name} بمبلغ {selectedPayment.Amount:C}؟\n\nهذا الإجراء لا يمكن التراجع عنه.",
                        "تأكيد الحذف",
                        MessageBoxButton.YesNo,
                        MessageBoxImage.Warning);

                    if (result == MessageBoxResult.Yes)
                    {
                        await _paymentService.DeletePaymentAsync(selectedPayment.Id);
                        await LoadPayments();
                        await LoadStatistics();

                        MessageBox.Show("تم حذف الدفعة بنجاح", "تم الحذف",
                                      MessageBoxButton.OK, MessageBoxImage.Information);
                    }
                }
                else
                {
                    MessageBox.Show("يرجى اختيار دفعة لحذفها", "تنبيه",
                                  MessageBoxButton.OK, MessageBoxImage.Warning);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في حذف الدفعة: {ex.Message}", "خطأ",
                              MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void TxtSearch_GotFocus(object sender, RoutedEventArgs e)
        {
            if (TxtSearch.Text == "البحث في المدفوعات...")
            {
                TxtSearch.Text = "";
                TxtSearch.Foreground = System.Windows.Media.Brushes.Black;
            }
        }

        private void TxtSearch_LostFocus(object sender, RoutedEventArgs e)
        {
            if (string.IsNullOrWhiteSpace(TxtSearch.Text))
            {
                TxtSearch.Text = "البحث في المدفوعات...";
                TxtSearch.Foreground = System.Windows.Media.Brushes.Gray;
            }
        }
    }
}
