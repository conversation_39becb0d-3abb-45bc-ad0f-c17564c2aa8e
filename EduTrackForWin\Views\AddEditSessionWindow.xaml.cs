using System.Windows;
using EduTrackForWin.Models;
using EduTrackForWin.Data;
using Microsoft.EntityFrameworkCore;
using System;
using System.Windows.Controls;

namespace EduTrackForWin.Views
{
    public partial class AddEditSessionWindow : Window
    {
        private readonly EduTrackDbContext _context;
        private Session? _currentSession;
        private bool _isEditMode;

        public AddEditSessionWindow(Session? session = null)
        {
            try
            {
                InitializeComponent();
                _context = new EduTrackDbContext();
                _currentSession = session;
                _isEditMode = session != null;

                InitializeWindow();
                LoadData();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تهيئة نافذة الحصة: {ex.Message}", "خطأ",
                              MessageBoxButton.OK, MessageBoxImage.Error);
                throw;
            }
        }

        private void InitializeWindow()
        {
            if (_isEditMode && _currentSession != null)
            {
                TxtTitle.Text = "تعديل بيانات الحصة";
                Title = "تعديل بيانات الحصة";
                BtnSave.Content = "تحديث";
            }
            else
            {
                TxtTitle.Text = "إضافة حصة جديدة";
                Title = "إضافة حصة جديدة";
                BtnSave.Content = "حفظ";
                DpDate.SelectedDate = DateTime.Today;
            }
        }

        private async void LoadData()
        {
            await LoadGroups();
            await LoadTeachers();
            
            if (_isEditMode && _currentSession != null)
            {
                LoadSessionData();
            }
        }

        private async Task LoadGroups()
        {
            try
            {
                var groups = await _context.Groups
                    .Include(g => g.Teacher)
                    .Where(g => g.IsActive)
                    .OrderBy(g => g.Name)
                    .ToListAsync();

                CmbGroup.ItemsSource = groups;
                
                if (groups.Any())
                {
                    CmbGroup.SelectedIndex = 0;
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل المجموعات: {ex.Message}", "خطأ", 
                              MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async Task LoadTeachers()
        {
            try
            {
                var teachers = await _context.Teachers
                    .OrderBy(t => t.Name)
                    .ToListAsync();

                CmbTeacher.ItemsSource = teachers;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل المعلمين: {ex.Message}", "خطأ", 
                              MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void LoadSessionData()
        {
            if (_currentSession == null) return;

            DpDate.SelectedDate = _currentSession.Date;
            TxtStartTime.Text = _currentSession.StartTime.ToString(@"hh\:mm");
            TxtEndTime.Text = _currentSession.EndTime.ToString(@"hh\:mm");
            TxtRoom.Text = _currentSession.Room;
            TxtTopic.Text = _currentSession.Topic;
            TxtNotes.Text = _currentSession.Notes;
            ChkIsRecurring.IsChecked = _currentSession.IsRecurring;
            if (_currentSession.IsRecurring)
            {
                DpRecurrenceEndDate.SelectedDate = _currentSession.RecurrenceEndDate;
                RecurrencePanel.Visibility = Visibility.Visible;
            }

            // Set group
            CmbGroup.SelectedValue = _currentSession.GroupId;
            
            // Set teacher
            CmbTeacher.SelectedValue = _currentSession.TeacherId;
        }

        private void CmbGroup_SelectionChanged(object sender, System.Windows.Controls.SelectionChangedEventArgs e)
        {
            if (CmbGroup.SelectedItem is Group selectedGroup)
            {
                // Auto-select the group's teacher
                CmbTeacher.SelectedValue = selectedGroup.TeacherId;
                
                // Auto-fill room if group has a default room
                if (!string.IsNullOrEmpty(selectedGroup.Room))
                {
                    TxtRoom.Text = selectedGroup.Room;
                }
            }
        }

        private async void BtnSave_Click(object sender, RoutedEventArgs e)
        {
            if (!ValidateInput())
                return;

            try
            {
                var session = _currentSession ?? new Session();

                // Update session properties
                session.Date = DpDate.SelectedDate!.Value;
                session.StartTime = TimeSpan.Parse(TxtStartTime.Text);
                session.EndTime = TimeSpan.Parse(TxtEndTime.Text);
                session.Room = TxtRoom.Text.Trim();
                session.Topic = TxtTopic.Text.Trim();
                session.Notes = TxtNotes.Text.Trim();
                session.IsRecurring = ChkIsRecurring.IsChecked == true;

                if (session.IsRecurring)
                {
                    session.RecurrenceType = "Weekly";
                    session.RecurrenceEndDate = DpRecurrenceEndDate.SelectedDate;
                }
                else
                {
                    session.RecurrenceType = null;
                    session.RecurrenceEndDate = null;
                }


                if (CmbGroup.SelectedValue is int groupId)
                {
                    session.GroupId = groupId;
                }

                if (CmbTeacher.SelectedValue is int teacherId)
                {
                    session.TeacherId = teacherId;
                }

                if (_isEditMode)
                {
                    session.UpdatedAt = DateTime.Now;
                    _context.Sessions.Update(session);
                    await _context.SaveChangesAsync();
                    MessageBox.Show("تم تحديث بيانات الحصة بنجاح", "نجح",
                                  MessageBoxButton.OK, MessageBoxImage.Information);
                }
                else
                {
                    var sessionService = new Services.SessionService();
                    await sessionService.AddSessionAsync(session);
                    MessageBox.Show("تم إضافة الحصة بنجاح", "نجح",
                                  MessageBoxButton.OK, MessageBoxImage.Information);
                }

                DialogResult = true;
                Close();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في حفظ بيانات الحصة: {ex.Message}", "خطأ",
                              MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private bool ValidateInput()
        {
            if (!DpDate.SelectedDate.HasValue)
            {
                MessageBox.Show("يرجى اختيار تاريخ الحصة", "خطأ في البيانات",
                              MessageBoxButton.OK, MessageBoxImage.Warning);
                DpDate.Focus();
                return false;
            }

            if (ChkIsRecurring.IsChecked == true && !DpRecurrenceEndDate.SelectedDate.HasValue)
            {
                MessageBox.Show("يرجى اختيار تاريخ انتهاء التكرار", "خطأ في البيانات",
                              MessageBoxButton.OK, MessageBoxImage.Warning);
                DpRecurrenceEndDate.Focus();
                return false;
            }

            if (ChkIsRecurring.IsChecked == true && DpRecurrenceEndDate.SelectedDate.HasValue && DpRecurrenceEndDate.SelectedDate.Value <= DpDate.SelectedDate.Value)
            {
                MessageBox.Show("تاريخ انتهاء التكرار يجب أن يكون بعد تاريخ بدء الحصة", "خطأ في البيانات",
                              MessageBoxButton.OK, MessageBoxImage.Warning);
                DpRecurrenceEndDate.Focus();
                return false;
            }

            if (!TimeSpan.TryParse(TxtStartTime.Text, out var startTime))
            {
                MessageBox.Show("يرجى إدخال وقت البداية بصيغة صحيحة (مثال: 09:00)", "خطأ في البيانات", 
                              MessageBoxButton.OK, MessageBoxImage.Warning);
                TxtStartTime.Focus();
                return false;
            }

            if (!TimeSpan.TryParse(TxtEndTime.Text, out var endTime))
            {
                MessageBox.Show("يرجى إدخال وقت النهاية بصيغة صحيحة (مثال: 10:00)", "خطأ في البيانات", 
                              MessageBoxButton.OK, MessageBoxImage.Warning);
                TxtEndTime.Focus();
                return false;
            }

            if (endTime <= startTime)
            {
                MessageBox.Show("وقت النهاية يجب أن يكون بعد وقت البداية", "خطأ في البيانات", 
                              MessageBoxButton.OK, MessageBoxImage.Warning);
                TxtEndTime.Focus();
                return false;
            }

            if (CmbGroup.SelectedValue == null)
            {
                MessageBox.Show("يرجى اختيار المجموعة", "خطأ في البيانات", 
                              MessageBoxButton.OK, MessageBoxImage.Warning);
                CmbGroup.Focus();
                return false;
            }

            if (CmbTeacher.SelectedValue == null)
            {
                MessageBox.Show("يرجى اختيار المعلم", "خطأ في البيانات", 
                              MessageBoxButton.OK, MessageBoxImage.Warning);
                CmbTeacher.Focus();
                return false;
            }

            return true;
        }

        private void BtnCancel_Click(object sender, RoutedEventArgs e)
        {
            DialogResult = false;
            Close();
        }

        private void ChkIsRecurring_CheckedChanged(object sender, RoutedEventArgs e)
        {
            RecurrencePanel.Visibility = ChkIsRecurring.IsChecked == true ? Visibility.Visible : Visibility.Collapsed;
        }

        protected override void OnClosed(EventArgs e)
        {
            _context?.Dispose();
            base.OnClosed(e);
        }
    }
}
