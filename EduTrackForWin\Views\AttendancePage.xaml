<Page x:Class="EduTrackForWin.Views.AttendancePage"
      xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
      xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
      xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
      xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
      xmlns:local="clr-namespace:EduTrackForWin.Views"
      mc:Ignorable="d"
      d:DesignHeight="450" d:DesignWidth="800"
      Title="AttendancePage">

    <Page.Resources>
        <!-- Card Style -->
        <Style x:Key="CardStyle" TargetType="Border" BasedOn="{StaticResource ModernCardStyle}">
            <Setter Property="Background" Value="{StaticResource SurfaceColor}"/>
            <Setter Property="CornerRadius" Value="8"/>
            <Setter Property="Padding" Value="20"/>
            <Setter Property="Margin" Value="10"/>
        </Style>

        <!-- Button Styles -->
        <Style x:Key="PrimaryButtonStyle" TargetType="Button">
            <Setter Property="Background" Value="{StaticResource SuccessColor}"/>
            <Setter Property="Foreground" Value="White"/>
        </Style>

        <Style x:Key="SecondaryButtonStyle" TargetType="Button">
            <Setter Property="Background" Value="{StaticResource PrimaryColor}"/>
            <Setter Property="Foreground" Value="White"/>
        </Style>
    </Page.Resources>

    <Grid Background="#F5F5F5" Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <Border Grid.Row="0" Style="{StaticResource CardStyle}" Background="#E8F5E8">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <StackPanel Grid.Column="0">
                    <TextBlock Text="✅ الحضور والغياب" FontSize="24" FontWeight="Bold" Foreground="#2E7D32"/>
                    <TextBlock Text="تسجيل ومتابعة حضور الطلاب في الحصص" FontSize="14" Foreground="#424242" Margin="0,5,0,0"/>
                </StackPanel>

                <Button Grid.Column="1" Name="BtnTakeAttendance" Content="+ تسجيل حضور جديد"
                      Style="{StaticResource PrimaryButtonStyle}"
                      FontSize="14" Padding="20,10" Click="BtnTakeAttendance_Click"/>
            </Grid>
        </Border>

        <!-- Statistics Summary -->
        <Border Grid.Row="1" Style="{StaticResource CardStyle}">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>

                <!-- Today's Attendance -->
                <StackPanel Grid.Column="0" HorizontalAlignment="Center">
                    <TextBlock Text="📊" FontSize="36" HorizontalAlignment="Center"/>
                    <TextBlock Name="TxtTodayAttendance" Text="85%" FontSize="20" FontWeight="Bold"
                             Foreground="#4CAF50" HorizontalAlignment="Center"/>
                    <TextBlock Text="حضور اليوم" FontSize="12" Foreground="#757575" HorizontalAlignment="Center"/>
                </StackPanel>

                <!-- This Week -->
                <StackPanel Grid.Column="1" HorizontalAlignment="Center">
                    <TextBlock Text="📈" FontSize="36" HorizontalAlignment="Center"/>
                    <TextBlock Name="TxtWeekAttendance" Text="78%" FontSize="20" FontWeight="Bold"
                             Foreground="#2196F3" HorizontalAlignment="Center"/>
                    <TextBlock Text="حضور الأسبوع" FontSize="12" Foreground="#757575" HorizontalAlignment="Center"/>
                </StackPanel>

                <!-- This Month -->
                <StackPanel Grid.Column="2" HorizontalAlignment="Center">
                    <TextBlock Text="📅" FontSize="36" HorizontalAlignment="Center"/>
                    <TextBlock Name="TxtMonthAttendance" Text="82%" FontSize="20" FontWeight="Bold"
                             Foreground="#FF9800" HorizontalAlignment="Center"/>
                    <TextBlock Text="حضور الشهر" FontSize="12" Foreground="#757575" HorizontalAlignment="Center"/>
                </StackPanel>

                <!-- Absent Students -->
                <StackPanel Grid.Column="3" HorizontalAlignment="Center">
                    <TextBlock Text="⚠️" FontSize="36" HorizontalAlignment="Center"/>
                    <TextBlock Name="TxtAbsentStudents" Text="5" FontSize="20" FontWeight="Bold"
                             Foreground="#F44336" HorizontalAlignment="Center"/>
                    <TextBlock Text="طلاب غائبون اليوم" FontSize="12" Foreground="#757575" HorizontalAlignment="Center"/>
                </StackPanel>
            </Grid>
        </Border>

        <!-- Main Content -->
        <Grid Grid.Row="2">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="2*"/>
                <ColumnDefinition Width="*"/>
            </Grid.ColumnDefinitions>

            <!-- Recent Attendance Records -->
            <Border Grid.Column="0" Style="{StaticResource CardStyle}" Margin="0,0,10,0">
                <StackPanel>
                    <Grid Margin="0,0,0,15">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                        </Grid.ColumnDefinitions>

                        <TextBlock Grid.Column="0" Text="سجلات الحضور الحديثة" FontSize="18" FontWeight="SemiBold" Foreground="#212121"/>

                        <StackPanel Grid.Column="1" Orientation="Horizontal">
                            <DatePicker Name="DpFilterDate" Margin="0,0,10,0" SelectedDate="{x:Static sys:DateTime.Today}"
                                      xmlns:sys="clr-namespace:System;assembly=mscorlib"/>
                            <Button Name="BtnFilter" Content="🔍 تصفية" Style="{StaticResource SecondaryButtonStyle}"
                                  Click="BtnFilter_Click"/>
                        </StackPanel>
                    </Grid>

                    <DataGrid Name="DgAttendance"
                            AutoGenerateColumns="False"
                            CanUserAddRows="False"
                            CanUserDeleteRows="False"
                            IsReadOnly="True"
                            GridLinesVisibility="Horizontal"
                            HeadersVisibility="Column"
                            BorderThickness="0"
                            Background="Transparent"
                            RowBackground="White"
                            AlternatingRowBackground="#F5F5F5"
                            Height="300">

                        <DataGrid.Columns>
                            <DataGridTextColumn Header="التاريخ" Binding="{Binding Date, StringFormat='{}{0:yyyy/MM/dd}'}" Width="100"/>
                            <DataGridTextColumn Header="الحصة" Binding="{Binding SessionInfo}" Width="150"/>
                            <DataGridTextColumn Header="المجموعة" Binding="{Binding GroupName}" Width="120"/>
                            <DataGridTextColumn Header="الطالب" Binding="{Binding StudentName}" Width="150"/>

                            <DataGridTemplateColumn Header="الحالة" Width="80">
                                <DataGridTemplateColumn.CellTemplate>
                                    <DataTemplate>
                                        <Border CornerRadius="10" Padding="8,4">
                                            <Border.Style>
                                                <Style TargetType="Border">
                                                    <Style.Triggers>
                                                        <DataTrigger Binding="{Binding IsPresent}" Value="True">
                                                            <Setter Property="Background" Value="#4CAF50"/>
                                                        </DataTrigger>
                                                        <DataTrigger Binding="{Binding IsPresent}" Value="False">
                                                            <Setter Property="Background" Value="#F44336"/>
                                                        </DataTrigger>
                                                    </Style.Triggers>
                                                </Style>
                                            </Border.Style>
                                            <TextBlock Text="{Binding IsPresent, Converter={StaticResource BoolToAttendanceStatusConverter}}"
                                                     Foreground="White" FontSize="10" HorizontalAlignment="Center"/>
                                        </Border>
                                    </DataTemplate>
                                </DataGridTemplateColumn.CellTemplate>
                            </DataGridTemplateColumn>

                            <DataGridTextColumn Header="ملاحظات" Binding="{Binding Notes}" Width="*"/>
                        </DataGrid.Columns>
                    </DataGrid>
                </StackPanel>
            </Border>

            <!-- Quick Actions -->
            <Border Grid.Column="1" Style="{StaticResource CardStyle}" Margin="10,0,0,0">
                <StackPanel>
                    <TextBlock Text="إجراءات سريعة" FontSize="18" FontWeight="SemiBold"
                             Margin="0,0,0,20" Foreground="#212121"/>

                    <Button Name="BtnTodaySessions" Content="حصص اليوم"
                          Style="{StaticResource PrimaryButtonStyle}"
                          HorizontalAlignment="Stretch" Margin="0,5"
                          Click="BtnTodaySessions_Click"/>

                    <Button Name="BtnAbsentStudents" Content="الطلاب الغائبون"
                          Style="{StaticResource SecondaryButtonStyle}"
                          HorizontalAlignment="Stretch" Margin="0,5"
                          Click="BtnAbsentStudents_Click"/>

                    <Button Name="BtnAttendanceReport" Content="تقرير الحضور"
                          Style="{StaticResource SecondaryButtonStyle}"
                          HorizontalAlignment="Stretch" Margin="0,5"
                          Click="BtnAttendanceReport_Click"/>

                    <Button Name="BtnLateStudents" Content="الطلاب المتأخرون"
                          Background="#FF9800" Foreground="White"
                          BorderThickness="0" Padding="15,8" Margin="0,5"
                          Cursor="Hand" HorizontalAlignment="Stretch"
                          Click="BtnLateStudents_Click">
                        <Button.Template>
                            <ControlTemplate TargetType="Button">
                                <Border Background="{TemplateBinding Background}"
                                      CornerRadius="5">
                                    <ContentPresenter HorizontalAlignment="Center"
                                                    VerticalAlignment="Center"
                                                    Margin="{TemplateBinding Padding}"/>
                                </Border>
                            </ControlTemplate>
                        </Button.Template>
                    </Button>

                    <Separator Margin="0,20"/>

                    <TextBlock Text="إحصائيات سريعة" FontSize="16" FontWeight="SemiBold"
                             Margin="0,0,0,10" Foreground="#212121"/>

                    <StackPanel Margin="0,5">
                        <TextBlock Text="حصص اليوم:" FontSize="12" Foreground="#757575"/>
                        <TextBlock Name="TxtTodaySessionsCount" Text="8" FontSize="16" FontWeight="Bold" Foreground="#4CAF50"/>
                    </StackPanel>

                    <StackPanel Margin="0,5">
                        <TextBlock Text="طلاب حاضرون:" FontSize="12" Foreground="#757575"/>
                        <TextBlock Name="TxtPresentStudents" Text="45" FontSize="16" FontWeight="Bold" Foreground="#2196F3"/>
                    </StackPanel>

                    <StackPanel Margin="0,5">
                        <TextBlock Text="طلاب غائبون:" FontSize="12" Foreground="#757575"/>
                        <TextBlock Name="TxtAbsentStudentsCount" Text="5" FontSize="16" FontWeight="Bold" Foreground="#F44336"/>
                    </StackPanel>
                </StackPanel>
            </Border>
        </Grid>
    </Grid>
</Page>
