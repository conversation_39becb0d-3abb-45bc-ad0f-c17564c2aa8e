﻿<Application x:Class="EduTrackForWin.App"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:local="clr-namespace:EduTrackForWin"
             xmlns:helpers="clr-namespace:EduTrackForWin.Helpers"
             StartupUri="MainWindow.xaml">
    <Application.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <!-- Modern App Styles -->
                <ResourceDictionary Source="Resources/Styles/AppStyles.xaml"/>
                <ResourceDictionary Source="Resources/Styles/ButtonStyles.xaml"/>
                <ResourceDictionary Source="Resources/Styles/InputStyles.xaml"/>
                <ResourceDictionary Source="Resources/Styles/DataGridStyles.xaml"/>
            </ResourceDictionary.MergedDictionaries>

            <!-- Global Converters -->
            <helpers:BoolToStatusConverter x:Key="BoolToStatusConverter"/>
            <helpers:BoolToActiveStatusConverter x:Key="BoolToActiveStatusConverter"/>
            <helpers:BoolToAttendanceStatusConverter x:Key="BoolToAttendanceStatusConverter"/>
            <helpers:PaymentTypeConverter x:Key="PaymentTypeConverter"/>
            <helpers:CurrencyConverter x:Key="CurrencyConverter"/>
            <helpers:DateTimeConverter x:Key="DateTimeConverter"/>
            <helpers:TimeSpanConverter x:Key="TimeSpanConverter"/>
            <helpers:PercentageConverter x:Key="PercentageConverter"/>

            <!-- Legacy Global Styles (for backward compatibility) -->
            <Style x:Key="GlobalButtonStyle" TargetType="Button">
                <Setter Property="FontFamily" Value="Segoe UI"/>
                <Setter Property="FontSize" Value="14"/>
                <Setter Property="Cursor" Value="Hand"/>
            </Style>

            <Style x:Key="GlobalTextBlockStyle" TargetType="TextBlock">
                <Setter Property="FontFamily" Value="Segoe UI"/>
                <Setter Property="FontSize" Value="14"/>
            </Style>

            <Style x:Key="GlobalTextBoxStyle" TargetType="TextBox">
                <Setter Property="FontFamily" Value="Segoe UI"/>
                <Setter Property="FontSize" Value="14"/>
            </Style>
        </ResourceDictionary>
    </Application.Resources>
</Application>
