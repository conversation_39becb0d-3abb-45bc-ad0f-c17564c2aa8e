using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Linq;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using EduTrackForWin.Data;
using EduTrackForWin.Models;
using EduTrackForWin.Services;
using Microsoft.EntityFrameworkCore;

namespace EduTrackForWin.Views
{
    /// <summary>
    /// Interaction logic for BulkPaymentWindow.xaml
    /// </summary>
    public partial class BulkPaymentWindow : Window
    {
        private readonly EduTrackDbContext _context;
        private readonly PaymentService _paymentService;
        private readonly GroupService _groupService;
        private ObservableCollection<StudentPaymentItem> _students;
        private List<Group> _groups;

        public BulkPaymentWindow()
        {
            try
            {
                InitializeComponent();
                _context = new EduTrackDbContext();
                _paymentService = new PaymentService();
                _groupService = new GroupService();
                _students = new ObservableCollection<StudentPaymentItem>();
                _groups = new List<Group>();

                DgStudents.ItemsSource = _students;
                Loaded += BulkPaymentWindow_Loaded;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تهيئة نافذة الدفع الجماعي: {ex.Message}", "خطأ", 
                              MessageBoxButton.OK, MessageBoxImage.Error);
                throw;
            }
        }

        private async void BulkPaymentWindow_Loaded(object sender, RoutedEventArgs e)
        {
            await LoadGroups();
        }

        private async Task LoadGroups()
        {
            try
            {
                _groups = await _groupService.GetAllGroupsAsync();
                
                if (CmbGroup != null)
                {
                    CmbGroup.ItemsSource = _groups;
                    if (_groups.Any())
                    {
                        CmbGroup.SelectedIndex = 0;
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل المجموعات: {ex.Message}", "خطأ", 
                              MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async void CmbGroup_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            if (CmbGroup.SelectedItem is Group selectedGroup)
            {
                await LoadStudents(selectedGroup.Id);
                
                // Set default amount to group fee
                if (TxtAmount != null && selectedGroup.MonthlyFee > 0)
                {
                    TxtAmount.Text = selectedGroup.MonthlyFee.ToString("F2");
                }
            }
        }

        private async Task LoadStudents(int groupId)
        {
            try
            {
                var students = await _context.Students
                    .Include(s => s.Group)
                    .Include(s => s.Payments)
                    .Where(s => s.GroupId == groupId)
                    .ToListAsync();

                _students.Clear();
                foreach (var student in students)
                {
                    var item = new StudentPaymentItem
                    {
                        Student = student,
                        IsSelected = false,
                        CustomAmount = student.Group?.MonthlyFee ?? 0
                    };
                    item.PropertyChanged += StudentItem_PropertyChanged;
                    _students.Add(item);
                }

                UpdateSummary();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل الطلاب: {ex.Message}", "خطأ", 
                              MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void StudentItem_PropertyChanged(object sender, PropertyChangedEventArgs e)
        {
            if (e.PropertyName == nameof(StudentPaymentItem.IsSelected) || 
                e.PropertyName == nameof(StudentPaymentItem.CustomAmount))
            {
                UpdateSummary();
            }
        }

        private void UpdateSummary()
        {
            try
            {
                var selectedStudents = _students.Where(s => s.IsSelected).ToList();
                var totalAmount = selectedStudents.Sum(s => s.CustomAmount);

                if (TxtSelectedCount != null)
                {
                    TxtSelectedCount.Text = selectedStudents.Count == 0 
                        ? "لم يتم تحديد أي طالب"
                        : $"تم تحديد {selectedStudents.Count} طالب";
                }

                if (TxtTotalAmount != null)
                {
                    TxtTotalAmount.Text = $"المجموع: {totalAmount:C}";
                }

                // Update select all checkbox
                if (ChkSelectAll != null)
                {
                    ChkSelectAll.IsChecked = _students.Any() && _students.All(s => s.IsSelected);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحديث الملخص: {ex.Message}", "خطأ", 
                              MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void ChkSelectAll_Checked(object sender, RoutedEventArgs e)
        {
            foreach (var student in _students)
            {
                student.IsSelected = true;
            }
        }

        private void ChkSelectAll_Unchecked(object sender, RoutedEventArgs e)
        {
            foreach (var student in _students)
            {
                student.IsSelected = false;
            }
        }

        private void StudentCheckBox_Changed(object sender, RoutedEventArgs e)
        {
            UpdateSummary();
        }

        private void CustomAmount_TextChanged(object sender, TextChangedEventArgs e)
        {
            UpdateSummary();
        }

        private void BtnUseGroupFee_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (CmbGroup.SelectedItem is Group selectedGroup && TxtAmount != null)
                {
                    TxtAmount.Text = selectedGroup.MonthlyFee.ToString("F2");
                    
                    // Update all custom amounts
                    foreach (var student in _students)
                    {
                        student.CustomAmount = selectedGroup.MonthlyFee;
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تعيين رسوم المجموعة: {ex.Message}", "خطأ", 
                              MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async void BtnProcessPayments_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (!ValidateInput())
                    return;

                var selectedStudents = _students.Where(s => s.IsSelected).ToList();
                
                if (!selectedStudents.Any())
                {
                    MessageBox.Show("يرجى تحديد طالب واحد على الأقل", "تنبيه", 
                                  MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }

                var result = MessageBox.Show(
                    $"هل أنت متأكد من معالجة {selectedStudents.Count} دفعة بإجمالي {selectedStudents.Sum(s => s.CustomAmount):C}؟",
                    "تأكيد الدفع الجماعي", 
                    MessageBoxButton.YesNo, 
                    MessageBoxImage.Question);

                if (result != MessageBoxResult.Yes)
                    return;

                var paymentType = GetSelectedPaymentType();
                var paymentMethod = ((ComboBoxItem)CmbPaymentMethod.SelectedItem).Content.ToString();
                var successCount = 0;
                var errors = new List<string>();

                foreach (var studentItem in selectedStudents)
                {
                    try
                    {
                        var payment = new Payment
                        {
                            StudentId = studentItem.Student.Id,
                            Type = paymentType,
                            Amount = studentItem.CustomAmount,
                            PaymentDate = DateTime.Now,
                            PaymentMethod = paymentMethod,
                            Notes = $"دفع جماعي - {DateTime.Now:yyyy/MM/dd}",
                            CreatedAt = DateTime.Now
                        };

                        await _paymentService.AddPaymentAsync(payment);
                        successCount++;
                    }
                    catch (Exception ex)
                    {
                        errors.Add($"خطأ في دفعة {studentItem.Student.Name}: {ex.Message}");
                    }
                }

                var message = $"تم معالجة {successCount} دفعة بنجاح";
                if (errors.Any())
                {
                    message += $"\n\nأخطاء ({errors.Count}):\n" + string.Join("\n", errors.Take(5));
                    if (errors.Count > 5)
                    {
                        message += $"\n... و {errors.Count - 5} أخطاء أخرى";
                    }
                }

                MessageBox.Show(message, "نتيجة الدفع الجماعي", 
                              MessageBoxButton.OK, 
                              errors.Any() ? MessageBoxImage.Warning : MessageBoxImage.Information);

                if (successCount > 0)
                {
                    DialogResult = true;
                    Close();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في معالجة المدفوعات: {ex.Message}", "خطأ", 
                              MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private PaymentType GetSelectedPaymentType()
        {
            var selectedType = ((ComboBoxItem)CmbPaymentType.SelectedItem).Content.ToString();
            return selectedType switch
            {
                "اشتراك شهري" => PaymentType.Subscription,
                "رسوم تسجيل" => PaymentType.Other,
                "رسوم إضافية" => PaymentType.Other,
                _ => PaymentType.Subscription
            };
        }

        private bool ValidateInput()
        {
            try
            {
                if (CmbGroup?.SelectedItem == null)
                {
                    MessageBox.Show("يرجى اختيار المجموعة", "خطأ في البيانات", 
                                  MessageBoxButton.OK, MessageBoxImage.Warning);
                    return false;
                }

                if (CmbPaymentType?.SelectedItem == null)
                {
                    MessageBox.Show("يرجى اختيار نوع الدفعة", "خطأ في البيانات", 
                                  MessageBoxButton.OK, MessageBoxImage.Warning);
                    return false;
                }

                if (CmbPaymentMethod?.SelectedItem == null)
                {
                    MessageBox.Show("يرجى اختيار طريقة الدفع", "خطأ في البيانات", 
                                  MessageBoxButton.OK, MessageBoxImage.Warning);
                    return false;
                }

                return true;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في التحقق من البيانات: {ex.Message}", "خطأ", 
                              MessageBoxButton.OK, MessageBoxImage.Error);
                return false;
            }
        }

        private void BtnCancel_Click(object sender, RoutedEventArgs e)
        {
            DialogResult = false;
            Close();
        }
    }

    // Helper class for student payment items
    public class StudentPaymentItem : INotifyPropertyChanged
    {
        private bool _isSelected;
        private decimal _customAmount;

        public Student Student { get; set; }

        public bool IsSelected
        {
            get => _isSelected;
            set
            {
                _isSelected = value;
                OnPropertyChanged();
            }
        }

        public decimal CustomAmount
        {
            get => _customAmount;
            set
            {
                _customAmount = value;
                OnPropertyChanged();
            }
        }

        // Delegate properties for binding
        public string Name => Student?.Name ?? "";
        public string Phone => Student?.Phone ?? "";
        public decimal TotalDue => Student?.TotalDue ?? 0;
        public string PaymentStatus => Student?.PaymentStatus ?? "";

        public event PropertyChangedEventHandler PropertyChanged;

        protected virtual void OnPropertyChanged([System.Runtime.CompilerServices.CallerMemberName] string propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
    }
}
