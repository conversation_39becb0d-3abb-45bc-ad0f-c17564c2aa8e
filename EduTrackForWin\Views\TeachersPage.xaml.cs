using System.Collections.ObjectModel;
using System.Windows;
using System.Windows.Controls;
using EduTrackForWin.Models;
using EduTrackForWin.Data;
using Microsoft.EntityFrameworkCore;

namespace EduTrackForWin.Views
{
    public partial class TeachersPage : Page
    {
        private readonly EduTrackDbContext _context;
        private ObservableCollection<Teacher> _teachers;
        private List<Teacher> _allTeachers;

        public TeachersPage()
        {
            InitializeComponent();
            _context = new EduTrackDbContext();
            _teachers = new ObservableCollection<Teacher>();
            _allTeachers = new List<Teacher>();

            DgTeachers.ItemsSource = _teachers;
            LoadTeachers();
        }

        private async void LoadTeachers()
        {
            try
            {
                _allTeachers = await _context.Teachers
                    .Include(t => t.Groups)
                    .Include(t => t.Sessions)
                    .OrderBy(t => t.Name)
                    .ToListAsync();

                RefreshTeachersList();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل بيانات المعلمين: {ex.Message}", "خطأ",
                              MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void RefreshTeachersList()
        {
            _teachers.Clear();

            var filteredTeachers = _allTeachers.AsEnumerable();

            // Apply search filter
            if (!string.IsNullOrWhiteSpace(TxtSearch.Text))
            {
                var searchTerm = TxtSearch.Text.ToLower();
                filteredTeachers = filteredTeachers.Where(t =>
                    t.Name.ToLower().Contains(searchTerm) ||
                    t.Phone.Contains(searchTerm) ||
                    t.Email.ToLower().Contains(searchTerm) ||
                    t.Specialization.ToLower().Contains(searchTerm));
            }

            // Apply specialization filter
            if (CmbSpecializationFilter.SelectedIndex > 0)
            {
                var selectedSpecialization = ((ComboBoxItem)CmbSpecializationFilter.SelectedItem).Content.ToString();
                filteredTeachers = filteredTeachers.Where(t => t.Specialization == selectedSpecialization);
            }

            foreach (var teacher in filteredTeachers)
            {
                _teachers.Add(teacher);
            }

            // Show/hide no data message
            TxtNoData.Visibility = _teachers.Count == 0 ? Visibility.Visible : Visibility.Collapsed;
            DgTeachers.Visibility = _teachers.Count == 0 ? Visibility.Collapsed : Visibility.Visible;
        }

        private void TxtSearch_TextChanged(object sender, TextChangedEventArgs e)
        {
            RefreshTeachersList();
        }

        private void CmbSpecializationFilter_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            if (IsLoaded)
                RefreshTeachersList();
        }

        private void BtnRefresh_Click(object sender, RoutedEventArgs e)
        {
            LoadTeachers();
        }

        private void BtnAddTeacher_Click(object sender, RoutedEventArgs e)
        {
            var addTeacherWindow = new AddEditTeacherWindow();
            if (addTeacherWindow.ShowDialog() == true)
            {
                LoadTeachers(); // Refresh the list
            }
        }

        private void BtnViewTeacher_Click(object sender, RoutedEventArgs e)
        {
            if (sender is Button button && button.Tag is int teacherId)
            {
                var teacher = _allTeachers.FirstOrDefault(t => t.Id == teacherId);
                if (teacher != null)
                {
                    var viewWindow = new TeacherDetailsWindow(teacher);
                    viewWindow.ShowDialog();
                }
            }
        }

        private void BtnEditTeacher_Click(object sender, RoutedEventArgs e)
        {
            if (sender is Button button && button.Tag is int teacherId)
            {
                var teacher = _allTeachers.FirstOrDefault(t => t.Id == teacherId);
                if (teacher != null)
                {
                    var editWindow = new AddEditTeacherWindow(teacher);
                    if (editWindow.ShowDialog() == true)
                    {
                        LoadTeachers(); // Refresh the list
                    }
                }
            }
        }

        private async void BtnDeleteTeacher_Click(object sender, RoutedEventArgs e)
        {
            if (sender is Button button && button.Tag is int teacherId)
            {
                var teacher = _allTeachers.FirstOrDefault(t => t.Id == teacherId);
                if (teacher != null)
                {
                    // Check if teacher has groups
                    if (teacher.Groups != null && teacher.Groups.Any())
                    {
                        MessageBox.Show(
                            $"لا يمكن حذف المعلم '{teacher.Name}' لأنه مرتبط بمجموعات دراسية.\nيرجى إلغاء ربط المعلم من المجموعات أولاً.",
                            "تعذر الحذف",
                            MessageBoxButton.OK,
                            MessageBoxImage.Warning);
                        return;
                    }

                    var result = MessageBox.Show(
                        $"هل أنت متأكد من حذف المعلم '{teacher.Name}'؟\nسيتم حذف جميع البيانات المرتبطة به.",
                        "تأكيد الحذف",
                        MessageBoxButton.YesNo,
                        MessageBoxImage.Warning);

                    if (result == MessageBoxResult.Yes)
                    {
                        try
                        {
                            _context.Teachers.Remove(teacher);
                            await _context.SaveChangesAsync();
                            LoadTeachers(); // Refresh the list
                            MessageBox.Show("تم حذف المعلم بنجاح", "نجح",
                                          MessageBoxButton.OK, MessageBoxImage.Information);
                        }
                        catch (Exception ex)
                        {
                            MessageBox.Show($"خطأ في حذف المعلم: {ex.Message}", "خطأ",
                                          MessageBoxButton.OK, MessageBoxImage.Error);
                        }
                    }
                }
            }
        }

        private void Page_Unloaded(object sender, RoutedEventArgs e)
        {
            _context?.Dispose();
        }
    }
}
