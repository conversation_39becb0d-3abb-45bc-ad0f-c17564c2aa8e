﻿#pragma checksum "..\..\..\..\Views\AttendancePage.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "C0B927C1D1800195FA3EA0DD95C691286F922123"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using EduTrackForWin.Views;
using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace EduTrackForWin.Views {
    
    
    /// <summary>
    /// AttendancePage
    /// </summary>
    public partial class AttendancePage : System.Windows.Controls.Page, System.Windows.Markup.IComponentConnector {
        
        
        #line 89 "..\..\..\..\Views\AttendancePage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BtnTakeAttendance;
        
        #line default
        #line hidden
        
        
        #line 108 "..\..\..\..\Views\AttendancePage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TxtTodayAttendance;
        
        #line default
        #line hidden
        
        
        #line 116 "..\..\..\..\Views\AttendancePage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TxtWeekAttendance;
        
        #line default
        #line hidden
        
        
        #line 124 "..\..\..\..\Views\AttendancePage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TxtMonthAttendance;
        
        #line default
        #line hidden
        
        
        #line 132 "..\..\..\..\Views\AttendancePage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TxtAbsentStudents;
        
        #line default
        #line hidden
        
        
        #line 158 "..\..\..\..\Views\AttendancePage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DatePicker DpFilterDate;
        
        #line default
        #line hidden
        
        
        #line 160 "..\..\..\..\Views\AttendancePage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BtnFilter;
        
        #line default
        #line hidden
        
        
        #line 165 "..\..\..\..\Views\AttendancePage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DataGrid DgAttendance;
        
        #line default
        #line hidden
        
        
        #line 219 "..\..\..\..\Views\AttendancePage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BtnTodaySessions;
        
        #line default
        #line hidden
        
        
        #line 224 "..\..\..\..\Views\AttendancePage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BtnAbsentStudents;
        
        #line default
        #line hidden
        
        
        #line 229 "..\..\..\..\Views\AttendancePage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BtnAttendanceReport;
        
        #line default
        #line hidden
        
        
        #line 234 "..\..\..\..\Views\AttendancePage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BtnLateStudents;
        
        #line default
        #line hidden
        
        
        #line 258 "..\..\..\..\Views\AttendancePage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TxtTodaySessionsCount;
        
        #line default
        #line hidden
        
        
        #line 263 "..\..\..\..\Views\AttendancePage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TxtPresentStudents;
        
        #line default
        #line hidden
        
        
        #line 268 "..\..\..\..\Views\AttendancePage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TxtAbsentStudentsCount;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/EduTrackForWin;component/views/attendancepage.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\Views\AttendancePage.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.BtnTakeAttendance = ((System.Windows.Controls.Button)(target));
            
            #line 91 "..\..\..\..\Views\AttendancePage.xaml"
            this.BtnTakeAttendance.Click += new System.Windows.RoutedEventHandler(this.BtnTakeAttendance_Click);
            
            #line default
            #line hidden
            return;
            case 2:
            this.TxtTodayAttendance = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 3:
            this.TxtWeekAttendance = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 4:
            this.TxtMonthAttendance = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 5:
            this.TxtAbsentStudents = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 6:
            this.DpFilterDate = ((System.Windows.Controls.DatePicker)(target));
            return;
            case 7:
            this.BtnFilter = ((System.Windows.Controls.Button)(target));
            
            #line 161 "..\..\..\..\Views\AttendancePage.xaml"
            this.BtnFilter.Click += new System.Windows.RoutedEventHandler(this.BtnFilter_Click);
            
            #line default
            #line hidden
            return;
            case 8:
            this.DgAttendance = ((System.Windows.Controls.DataGrid)(target));
            return;
            case 9:
            this.BtnTodaySessions = ((System.Windows.Controls.Button)(target));
            
            #line 222 "..\..\..\..\Views\AttendancePage.xaml"
            this.BtnTodaySessions.Click += new System.Windows.RoutedEventHandler(this.BtnTodaySessions_Click);
            
            #line default
            #line hidden
            return;
            case 10:
            this.BtnAbsentStudents = ((System.Windows.Controls.Button)(target));
            
            #line 227 "..\..\..\..\Views\AttendancePage.xaml"
            this.BtnAbsentStudents.Click += new System.Windows.RoutedEventHandler(this.BtnAbsentStudents_Click);
            
            #line default
            #line hidden
            return;
            case 11:
            this.BtnAttendanceReport = ((System.Windows.Controls.Button)(target));
            
            #line 232 "..\..\..\..\Views\AttendancePage.xaml"
            this.BtnAttendanceReport.Click += new System.Windows.RoutedEventHandler(this.BtnAttendanceReport_Click);
            
            #line default
            #line hidden
            return;
            case 12:
            this.BtnLateStudents = ((System.Windows.Controls.Button)(target));
            
            #line 238 "..\..\..\..\Views\AttendancePage.xaml"
            this.BtnLateStudents.Click += new System.Windows.RoutedEventHandler(this.BtnLateStudents_Click);
            
            #line default
            #line hidden
            return;
            case 13:
            this.TxtTodaySessionsCount = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 14:
            this.TxtPresentStudents = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 15:
            this.TxtAbsentStudentsCount = ((System.Windows.Controls.TextBlock)(target));
            return;
            }
            this._contentLoaded = true;
        }
    }
}

