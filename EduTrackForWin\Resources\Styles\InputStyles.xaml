<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">

    <!-- Modern TextBox Style -->
    <Style x:Key="ModernTextBoxStyle" TargetType="TextBox">
        <Setter Property="Background" Value="{StaticResource SurfaceColor}"/>
        <Setter Property="Foreground" Value="{StaticResource TextPrimaryColor}"/>
        <Setter Property="BorderBrush" Value="{StaticResource BorderColor}"/>
        <Setter Property="BorderThickness" Value="2"/>
        <Setter Property="Padding" Value="16,12"/>
        <Setter Property="Margin" Value="0,8"/>
        <Setter Property="FontSize" Value="14"/>
        <Setter Property="FontFamily" Value="Segoe UI"/>
        <Setter Property="Height" Value="48"/>
        <Setter Property="VerticalContentAlignment" Value="Center"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="TextBox">
                    <Border Name="Border"
                          Background="{TemplateBinding Background}"
                          BorderBrush="{TemplateBinding BorderBrush}"
                          BorderThickness="{TemplateBinding BorderThickness}"
                          CornerRadius="8">
                        <ScrollViewer Name="PART_ContentHost"
                                    Margin="{TemplateBinding Padding}"
                                    VerticalAlignment="{TemplateBinding VerticalContentAlignment}"/>
                    </Border>
                    <ControlTemplate.Triggers>
                        <Trigger Property="IsFocused" Value="True">
                            <Setter TargetName="Border" Property="BorderBrush" Value="{StaticResource PrimaryColor}"/>
                            <Trigger.EnterActions>
                                <BeginStoryboard>
                                    <Storyboard>
                                        <DoubleAnimation Storyboard.TargetName="Border"
                                                       Storyboard.TargetProperty="(Border.RenderTransform).(ScaleTransform.ScaleX)"
                                                       To="1.02" Duration="0:0:0.2"/>
                                        <DoubleAnimation Storyboard.TargetName="Border"
                                                       Storyboard.TargetProperty="(Border.RenderTransform).(ScaleTransform.ScaleY)"
                                                       To="1.02" Duration="0:0:0.2"/>
                                    </Storyboard>
                                </BeginStoryboard>
                            </Trigger.EnterActions>
                            <Trigger.ExitActions>
                                <BeginStoryboard>
                                    <Storyboard>
                                        <DoubleAnimation Storyboard.TargetName="Border"
                                                       Storyboard.TargetProperty="(Border.RenderTransform).(ScaleTransform.ScaleX)"
                                                       To="1" Duration="0:0:0.2"/>
                                        <DoubleAnimation Storyboard.TargetName="Border"
                                                       Storyboard.TargetProperty="(Border.RenderTransform).(ScaleTransform.ScaleY)"
                                                       To="1" Duration="0:0:0.2"/>
                                    </Storyboard>
                                </BeginStoryboard>
                            </Trigger.ExitActions>
                        </Trigger>
                        <Trigger Property="IsMouseOver" Value="True">
                            <Setter TargetName="Border" Property="BorderBrush" Value="{StaticResource PrimaryLightColor}"/>
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
        <Setter Property="RenderTransform">
            <Setter.Value>
                <ScaleTransform/>
            </Setter.Value>
        </Setter>
    </Style>

    <!-- Floating Label TextBox Style -->
    <Style x:Key="FloatingLabelTextBoxStyle" TargetType="TextBox">
        <Setter Property="Background" Value="Transparent"/>
        <Setter Property="Foreground" Value="{StaticResource TextPrimaryColor}"/>
        <Setter Property="BorderBrush" Value="{StaticResource BorderColor}"/>
        <Setter Property="BorderThickness" Value="0,0,0,2"/>
        <Setter Property="Padding" Value="0,20,0,8"/>
        <Setter Property="Margin" Value="0,16"/>
        <Setter Property="FontSize" Value="16"/>
        <Setter Property="FontFamily" Value="Segoe UI"/>
        <Setter Property="Height" Value="56"/>
        <Setter Property="VerticalContentAlignment" Value="Bottom"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="TextBox">
                    <Grid>
                        <Border Name="Border"
                              Background="{TemplateBinding Background}"
                              BorderBrush="{TemplateBinding BorderBrush}"
                              BorderThickness="{TemplateBinding BorderThickness}">
                            <ScrollViewer Name="PART_ContentHost"
                                        Margin="{TemplateBinding Padding}"
                                        VerticalAlignment="{TemplateBinding VerticalContentAlignment}"/>
                        </Border>
                        <TextBlock Name="PlaceholderText"
                                 Text="{TemplateBinding Tag}"
                                 Foreground="{StaticResource TextHintColor}"
                                 FontSize="16"
                                 Margin="0,20,0,8"
                                 VerticalAlignment="Bottom"
                                 IsHitTestVisible="False">
                            <TextBlock.RenderTransform>
                                <TransformGroup>
                                    <ScaleTransform/>
                                    <TranslateTransform/>
                                </TransformGroup>
                            </TextBlock.RenderTransform>
                        </TextBlock>
                    </Grid>
                    <ControlTemplate.Triggers>
                        <Trigger Property="IsFocused" Value="True">
                            <Setter TargetName="Border" Property="BorderBrush" Value="{StaticResource PrimaryColor}"/>
                            <Trigger.EnterActions>
                                <BeginStoryboard>
                                    <Storyboard>
                                        <DoubleAnimation Storyboard.TargetName="PlaceholderText"
                                                       Storyboard.TargetProperty="(TextBlock.RenderTransform).(TransformGroup.Children)[0].(ScaleTransform.ScaleX)"
                                                       To="0.8" Duration="0:0:0.2"/>
                                        <DoubleAnimation Storyboard.TargetName="PlaceholderText"
                                                       Storyboard.TargetProperty="(TextBlock.RenderTransform).(TransformGroup.Children)[0].(ScaleTransform.ScaleY)"
                                                       To="0.8" Duration="0:0:0.2"/>
                                        <DoubleAnimation Storyboard.TargetName="PlaceholderText"
                                                       Storyboard.TargetProperty="(TextBlock.RenderTransform).(TransformGroup.Children)[1].(TranslateTransform.Y)"
                                                       To="-20" Duration="0:0:0.2"/>
                                        <ColorAnimation Storyboard.TargetName="PlaceholderText"
                                                      Storyboard.TargetProperty="Foreground.Color"
                                                      To="{Binding Source={StaticResource PrimaryColor}, Path=Color}"
                                                      Duration="0:0:0.2"/>
                                    </Storyboard>
                                </BeginStoryboard>
                            </Trigger.EnterActions>
                        </Trigger>
                        <MultiTrigger>
                            <MultiTrigger.Conditions>
                                <Condition Property="IsFocused" Value="False"/>
                                <Condition Property="Text" Value=""/>
                            </MultiTrigger.Conditions>
                            <MultiTrigger.EnterActions>
                                <BeginStoryboard>
                                    <Storyboard>
                                        <DoubleAnimation Storyboard.TargetName="PlaceholderText"
                                                       Storyboard.TargetProperty="(TextBlock.RenderTransform).(TransformGroup.Children)[0].(ScaleTransform.ScaleX)"
                                                       To="1" Duration="0:0:0.2"/>
                                        <DoubleAnimation Storyboard.TargetName="PlaceholderText"
                                                       Storyboard.TargetProperty="(TextBlock.RenderTransform).(TransformGroup.Children)[0].(ScaleTransform.ScaleY)"
                                                       To="1" Duration="0:0:0.2"/>
                                        <DoubleAnimation Storyboard.TargetName="PlaceholderText"
                                                       Storyboard.TargetProperty="(TextBlock.RenderTransform).(TransformGroup.Children)[1].(TranslateTransform.Y)"
                                                       To="0" Duration="0:0:0.2"/>
                                        <ColorAnimation Storyboard.TargetName="PlaceholderText"
                                                      Storyboard.TargetProperty="Foreground.Color"
                                                      To="{Binding Source={StaticResource TextHintColor}, Path=Color}"
                                                      Duration="0:0:0.2"/>
                                    </Storyboard>
                                </BeginStoryboard>
                            </MultiTrigger.EnterActions>
                        </MultiTrigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <!-- Modern ComboBox Style -->
    <Style x:Key="ModernComboBoxStyle" TargetType="ComboBox">
        <Setter Property="Background" Value="{StaticResource SurfaceColor}"/>
        <Setter Property="Foreground" Value="{StaticResource TextPrimaryColor}"/>
        <Setter Property="BorderBrush" Value="{StaticResource BorderColor}"/>
        <Setter Property="BorderThickness" Value="2"/>
        <Setter Property="Padding" Value="16,12"/>
        <Setter Property="Margin" Value="0,8"/>
        <Setter Property="FontSize" Value="14"/>
        <Setter Property="FontFamily" Value="Segoe UI"/>
        <Setter Property="Height" Value="48"/>
        <Setter Property="VerticalContentAlignment" Value="Center"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="ComboBox">
                    <Grid>
                        <Border Name="Border"
                              Background="{TemplateBinding Background}"
                              BorderBrush="{TemplateBinding BorderBrush}"
                              BorderThickness="{TemplateBinding BorderThickness}"
                              CornerRadius="8">
                            <Grid>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="Auto"/>
                                </Grid.ColumnDefinitions>
                                <ContentPresenter Name="ContentSite"
                                                Grid.Column="0"
                                                IsHitTestVisible="False"
                                                Content="{TemplateBinding SelectionBoxItem}"
                                                ContentTemplate="{TemplateBinding SelectionBoxItemTemplate}"
                                                ContentTemplateSelector="{TemplateBinding ItemTemplateSelector}"
                                                Margin="{TemplateBinding Padding}"
                                                VerticalAlignment="{TemplateBinding VerticalContentAlignment}"
                                                HorizontalAlignment="{TemplateBinding HorizontalContentAlignment}"/>
                                <ToggleButton Name="ToggleButton"
                                            Grid.Column="1"
                                            Focusable="False"
                                            IsChecked="{Binding Path=IsDropDownOpen, Mode=TwoWay, RelativeSource={RelativeSource TemplatedParent}}"
                                            ClickMode="Press"
                                            Background="Transparent"
                                            BorderThickness="0"
                                            Width="30">
                                    <Path Name="Arrow"
                                        Fill="{StaticResource TextSecondaryColor}"
                                        HorizontalAlignment="Center"
                                        VerticalAlignment="Center"
                                        Data="M 0 0 L 4 4 L 8 0 Z">
                                        <Path.RenderTransform>
                                            <RotateTransform/>
                                        </Path.RenderTransform>
                                    </Path>
                                </ToggleButton>
                            </Grid>
                        </Border>
                        <Popup Name="Popup"
                             Placement="Bottom"
                             IsOpen="{TemplateBinding IsDropDownOpen}"
                             AllowsTransparency="True"
                             Focusable="False"
                             PopupAnimation="Slide">
                            <Grid Name="DropDown"
                                SnapsToDevicePixels="True"
                                MinWidth="{TemplateBinding ActualWidth}"
                                MaxHeight="{TemplateBinding MaxDropDownHeight}">
                                <Border Name="DropDownBorder"
                                      Background="{StaticResource SurfaceColor}"
                                      BorderBrush="{StaticResource BorderColor}"
                                      BorderThickness="1"
                                      CornerRadius="8"
                                      Margin="0,4,0,0">
                                    <Border.Effect>
                                        <DropShadowEffect Color="#000000" Direction="270" ShadowDepth="4" 
                                                        Opacity="0.2" BlurRadius="12"/>
                                    </Border.Effect>
                                    <ScrollViewer Margin="4,6,4,6" SnapsToDevicePixels="True">
                                        <StackPanel IsItemsHost="True" KeyboardNavigation.DirectionalNavigation="Contained"/>
                                    </ScrollViewer>
                                </Border>
                            </Grid>
                        </Popup>
                    </Grid>
                    <ControlTemplate.Triggers>
                        <Trigger Property="IsFocused" Value="True">
                            <Setter TargetName="Border" Property="BorderBrush" Value="{StaticResource PrimaryColor}"/>
                        </Trigger>
                        <Trigger Property="IsMouseOver" Value="True">
                            <Setter TargetName="Border" Property="BorderBrush" Value="{StaticResource PrimaryLightColor}"/>
                        </Trigger>
                        <Trigger Property="IsDropDownOpen" Value="True">
                            <Trigger.EnterActions>
                                <BeginStoryboard>
                                    <Storyboard>
                                        <DoubleAnimation Storyboard.TargetName="Arrow"
                                                       Storyboard.TargetProperty="(Path.RenderTransform).(RotateTransform.Angle)"
                                                       To="180" Duration="0:0:0.2"/>
                                    </Storyboard>
                                </BeginStoryboard>
                            </Trigger.EnterActions>
                            <Trigger.ExitActions>
                                <BeginStoryboard>
                                    <Storyboard>
                                        <DoubleAnimation Storyboard.TargetName="Arrow"
                                                       Storyboard.TargetProperty="(Path.RenderTransform).(RotateTransform.Angle)"
                                                       To="0" Duration="0:0:0.2"/>
                                    </Storyboard>
                                </BeginStoryboard>
                            </Trigger.ExitActions>
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <!-- Modern DatePicker Style -->
    <Style x:Key="ModernDatePickerStyle" TargetType="DatePicker">
        <Setter Property="Background" Value="{StaticResource SurfaceColor}"/>
        <Setter Property="Foreground" Value="{StaticResource TextPrimaryColor}"/>
        <Setter Property="BorderBrush" Value="{StaticResource BorderColor}"/>
        <Setter Property="BorderThickness" Value="2"/>
        <Setter Property="Padding" Value="16,12"/>
        <Setter Property="Margin" Value="0,8"/>
        <Setter Property="FontSize" Value="14"/>
        <Setter Property="FontFamily" Value="Segoe UI"/>
        <Setter Property="Height" Value="48"/>
        <Setter Property="VerticalContentAlignment" Value="Center"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="DatePicker">
                    <Border Name="Border"
                          Background="{TemplateBinding Background}"
                          BorderBrush="{TemplateBinding BorderBrush}"
                          BorderThickness="{TemplateBinding BorderThickness}"
                          CornerRadius="8">
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="Auto"/>
                            </Grid.ColumnDefinitions>
                            <DatePickerTextBox Name="PART_TextBox"
                                             Grid.Column="0"
                                             Margin="{TemplateBinding Padding}"
                                             VerticalAlignment="{TemplateBinding VerticalContentAlignment}"
                                             Background="Transparent"
                                             BorderThickness="0"
                                             Focusable="{TemplateBinding Focusable}"/>
                            <Button Name="PART_Button"
                                  Grid.Column="1"
                                  Background="Transparent"
                                  BorderThickness="0"
                                  Width="30"
                                  Focusable="False">
                                <Path Fill="{StaticResource TextSecondaryColor}"
                                    Data="M19,3H5C3.89,3 3,3.89 3,5V19A2,2 0 0,0 5,21H19A2,2 0 0,0 21,19V5C21,3.89 20.1,3 19,3M16,1V3H8V1H6V3H5A2,2 0 0,0 3,5V19A2,2 0 0,0 5,21H19A2,2 0 0,0 21,19V5A2,2 0 0,0 19,3H18V1M19,19H5V8H19V19Z"
                                    Stretch="Uniform"
                                    Width="16" Height="16"/>
                            </Button>
                        </Grid>
                    </Border>
                    <ControlTemplate.Triggers>
                        <Trigger Property="IsFocused" Value="True">
                            <Setter TargetName="Border" Property="BorderBrush" Value="{StaticResource PrimaryColor}"/>
                        </Trigger>
                        <Trigger Property="IsMouseOver" Value="True">
                            <Setter TargetName="Border" Property="BorderBrush" Value="{StaticResource PrimaryLightColor}"/>
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <!-- Search TextBox Style -->
    <Style x:Key="SearchTextBoxStyle" TargetType="TextBox">
        <Setter Property="Background" Value="{StaticResource SurfaceColor}"/>
        <Setter Property="Foreground" Value="{StaticResource TextPrimaryColor}"/>
        <Setter Property="BorderBrush" Value="{StaticResource BorderColor}"/>
        <Setter Property="BorderThickness" Value="1"/>
        <Setter Property="Padding" Value="40,12,16,12"/>
        <Setter Property="Margin" Value="0,8"/>
        <Setter Property="FontSize" Value="14"/>
        <Setter Property="FontFamily" Value="Segoe UI"/>
        <Setter Property="Height" Value="44"/>
        <Setter Property="VerticalContentAlignment" Value="Center"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="TextBox">
                    <Border Name="Border"
                          Background="{TemplateBinding Background}"
                          BorderBrush="{TemplateBinding BorderBrush}"
                          BorderThickness="{TemplateBinding BorderThickness}"
                          CornerRadius="22">
                        <Grid>
                            <Path Name="SearchIcon"
                                Fill="{StaticResource TextHintColor}"
                                Data="M9.5,3A6.5,6.5 0 0,1 16,9.5C16,11.11 15.41,12.59 14.44,13.73L14.71,14H15.5L20.5,19L19,20.5L14,15.5V14.71L13.73,14.44C12.59,15.41 11.11,16 9.5,16A6.5,6.5 0 0,1 3,9.5A6.5,6.5 0 0,1 9.5,3M9.5,5C7,5 5,7 5,9.5C5,12 7,14 9.5,14C12,14 14,12 14,9.5C14,7 12,5 9.5,5Z"
                                Stretch="Uniform"
                                Width="16" Height="16"
                                HorizontalAlignment="Left"
                                VerticalAlignment="Center"
                                Margin="16,0,0,0"/>
                            <ScrollViewer Name="PART_ContentHost"
                                        Margin="{TemplateBinding Padding}"
                                        VerticalAlignment="{TemplateBinding VerticalContentAlignment}"/>
                        </Grid>
                    </Border>
                    <ControlTemplate.Triggers>
                        <Trigger Property="IsFocused" Value="True">
                            <Setter TargetName="Border" Property="BorderBrush" Value="{StaticResource PrimaryColor}"/>
                            <Setter TargetName="SearchIcon" Property="Fill" Value="{StaticResource PrimaryColor}"/>
                        </Trigger>
                        <Trigger Property="IsMouseOver" Value="True">
                            <Setter TargetName="Border" Property="BorderBrush" Value="{StaticResource PrimaryLightColor}"/>
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

</ResourceDictionary>
