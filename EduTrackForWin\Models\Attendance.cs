using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace EduTrackForWin.Models
{
    public class Attendance
    {
        [Key]
        public int Id { get; set; }

        public DateTime Date { get; set; }

        public bool IsPresent { get; set; }

        [StringLength(200)]
        public string Notes { get; set; } = string.Empty;

        public DateTime RecordedAt { get; set; } = DateTime.Now;

        // Foreign Keys
        public int StudentId { get; set; }
        public int SessionId { get; set; }

        // Navigation Properties
        [ForeignKey("StudentId")]
        public virtual Student Student { get; set; } = null!;

        [ForeignKey("SessionId")]
        public virtual Session Session { get; set; } = null!;

        // Computed Properties
        [NotMapped]
        public string Status => IsPresent ? "حاضر" : "غائب";

        [NotMapped]
        public string StudentName => Student?.Name ?? "";

        [NotMapped]
        public string GroupName => Session?.Group?.Name ?? "";
    }
}
