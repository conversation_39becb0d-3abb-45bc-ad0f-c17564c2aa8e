using Microsoft.EntityFrameworkCore;
using EduTrackForWin.Data;
using EduTrackForWin.Models;

namespace EduTrackForWin.Services
{
    public class SessionService
    {
        private readonly EduTrackDbContext _context;

        public SessionService()
        {
            _context = new EduTrackDbContext();
        }

        public async Task<List<Session>> GetAllSessionsAsync()
        {
            var sessions = await _context.Sessions
                .Include(s => s.Group)
                .ThenInclude(g => g.Teacher)
                .Include(s => s.Teacher)
                .Include(s => s.Attendances)
                .ThenInclude(a => a.Student)
                .OrderByDescending(s => s.Date)
                .ToListAsync();

            // ترتيب على العميل لتجنب مشكلة TimeSpan في SQLite
            return sessions
                .OrderByDescending(s => s.Date)
                .ThenBy(s => s.StartTime)
                .ToList();
        }

        public async Task<Session?> GetSessionByIdAsync(int id)
        {
            return await _context.Sessions
                .Include(s => s.Group)
                .ThenInclude(g => g.Teacher)
                .Include(s => s.Group)
                .ThenInclude(g => g.Students)
                .Include(s => s.Teacher)
                .Include(s => s.Attendances)
                .ThenInclude(a => a.Student)
                .FirstOrDefaultAsync(s => s.Id == id);
        }

        public async Task<List<Session>> GetSessionsByDateAsync(DateTime date)
        {
            var sessions = await _context.Sessions
                .Include(s => s.Group)
                .ThenInclude(g => g.Teacher)
                .Include(s => s.Teacher)
                .Where(s => s.Date.Date == date.Date)
                .ToListAsync();

            // ترتيب على العميل
            return sessions
                .OrderBy(s => s.StartTime)
                .ToList();
        }

        public async Task<List<Session>> GetSessionsByGroupAsync(int groupId)
        {
            var sessions = await _context.Sessions
                .Include(s => s.Teacher)
                .Include(s => s.Attendances)
                .ThenInclude(a => a.Student)
                .Where(s => s.GroupId == groupId)
                .OrderByDescending(s => s.Date)
                .ToListAsync();

            // ترتيب على العميل
            return sessions
                .OrderByDescending(s => s.Date)
                .ThenBy(s => s.StartTime)
                .ToList();
        }

        public async Task<List<Session>> GetSessionsByTeacherAsync(int teacherId)
        {
            var sessions = await _context.Sessions
                .Include(s => s.Group)
                .Include(s => s.Attendances)
                .ThenInclude(a => a.Student)
                .Where(s => s.TeacherId == teacherId)
                .OrderByDescending(s => s.Date)
                .ToListAsync();

            // ترتيب على العميل
            return sessions
                .OrderByDescending(s => s.Date)
                .ThenBy(s => s.StartTime)
                .ToList();
        }

        public async Task<List<Session>> GetSessionsByDateRangeAsync(DateTime startDate, DateTime endDate)
        {
            var sessions = await _context.Sessions
                .Include(s => s.Group)
                .ThenInclude(g => g.Teacher)
                .Include(s => s.Teacher)
                .Where(s => s.Date >= startDate && s.Date <= endDate)
                .OrderBy(s => s.Date)
                .ToListAsync();

            // ترتيب على العميل
            return sessions
                .OrderBy(s => s.Date)
                .ThenBy(s => s.StartTime)
                .ToList();
        }

        public async Task<Session> AddSessionAsync(Session session)
        {
            session.CreatedAt = DateTime.Now;
            _context.Sessions.Add(session);
            await _context.SaveChangesAsync();
            return session;
        }

        public async Task<Session> UpdateSessionAsync(Session session)
        {
            session.UpdatedAt = DateTime.Now;
            _context.Sessions.Update(session);
            await _context.SaveChangesAsync();
            return session;
        }

        public async Task<bool> DeleteSessionAsync(int id)
        {
            var session = await _context.Sessions
                .Include(s => s.Attendances)
                .FirstOrDefaultAsync(s => s.Id == id);
            
            if (session == null) return false;

            _context.Sessions.Remove(session);
            await _context.SaveChangesAsync();
            return true;
        }

        public async Task<bool> MarkSessionCompleteAsync(int sessionId, bool isCompleted = true)
        {
            var session = await _context.Sessions.FindAsync(sessionId);
            if (session == null) return false;

            session.IsCompleted = isCompleted;
            session.UpdatedAt = DateTime.Now;
            await _context.SaveChangesAsync();
            return true;
        }

        public async Task<bool> TakeAttendanceAsync(int sessionId, List<Attendance> attendances)
        {
            var session = await _context.Sessions
                .Include(s => s.Attendances)
                .FirstOrDefaultAsync(s => s.Id == sessionId);
            
            if (session == null) return false;

            // Remove existing attendance records for this session
            if (session.Attendances != null && session.Attendances.Any())
            {
                _context.Attendances.RemoveRange(session.Attendances);
            }

            // Add new attendance records
            foreach (var attendance in attendances)
            {
                attendance.SessionId = sessionId;
                attendance.Date = session.Date;
                attendance.RecordedAt = DateTime.Now;
                _context.Attendances.Add(attendance);
            }

            await _context.SaveChangesAsync();
            return true;
        }

        public async Task<List<Attendance>> GetSessionAttendanceAsync(int sessionId)
        {
            return await _context.Attendances
                .Include(a => a.Student)
                .Where(a => a.SessionId == sessionId)
                .OrderBy(a => a.Student.Name)
                .ToListAsync();
        }

        public async Task<Dictionary<string, object>> GetSessionStatisticsAsync(int sessionId)
        {
            var session = await GetSessionByIdAsync(sessionId);
            if (session == null) return new Dictionary<string, object>();

            var totalStudents = session.Group?.Students?.Count ?? 0;
            var presentStudents = session.Attendances?.Count(a => a.IsPresent) ?? 0;
            var absentStudents = session.Attendances?.Count(a => !a.IsPresent) ?? 0;
            var attendanceRate = totalStudents > 0 ? (double)presentStudents / totalStudents * 100 : 0;

            return new Dictionary<string, object>
            {
                ["TotalStudents"] = totalStudents,
                ["PresentStudents"] = presentStudents,
                ["AbsentStudents"] = absentStudents,
                ["AttendanceRate"] = attendanceRate,
                ["IsCompleted"] = session.IsCompleted,
                ["Duration"] = session.Duration.TotalMinutes
            };
        }

        public async Task<Dictionary<string, int>> GetSessionStatisticsSummaryAsync()
        {
            var today = DateTime.Today;
            var thisWeek = today.AddDays(-(int)today.DayOfWeek);
            var thisMonth = new DateTime(today.Year, today.Month, 1);

            var totalSessions = await _context.Sessions.CountAsync();
            var todaySessions = await _context.Sessions.CountAsync(s => s.Date.Date == today);
            var thisWeekSessions = await _context.Sessions.CountAsync(s => s.Date >= thisWeek);
            var thisMonthSessions = await _context.Sessions.CountAsync(s => s.Date >= thisMonth);
            var completedSessions = await _context.Sessions.CountAsync(s => s.IsCompleted);

            return new Dictionary<string, int>
            {
                ["TotalSessions"] = totalSessions,
                ["TodaySessions"] = todaySessions,
                ["ThisWeekSessions"] = thisWeekSessions,
                ["ThisMonthSessions"] = thisMonthSessions,
                ["CompletedSessions"] = completedSessions,
                ["PendingSessions"] = totalSessions - completedSessions
            };
        }

        public async Task<bool> CheckSessionConflictAsync(Session session)
        {
            var conflictingSessions = await _context.Sessions
                .Where(s => s.Id != session.Id && // Exclude current session if updating
                           s.Date.Date == session.Date.Date &&
                           s.TeacherId == session.TeacherId &&
                           ((s.StartTime < session.EndTime && s.EndTime > session.StartTime)))
                .AnyAsync();

            return conflictingSessions;
        }

        public async Task<List<Session>> GetUpcomingSessionsAsync(int days = 7)
        {
            var startDate = DateTime.Today;
            var endDate = startDate.AddDays(days);

            var sessions = await _context.Sessions
                .Include(s => s.Group)
                .ThenInclude(g => g.Teacher)
                .Include(s => s.Teacher)
                .Where(s => s.Date >= startDate && s.Date <= endDate && !s.IsCompleted)
                .OrderBy(s => s.Date)
                .ToListAsync();

            // ترتيب على العميل
            return sessions
                .OrderBy(s => s.Date)
                .ThenBy(s => s.StartTime)
                .ToList();
        }

        public void Dispose()
        {
            _context?.Dispose();
        }
    }
}
