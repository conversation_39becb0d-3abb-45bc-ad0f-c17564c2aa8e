<Window x:Class="EduTrackForWin.Views.QuickPaymentWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        mc:Ignorable="d"
        Title="دفعة سريعة" Height="400" Width="500"
        WindowStartupLocation="CenterScreen"
        ResizeMode="NoResize">

    <Window.Resources>
        <!-- Styles -->
        <Style x:Key="LabelStyle" TargetType="TextBlock">
            <Setter Property="FontWeight" Value="SemiBold"/>
            <Setter Property="Margin" Value="0,10,0,5"/>
            <Setter Property="Foreground" Value="#424242"/>
        </Style>

        <Style x:Key="InputFieldStyle" TargetType="TextBox">
            <Setter Property="Padding" Value="10"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="BorderBrush" Value="#E0E0E0"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="Margin" Value="0,5,0,15"/>
        </Style>

        <Style x:Key="ComboBoxStyle" TargetType="ComboBox">
            <Setter Property="Padding" Value="10"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="BorderBrush" Value="#E0E0E0"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="Margin" Value="0,5,0,15"/>
        </Style>

        <Style x:Key="PrimaryButtonStyle" TargetType="Button">
            <Setter Property="Background" Value="#4CAF50"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Padding" Value="20,10"/>
            <Setter Property="Margin" Value="10"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="FontWeight" Value="SemiBold"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}" 
                              CornerRadius="5">
                            <ContentPresenter HorizontalAlignment="Center" 
                                            VerticalAlignment="Center"
                                            Margin="{TemplateBinding Padding}"/>
                        </Border>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <Style x:Key="SecondaryButtonStyle" TargetType="Button">
            <Setter Property="Background" Value="#757575"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Padding" Value="20,10"/>
            <Setter Property="Margin" Value="10"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="FontWeight" Value="SemiBold"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}" 
                              CornerRadius="5">
                            <ContentPresenter HorizontalAlignment="Center" 
                                            VerticalAlignment="Center"
                                            Margin="{TemplateBinding Padding}"/>
                        </Border>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>
    </Window.Resources>

    <Grid Background="#F5F5F5">
        <Border Background="White" CornerRadius="10" Margin="20" Padding="30">
            <Border.Effect>
                <DropShadowEffect Color="Gray" Direction="270" ShadowDepth="3" Opacity="0.3" BlurRadius="10"/>
            </Border.Effect>

            <StackPanel>
                <!-- Header -->
                <TextBlock Text="⚡ دفعة سريعة" 
                         FontSize="24" FontWeight="Bold" 
                         HorizontalAlignment="Center" 
                         Foreground="#4CAF50" 
                         Margin="0,0,0,30"/>

                <!-- Student Search -->
                <TextBlock Text="البحث عن الطالب *" Style="{StaticResource LabelStyle}"/>
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>
                    
                    <TextBox Name="TxtStudentSearch" Grid.Column="0" 
                           Style="{StaticResource InputFieldStyle}" 
                           TextChanged="TxtStudentSearch_TextChanged"
                           Text="اكتب اسم الطالب أو رقم الهاتف..."/>
                    
                    <Button Name="BtnSearchStudent" Grid.Column="1" Content="🔍" 
                          Background="#2196F3" Foreground="White" 
                          BorderThickness="0" Padding="10" Margin="5,5,0,15"
                          Click="BtnSearchStudent_Click"/>
                </Grid>

                <!-- Student Results -->
                <ListBox Name="LstStudentResults" MaxHeight="100" 
                       SelectionChanged="LstStudentResults_SelectionChanged"
                       Visibility="Collapsed"/>

                <!-- Selected Student Info -->
                <Border Name="SelectedStudentPanel" Visibility="Collapsed" 
                      Background="#E8F5E8" Padding="15" Margin="0,0,0,15" CornerRadius="5">
                    <StackPanel>
                        <TextBlock Name="TxtSelectedStudent" FontWeight="Bold" FontSize="16"/>
                        <TextBlock Name="TxtStudentInfo" FontSize="12" Foreground="#666"/>
                        <TextBlock Name="TxtStudentDue" FontSize="12" FontWeight="Bold" Foreground="Red"/>
                    </StackPanel>
                </Border>

                <!-- Amount -->
                <TextBlock Text="المبلغ *" Style="{StaticResource LabelStyle}"/>
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>
                    
                    <TextBox Name="TxtAmount" Grid.Column="0" 
                           Style="{StaticResource InputFieldStyle}"/>
                    
                    <Button Name="BtnFullAmount" Grid.Column="1" Content="المبلغ كاملاً" 
                          Background="#FF9800" Foreground="White" 
                          BorderThickness="0" Padding="10" Margin="5,5,0,15"
                          Click="BtnFullAmount_Click"/>
                    
                    <Button Name="BtnMonthlyFee" Grid.Column="2" Content="الاشتراك الشهري" 
                          Background="#9C27B0" Foreground="White" 
                          BorderThickness="0" Padding="10" Margin="5,5,0,15"
                          Click="BtnMonthlyFee_Click"/>
                </Grid>

                <!-- Payment Method -->
                <TextBlock Text="طريقة الدفع *" Style="{StaticResource LabelStyle}"/>
                <ComboBox Name="CmbPaymentMethod" Style="{StaticResource ComboBoxStyle}">
                    <ComboBoxItem Content="نقداً" IsSelected="True"/>
                    <ComboBoxItem Content="بطاقة ائتمان"/>
                    <ComboBoxItem Content="تحويل بنكي"/>
                    <ComboBoxItem Content="شيك"/>
                    <ComboBoxItem Content="محفظة إلكترونية"/>
                </ComboBox>

                <!-- Notes -->
                <TextBlock Text="ملاحظات" Style="{StaticResource LabelStyle}"/>
                <TextBox Name="TxtNotes" Style="{StaticResource InputFieldStyle}" 
                       Height="60" TextWrapping="Wrap" AcceptsReturn="True"/>

                <!-- Buttons -->
                <StackPanel Orientation="Horizontal" HorizontalAlignment="Center" Margin="0,20,0,0">
                    <Button Name="BtnSave" Content="تسجيل الدفعة" 
                          Style="{StaticResource PrimaryButtonStyle}" 
                          Click="BtnSave_Click"/>
                    <Button Name="BtnCancel" Content="إلغاء" 
                          Style="{StaticResource SecondaryButtonStyle}" 
                          Click="BtnCancel_Click"/>
                </StackPanel>
            </StackPanel>
        </Border>
    </Grid>
</Window>
