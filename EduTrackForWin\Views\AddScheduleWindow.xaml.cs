using System;
using System.Collections.Generic;
using System.Linq;
using System.Windows;
using System.Windows.Controls;
using EduTrackForWin.Models;
using EduTrackForWin.Data;
using Microsoft.EntityFrameworkCore;
using System.Text;

namespace EduTrackForWin.Views
{
    public partial class AddScheduleWindow : Window
    {
        private readonly EduTrackDbContext _context;
        private readonly List<DayOfWeek> _selectedDays = new List<DayOfWeek>();

        public AddScheduleWindow()
        {
            try
            {
                InitializeComponent();
                _context = new EduTrackDbContext();
                
                // تعيين تاريخ البداية والنهاية
                DpStartDate.SelectedDate = DateTime.Today;
                DpEndDate.SelectedDate = DateTime.Today.AddMonths(1);
                
                LoadData();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تهيئة نافذة الجدول الزمني: {ex.Message}", "خطأ",
                              MessageBoxButton.OK, MessageBoxImage.Error);
                throw;
            }
        }

        private async void LoadData()
        {
            await LoadGroups();
            await LoadTeachers();
        }

        private async Task LoadGroups()
        {
            try
            {
                var groups = await _context.Groups
                    .Include(g => g.Teacher)
                    .Where(g => g.IsActive)
                    .OrderBy(g => g.Name)
                    .ToListAsync();

                CmbGroup.ItemsSource = groups;
                
                if (groups.Any())
                {
                    CmbGroup.SelectedIndex = 0;
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل المجموعات: {ex.Message}", "خطأ", 
                              MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async Task LoadTeachers()
        {
            try
            {
                var teachers = await _context.Teachers
                    .OrderBy(t => t.Name)
                    .ToListAsync();

                CmbTeacher.ItemsSource = teachers;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل المعلمين: {ex.Message}", "خطأ", 
                              MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void CmbGroup_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            if (CmbGroup.SelectedItem is Group selectedGroup)
            {
                // Auto-select the group's teacher
                CmbTeacher.SelectedValue = selectedGroup.TeacherId;
                
                // Auto-fill room if group has a default room
                if (!string.IsNullOrEmpty(selectedGroup.Room))
                {
                    TxtRoom.Text = selectedGroup.Room;
                }
            }
        }

        private void Day_CheckedChanged(object sender, RoutedEventArgs e)
        {
            UpdateSelectedDays();
            UpdatePreview();
        }

        private void UpdateSelectedDays()
        {
            _selectedDays.Clear();
            
            if (ChkSunday.IsChecked == true) _selectedDays.Add(DayOfWeek.Sunday);
            if (ChkMonday.IsChecked == true) _selectedDays.Add(DayOfWeek.Monday);
            if (ChkTuesday.IsChecked == true) _selectedDays.Add(DayOfWeek.Tuesday);
            if (ChkWednesday.IsChecked == true) _selectedDays.Add(DayOfWeek.Wednesday);
            if (ChkThursday.IsChecked == true) _selectedDays.Add(DayOfWeek.Thursday);
            if (ChkFriday.IsChecked == true) _selectedDays.Add(DayOfWeek.Friday);
            if (ChkSaturday.IsChecked == true) _selectedDays.Add(DayOfWeek.Saturday);
        }

        private void UpdatePreview()
        {
            if (_selectedDays.Count == 0)
            {
                TxtPreview.Text = "لم يتم اختيار أي أيام بعد";
                return;
            }

            var startDate = DpStartDate.SelectedDate ?? DateTime.Today;
            var endDate = DpEndDate.SelectedDate ?? DateTime.Today.AddMonths(1);
            var startTime = TxtStartTime.Text;
            var endTime = TxtEndTime.Text;
            
            var sb = new StringBuilder();
            sb.AppendLine($"سيتم إنشاء حصص في الأيام التالية من {startDate:yyyy/MM/dd} إلى {endDate:yyyy/MM/dd}:");
            sb.AppendLine();
            
            var dayNames = new Dictionary<DayOfWeek, string>
            {
                { DayOfWeek.Sunday, "الأحد" },
                { DayOfWeek.Monday, "الإثنين" },
                { DayOfWeek.Tuesday, "الثلاثاء" },
                { DayOfWeek.Wednesday, "الأربعاء" },
                { DayOfWeek.Thursday, "الخميس" },
                { DayOfWeek.Friday, "الجمعة" },
                { DayOfWeek.Saturday, "السبت" }
            };
            
            foreach (var day in _selectedDays.OrderBy(d => (int)d))
            {
                sb.AppendLine($"• {dayNames[day]} من الساعة {startTime} إلى {endTime}");
            }
            
            // حساب عدد الحصص التي سيتم إنشاؤها
            int sessionCount = 0;
            var currentDate = startDate;
            while (currentDate <= endDate)
            {
                if (_selectedDays.Contains(currentDate.DayOfWeek))
                {
                    sessionCount++;
                }
                currentDate = currentDate.AddDays(1);
            }
            
            sb.AppendLine();
            sb.AppendLine($"إجمالي عدد الحصص: {sessionCount} حصة");
            
            TxtPreview.Text = sb.ToString();
        }

        private async void BtnSave_Click(object sender, RoutedEventArgs e)
        {
            if (!ValidateInput())
                return;

            try
            {
                var startDate = DpStartDate.SelectedDate!.Value;
                var endDate = DpEndDate.SelectedDate!.Value;
                var groupId = (int)CmbGroup.SelectedValue;
                var teacherId = (int)CmbTeacher.SelectedValue;
                var room = TxtRoom.Text.Trim();
                var topic = TxtTopic.Text.Trim();
                var notes = TxtNotes.Text.Trim();
                var startTime = TimeSpan.Parse(TxtStartTime.Text);
                var endTime = TimeSpan.Parse(TxtEndTime.Text);
                
                // إنشاء قائمة بالحصص
                var sessions = new List<Session>();
                var currentDate = startDate;
                
                while (currentDate <= endDate)
                {
                    if (_selectedDays.Contains(currentDate.DayOfWeek))
                    {
                        var session = new Session
                        {
                            Date = currentDate,
                            StartTime = startTime,
                            EndTime = endTime,
                            Room = room,
                            Topic = topic,
                            Notes = notes,
                            GroupId = groupId,
                            TeacherId = teacherId,
                            IsCompleted = false,
                            TeacherPresent = true,
                            CreatedAt = DateTime.Now
                        };
                        
                        sessions.Add(session);
                    }
                    
                    currentDate = currentDate.AddDays(1);
                }
                
                // حفظ الحصص في قاعدة البيانات
                await _context.Sessions.AddRangeAsync(sessions);
                await _context.SaveChangesAsync();
                
                MessageBox.Show($"تم إنشاء {sessions.Count} حصة بنجاح", "نجح",
                              MessageBoxButton.OK, MessageBoxImage.Information);
                
                DialogResult = true;
                Close();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في حفظ الجدول الزمني: {ex.Message}", "خطأ",
                              MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private bool ValidateInput()
        {
            if (!DpStartDate.SelectedDate.HasValue)
            {
                MessageBox.Show("يرجى اختيار تاريخ البداية", "خطأ في البيانات",
                              MessageBoxButton.OK, MessageBoxImage.Warning);
                DpStartDate.Focus();
                return false;
            }

            if (!DpEndDate.SelectedDate.HasValue)
            {
                MessageBox.Show("يرجى اختيار تاريخ النهاية", "خطأ في البيانات",
                              MessageBoxButton.OK, MessageBoxImage.Warning);
                DpEndDate.Focus();
                return false;
            }

            if (DpEndDate.SelectedDate.Value < DpStartDate.SelectedDate.Value)
            {
                MessageBox.Show("تاريخ النهاية يجب أن يكون بعد تاريخ البداية", "خطأ في البيانات",
                              MessageBoxButton.OK, MessageBoxImage.Warning);
                DpEndDate.Focus();
                return false;
            }

            if (_selectedDays.Count == 0)
            {
                MessageBox.Show("يرجى اختيار يوم واحد على الأقل من أيام الأسبوع", "خطأ في البيانات",
                              MessageBoxButton.OK, MessageBoxImage.Warning);
                return false;
            }

            if (!TimeSpan.TryParse(TxtStartTime.Text, out var startTime))
            {
                MessageBox.Show("يرجى إدخال وقت البداية بصيغة صحيحة (مثال: 09:00)", "خطأ في البيانات", 
                              MessageBoxButton.OK, MessageBoxImage.Warning);
                TxtStartTime.Focus();
                return false;
            }

            if (!TimeSpan.TryParse(TxtEndTime.Text, out var endTime))
            {
                MessageBox.Show("يرجى إدخال وقت النهاية بصيغة صحيحة (مثال: 10:00)", "خطأ في البيانات", 
                              MessageBoxButton.OK, MessageBoxImage.Warning);
                TxtEndTime.Focus();
                return false;
            }

            if (endTime <= startTime)
            {
                MessageBox.Show("وقت النهاية يجب أن يكون بعد وقت البداية", "خطأ في البيانات", 
                              MessageBoxButton.OK, MessageBoxImage.Warning);
                TxtEndTime.Focus();
                return false;
            }

            if (CmbGroup.SelectedValue == null)
            {
                MessageBox.Show("يرجى اختيار المجموعة", "خطأ في البيانات", 
                              MessageBoxButton.OK, MessageBoxImage.Warning);
                CmbGroup.Focus();
                return false;
            }

            if (CmbTeacher.SelectedValue == null)
            {
                MessageBox.Show("يرجى اختيار المعلم", "خطأ في البيانات", 
                              MessageBoxButton.OK, MessageBoxImage.Warning);
                CmbTeacher.Focus();
                return false;
            }

            return true;
        }

        private void BtnCancel_Click(object sender, RoutedEventArgs e)
        {
            DialogResult = false;
            Close();
        }

        protected override void OnClosed(EventArgs e)
        {
            _context?.Dispose();
            base.OnClosed(e);
        }
    }
}