<Page x:Class="EduTrackForWin.Views.SchedulesPage"
      xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
      xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
      xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
      xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
      xmlns:local="clr-namespace:EduTrackForWin.Views"
      mc:Ignorable="d"
      d:DesignHeight="450" d:DesignWidth="800"
      Title="الجداول الزمنية">

    <Page.Resources>
        <!-- Card Style -->
        <Style x:Key="CardStyle" TargetType="Border" BasedOn="{StaticResource ModernCardStyle}">
            <Setter Property="Background" Value="{StaticResource SurfaceColor}"/>
            <Setter Property="CornerRadius" Value="8"/>
            <Setter Property="Padding" Value="20"/>
            <Setter Property="Margin" Value="10"/>
        </Style>

        <!-- Button Styles -->
        <Style x:Key="PrimaryButtonStyle" TargetType="Button">
            <Setter Property="Background" Value="{StaticResource AccentColor}"/>
            <Setter Property="Foreground" Value="White"/>
        </Style>

        <Style x:Key="SecondaryButtonStyle" TargetType="Button">
            <Setter Property="Background" Value="{StaticResource PrimaryColor}"/>
            <Setter Property="Foreground" Value="White"/>
        </Style>
    </Page.Resources>

    <Grid Background="#F5F5F5" Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <Border Grid.Row="0" Style="{StaticResource CardStyle}" Background="#F3E5F5">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <StackPanel Grid.Column="0">
                    <TextBlock Text="📅 الجداول الزمنية" FontSize="24" FontWeight="Bold" Foreground="#7B1FA2"/>
                    <TextBlock Text="إدارة الجداول الزمنية الأسبوعية والحصص المتكررة" FontSize="14" Foreground="#424242" Margin="0,5,0,0"/>
                </StackPanel>

                <Button Grid.Column="1" Name="BtnAddSchedule" Content="+ إضافة جدول زمني جديد"
                      Style="{StaticResource PrimaryButtonStyle}"
                      FontSize="14" Padding="20,10" Click="BtnAddSchedule_Click"/>
            </Grid>
        </Border>

        <!-- Filter Controls -->
        <Border Grid.Row="1" Style="{StaticResource CardStyle}">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="200"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <!-- Group Filter -->
                <ComboBox Grid.Column="0" Name="CmbGroupFilter"
                        Padding="10" FontSize="14" Margin="0,0,10,0"
                        BorderBrush="#E0E0E0" BorderThickness="1"
                        SelectionChanged="CmbGroupFilter_SelectionChanged">
                    <ComboBoxItem Content="جميع المجموعات" IsSelected="True"/>
                </ComboBox>

                <!-- Status Filter -->
                <StackPanel Grid.Column="1" Orientation="Horizontal" VerticalAlignment="Center" Margin="10,0">
                    <RadioButton Name="RbAllSchedules" Content="جميع الجداول" IsChecked="True"
                               Margin="0,0,20,0" Checked="FilterStatus_Changed"/>
                    <RadioButton Name="RbActiveSchedules" Content="الجداول النشطة"
                               Margin="0,0,20,0" Checked="FilterStatus_Changed"/>
                    <RadioButton Name="RbExpiredSchedules" Content="الجداول المنتهية"
                               Checked="FilterStatus_Changed"/>
                </StackPanel>

                <!-- Refresh Button -->
                <Button Grid.Column="2" Name="BtnRefresh" Content="🔄 تحديث"
                      Style="{StaticResource SecondaryButtonStyle}"
                      Click="BtnRefresh_Click"/>
            </Grid>
        </Border>

        <!-- Main Content -->
        <Border Grid.Row="2" Style="{StaticResource CardStyle}">
            <StackPanel>
                <TextBlock Text="قائمة الجداول الزمنية" FontSize="18" FontWeight="SemiBold"
                         Margin="0,0,0,15" Foreground="#212121"/>

                <DataGrid Name="DgSchedules"
                        AutoGenerateColumns="False"
                        CanUserAddRows="False"
                        CanUserDeleteRows="False"
                        IsReadOnly="True"
                        GridLinesVisibility="Horizontal"
                        HeadersVisibility="Column"
                        BorderThickness="0"
                        Background="Transparent"
                        RowBackground="White"
                        AlternatingRowBackground="#F5F5F5"
                        SelectionMode="Single">

                    <DataGrid.Columns>
                        <DataGridTextColumn Header="اسم الجدول" Binding="{Binding Name}" Width="150"/>
                        <DataGridTextColumn Header="المجموعة" Binding="{Binding Group.Name}" Width="150"/>
                        <DataGridTextColumn Header="المعلم" Binding="{Binding Teacher.Name}" Width="150"/>
                        <DataGridTextColumn Header="من تاريخ" Binding="{Binding StartDate, StringFormat='{}{0:yyyy/MM/dd}'}" Width="100"/>
                        <DataGridTextColumn Header="إلى تاريخ" Binding="{Binding EndDate, StringFormat='{}{0:yyyy/MM/dd}'}" Width="100"/>
                        <DataGridTextColumn Header="الوقت" Binding="{Binding ScheduleTime}" Width="120"/>
                        <DataGridTextColumn Header="أيام الأسبوع" Binding="{Binding DaysOfWeek}" Width="*"/>

                        <DataGridTemplateColumn Header="الحالة" Width="80">
                            <DataGridTemplateColumn.CellTemplate>
                                <DataTemplate>
                                    <Border CornerRadius="10" Padding="8,4">
                                        <Border.Style>
                                            <Style TargetType="Border">
                                                <Style.Triggers>
                                                    <DataTrigger Binding="{Binding EndDate, Converter={StaticResource DateCompareConverter}}" Value="True">
                                                        <Setter Property="Background" Value="#4CAF50"/>
                                                    </DataTrigger>
                                                    <DataTrigger Binding="{Binding EndDate, Converter={StaticResource DateCompareConverter}}" Value="False">
                                                        <Setter Property="Background" Value="#FF9800"/>
                                                    </DataTrigger>
                                                </Style.Triggers>
                                            </Style>
                                        </Border.Style>
                                        <TextBlock Text="{Binding EndDate, Converter={StaticResource DateStatusConverter}}"
                                                 Foreground="White" FontSize="10" HorizontalAlignment="Center"/>
                                    </Border>
                                </DataTemplate>
                            </DataGridTemplateColumn.CellTemplate>
                        </DataGridTemplateColumn>

                        <DataGridTemplateColumn Header="الإجراءات" Width="250">
                            <DataGridTemplateColumn.CellTemplate>
                                <DataTemplate>
                                    <StackPanel Orientation="Horizontal">
                                        <Button Content="عرض" Background="#2196F3" Foreground="White"
                                              Padding="8,4" Margin="2" BorderThickness="0"
                                              Click="BtnViewSchedule_Click" Tag="{Binding Id}"/>
                                        <Button Content="تعديل" Background="#4CAF50" Foreground="White"
                                              Padding="8,4" Margin="2" BorderThickness="0"
                                              Click="BtnEditSchedule_Click" Tag="{Binding Id}"/>
                                        <Button Content="تحديث الحصص" Background="#FF9800" Foreground="White"
                                              Padding="8,4" Margin="2" BorderThickness="0"
                                              Click="BtnRegenerateSchedule_Click" Tag="{Binding Id}"/>
                                        <Button Content="حذف" Background="#F44336" Foreground="White"
                                              Padding="8,4" Margin="2" BorderThickness="0"
                                              Click="BtnDeleteSchedule_Click" Tag="{Binding Id}"/>
                                    </StackPanel>
                                </DataTemplate>
                            </DataGridTemplateColumn.CellTemplate>
                        </DataGridTemplateColumn>
                    </DataGrid.Columns>
                </DataGrid>
            </StackPanel>
        </Border>
    </Grid>
</Page>