<Window x:Class="EduTrackForWin.Views.TakeAttendanceWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:EduTrackForWin.Views"
        mc:Ignorable="d"
        Title="تسجيل الحضور" Height="600" Width="700"
        WindowStartupLocation="CenterOwner">

    <Window.Resources>
        <!-- Card Style -->
        <Style x:Key="CardStyle" TargetType="Border">
            <Setter Property="Background" Value="White"/>
            <Setter Property="CornerRadius" Value="8"/>
            <Setter Property="Padding" Value="20"/>
            <Setter Property="Margin" Value="10"/>
            <Setter Property="Effect">
                <Setter.Value>
                    <DropShadowEffect Color="Gray" Direction="270" ShadowDepth="2" Opacity="0.3" BlurRadius="5"/>
                </Setter.Value>
            </Setter>
        </Style>

        <!-- Button Styles -->
        <Style x:Key="PrimaryButtonStyle" TargetType="Button">
            <Setter Property="Background" Value="#4CAF50"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Padding" Value="20,10"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}" 
                              CornerRadius="5">
                            <ContentPresenter HorizontalAlignment="Center" 
                                            VerticalAlignment="Center"
                                            Margin="{TemplateBinding Padding}"/>
                        </Border>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <Style x:Key="SecondaryButtonStyle" TargetType="Button">
            <Setter Property="Background" Value="#757575"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Padding" Value="20,10"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}" 
                              CornerRadius="5">
                            <ContentPresenter HorizontalAlignment="Center" 
                                            VerticalAlignment="Center"
                                            Margin="{TemplateBinding Padding}"/>
                        </Border>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>
    </Window.Resources>

    <Grid Background="#F5F5F5">
        <StackPanel Margin="20">
            <!-- Header -->
            <Border Style="{StaticResource CardStyle}" Background="#E8F5E8">
                <StackPanel>
                    <TextBlock Name="TxtSessionInfo" Text="تسجيل الحضور" FontSize="20" FontWeight="Bold" 
                             HorizontalAlignment="Center" Foreground="#2E7D32"/>
                    <TextBlock Name="TxtSessionDetails" Text="تفاصيل الحصة" FontSize="14" 
                             HorizontalAlignment="Center" Foreground="#424242" Margin="0,5,0,0"/>
                </StackPanel>
            </Border>

            <!-- Quick Actions -->
            <Border Style="{StaticResource CardStyle}">
                <StackPanel>
                    <TextBlock Text="إجراءات سريعة" FontSize="16" FontWeight="SemiBold" 
                             Margin="0,0,0,15" Foreground="#212121"/>
                    
                    <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                        <Button Name="BtnMarkAllPresent" Content="تمييز الكل حاضر"
                              Background="#4CAF50" Foreground="White"
                              Padding="15,8" Margin="10" BorderThickness="0"
                              Click="BtnMarkAllPresent_Click">
                            <Button.Template>
                                <ControlTemplate TargetType="Button">
                                    <Border Background="{TemplateBinding Background}"
                                          CornerRadius="5">
                                        <ContentPresenter HorizontalAlignment="Center"
                                                        VerticalAlignment="Center"
                                                        Margin="{TemplateBinding Padding}"/>
                                    </Border>
                                </ControlTemplate>
                            </Button.Template>
                        </Button>

                        <Button Name="BtnMarkAllAbsent" Content="تمييز الكل غائب"
                              Background="#F44336" Foreground="White"
                              Padding="15,8" Margin="10" BorderThickness="0"
                              Click="BtnMarkAllAbsent_Click">
                            <Button.Template>
                                <ControlTemplate TargetType="Button">
                                    <Border Background="{TemplateBinding Background}"
                                          CornerRadius="5">
                                        <ContentPresenter HorizontalAlignment="Center"
                                                        VerticalAlignment="Center"
                                                        Margin="{TemplateBinding Padding}"/>
                                    </Border>
                                </ControlTemplate>
                            </Button.Template>
                        </Button>

                        <Button Name="BtnClearAll" Content="مسح الكل"
                              Background="#FF9800" Foreground="White"
                              Padding="15,8" Margin="10" BorderThickness="0"
                              Click="BtnClearAll_Click">
                            <Button.Template>
                                <ControlTemplate TargetType="Button">
                                    <Border Background="{TemplateBinding Background}"
                                          CornerRadius="5">
                                        <ContentPresenter HorizontalAlignment="Center"
                                                        VerticalAlignment="Center"
                                                        Margin="{TemplateBinding Padding}"/>
                                    </Border>
                                </ControlTemplate>
                            </Button.Template>
                        </Button>
                    </StackPanel>
                </StackPanel>
            </Border>

            <!-- Attendance List -->
            <Border Style="{StaticResource CardStyle}">
                <StackPanel>
                    <TextBlock Text="قائمة الطلاب" FontSize="16" FontWeight="SemiBold" 
                             Margin="0,0,0,15" Foreground="#212121"/>
                    
                    <DataGrid Name="DgAttendance" 
                            AutoGenerateColumns="False" 
                            CanUserAddRows="False" 
                            CanUserDeleteRows="False"
                            GridLinesVisibility="Horizontal"
                            HeadersVisibility="Column"
                            BorderThickness="0"
                            Background="Transparent"
                            RowBackground="White"
                            AlternatingRowBackground="#F5F5F5"
                            Height="300">
                        
                        <DataGrid.Columns>
                            <DataGridTextColumn Header="اسم الطالب" Binding="{Binding StudentName}" Width="200" IsReadOnly="True"/>
                            
                            <DataGridTemplateColumn Header="الحضور" Width="150">
                                <DataGridTemplateColumn.CellTemplate>
                                    <DataTemplate>
                                        <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                                            <RadioButton Name="RbPresent" Content="حاضر" 
                                                       IsChecked="{Binding IsPresent}" 
                                                       GroupName="{Binding StudentId}"
                                                       Margin="0,0,20,0"/>
                                            <RadioButton Name="RbAbsent" Content="غائب" 
                                                       IsChecked="{Binding IsAbsent}" 
                                                       GroupName="{Binding StudentId}"/>
                                        </StackPanel>
                                    </DataTemplate>
                                </DataGridTemplateColumn.CellTemplate>
                            </DataGridTemplateColumn>
                            
                            <DataGridTemplateColumn Header="ملاحظات" Width="*">
                                <DataGridTemplateColumn.CellTemplate>
                                    <DataTemplate>
                                        <TextBox Text="{Binding Notes, UpdateSourceTrigger=PropertyChanged}" 
                                               BorderThickness="0" Background="Transparent"
                                               MaxLength="200"/>
                                    </DataTemplate>
                                </DataGridTemplateColumn.CellTemplate>
                            </DataGridTemplateColumn>
                        </DataGrid.Columns>
                    </DataGrid>
                </StackPanel>
            </Border>

            <!-- Summary -->
            <Border Style="{StaticResource CardStyle}">
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>
                    
                    <StackPanel Grid.Column="0" HorizontalAlignment="Center">
                        <TextBlock Text="إجمالي الطلاب" FontSize="12" Foreground="#757575" HorizontalAlignment="Center"/>
                        <TextBlock Name="TxtTotalStudents" Text="0" FontSize="20" FontWeight="Bold" 
                                 Foreground="#2196F3" HorizontalAlignment="Center"/>
                    </StackPanel>
                    
                    <StackPanel Grid.Column="1" HorizontalAlignment="Center">
                        <TextBlock Text="الحاضرون" FontSize="12" Foreground="#757575" HorizontalAlignment="Center"/>
                        <TextBlock Name="TxtPresentCount" Text="0" FontSize="20" FontWeight="Bold" 
                                 Foreground="#4CAF50" HorizontalAlignment="Center"/>
                    </StackPanel>
                    
                    <StackPanel Grid.Column="2" HorizontalAlignment="Center">
                        <TextBlock Text="نسبة الحضور" FontSize="12" Foreground="#757575" HorizontalAlignment="Center"/>
                        <TextBlock Name="TxtAttendanceRate" Text="0%" FontSize="20" FontWeight="Bold" 
                                 Foreground="#FF9800" HorizontalAlignment="Center"/>
                    </StackPanel>
                </Grid>
            </Border>

            <!-- Action Buttons -->
            <Border Style="{StaticResource CardStyle}">
                <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                    <Button Name="BtnSave" Content="حفظ الحضور" Style="{StaticResource PrimaryButtonStyle}" 
                          Margin="0,0,10,0" Click="BtnSave_Click"/>
                    <Button Name="BtnCancel" Content="إلغاء" Style="{StaticResource SecondaryButtonStyle}" 
                          Margin="10,0,0,0" Click="BtnCancel_Click"/>
                </StackPanel>
            </Border>
        </StackPanel>
    </Grid>
</Window>
