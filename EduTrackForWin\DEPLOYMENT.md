# دليل النشر والتوزيع - EduTrack for Windows

## نظرة عامة
هذا الدليل يوضح كيفية نشر وتوزيع تطبيق EduTrack for Windows للاستخدام في الإنتاج.

## متطلبات النشر

### للمطور (بيئة التطوير)
- Windows 10/11 (64-bit)
- .NET 8 SDK
- Visual Studio 2022 أو VS Code
- Git (اختياري)

### للمستخدم النهائي (بيئة الإنتاج)
- Windows 10/11 (64-bit)
- 4 GB RAM (الحد الأدنى)
- 500 MB مساحة تخزين
- لا يحتاج تثبيت .NET منفصل (مع النشر المستقل)

## طرق النشر

### 1. النشر المستقل (موصى به)
هذه الطريقة تنشئ تطبيق مستقل لا يحتاج تثبيت .NET على الجهاز المستهدف.

#### باستخدام ملف publish.bat
```batch
# في مجلد المشروع
publish.bat
```

#### باستخدام سطر الأوامر
```bash
# تنظيف المشروع
dotnet clean --configuration Release

# استعادة الحزم
dotnet restore

# بناء المشروع
dotnet build --configuration Release

# النشر المستقل
dotnet publish --configuration Release --output "./publish" --self-contained true --runtime win-x64
```

### 2. النشر المعتمد على Framework
هذه الطريقة تتطلب تثبيت .NET 8 Runtime على الجهاز المستهدف.

```bash
dotnet publish --configuration Release --output "./publish-framework" --self-contained false --runtime win-x64
```

## هيكل ملفات النشر

بعد النشر المستقل، ستجد الملفات التالية في مجلد `publish`:

```
publish/
├── EduTrackForWin.exe          # الملف التنفيذي الرئيسي
├── EduTrackForWin.dll          # مكتبة التطبيق
├── EduTrackForWin.deps.json    # تبعيات التطبيق
├── EduTrackForWin.runtimeconfig.json
├── Microsoft.*.dll             # مكتبات .NET
├── System.*.dll                # مكتبات النظام
├── README.md                   # دليل المشروع
├── SETUP.md                    # دليل التثبيت
├── USER_GUIDE.md               # دليل المستخدم
└── ... (ملفات أخرى)
```

## التوزيع

### 1. إنشاء حزمة ZIP
```bash
# ضغط مجلد النشر
7z a EduTrackForWin-v1.0.zip ./publish/*
```

### 2. إنشاء مثبت (اختياري)
يمكن استخدام أدوات مثل:
- Inno Setup
- WiX Toolset
- Advanced Installer

### 3. التوزيع عبر الشبكة
- رفع على خادم ملفات داخلي
- توزيع عبر USB
- مشاركة شبكة محلية

## التثبيت على الجهاز المستهدف

### للنشر المستقل
1. انسخ مجلد `publish` إلى الجهاز المستهدف
2. ضعه في مجلد مناسب مثل `C:\Program Files\EduTrack`
3. شغل `EduTrackForWin.exe` مباشرة
4. (اختياري) أنشئ اختصار على سطح المكتب

### للنشر المعتمد على Framework
1. تأكد من تثبيت .NET 8 Runtime على الجهاز
2. انسخ ملفات التطبيق
3. شغل `EduTrackForWin.exe`

## إعدادات الأمان

### Windows Defender
قد يحتاج المستخدم لإضافة التطبيق لقائمة الاستثناءات:
1. افتح Windows Security
2. اذهب إلى Virus & threat protection
3. اضغط Manage settings تحت Virus & threat protection settings
4. اضغط Add or remove exclusions
5. أضف مجلد التطبيق

### User Account Control (UAC)
التطبيق لا يحتاج صلاحيات مدير، ولكن قد يطلب Windows تأكيد التشغيل لأول مرة.

## قاعدة البيانات

### الموقع الافتراضي
```
%USERPROFILE%\AppData\Roaming\EduTrackForWin\edutrack.db
```

### النسخ الاحتياطي
- انسخ ملف `edutrack.db` بانتظام
- يمكن نسخه أثناء تشغيل التطبيق
- احفظ النسخ في مكان آمن

### الاستعادة
1. أغلق التطبيق
2. استبدل ملف `edutrack.db` بالنسخة الاحتياطية
3. شغل التطبيق

## استكشاف الأخطاء

### التطبيق لا يبدأ
1. تأكد من نظام التشغيل (Windows 10/11 64-bit)
2. تأكد من وجود جميع ملفات النشر
3. شغل من Command Prompt لرؤية رسائل الخطأ
4. تحقق من Windows Event Viewer

### مشاكل قاعدة البيانات
1. تأكد من صلاحيات الكتابة في مجلد AppData
2. تحقق من مساحة القرص الصلب
3. جرب حذف قاعدة البيانات (ستفقد البيانات!)

### مشاكل الأداء
1. تأكد من توفر ذاكرة كافية (4GB+)
2. أغلق البرامج غير الضرورية
3. تحقق من مساحة القرص الصلب

## التحديثات

### تحديث التطبيق
1. احفظ نسخة احتياطية من قاعدة البيانات
2. أغلق التطبيق القديم
3. استبدل ملفات التطبيق بالإصدار الجديد
4. احتفظ بملف قاعدة البيانات
5. شغل الإصدار الجديد

### ترقية قاعدة البيانات
- التطبيق يدعم الترقية التلقائية لقاعدة البيانات
- احفظ نسخة احتياطية قبل الترقية
- لا تستخدم قاعدة بيانات من إصدار أحدث مع إصدار أقدم

## الدعم الفني

### معلومات مطلوبة عند طلب الدعم
1. إصدار Windows
2. إصدار التطبيق
3. وصف المشكلة
4. خطوات إعادة إنتاج المشكلة
5. رسائل الخطأ (إن وجدت)

### ملفات السجل
التطبيق لا ينشئ ملفات سجل حالياً، ولكن يمكن إضافة هذه الميزة في إصدارات مستقبلية.

## أفضل الممارسات

### للمطور
1. اختبر النشر على أجهزة مختلفة
2. اختبر مع مستخدمين مختلفين
3. وثق أي تغييرات في قاعدة البيانات
4. احتفظ بإصدارات متعددة للتوافق العكسي

### للمدير
1. اعمل نسخ احتياطية يومية
2. اختبر استعادة النسخ الاحتياطية
3. درب المستخدمين على الاستخدام الأساسي
4. احتفظ بنسخة من ملفات التثبيت

### للمستخدم
1. لا تحذف ملفات التطبيق عشوائياً
2. أغلق التطبيق بشكل صحيح
3. أبلغ عن أي مشاكل فوراً
4. احتفظ بنسخ احتياطية شخصية

---

**ملاحظة**: هذا الدليل يغطي النشر الأساسي. للبيئات المؤسسية الكبيرة، قد تحتاج حلول نشر أكثر تقدماً مثل Group Policy أو System Center Configuration Manager.
