using System.Windows;
using EduTrackForWin.Models;
using EduTrackForWin.Data;
using Microsoft.EntityFrameworkCore;

namespace EduTrackForWin.Views
{
    public partial class TeacherDetailsWindow : Window
    {
        private readonly Teacher _teacher;
        private readonly EduTrackDbContext _context;

        public TeacherDetailsWindow(Teacher teacher)
        {
            InitializeComponent();
            _teacher = teacher;
            _context = new EduTrackDbContext();
            LoadTeacherDetails();
        }

        private async void LoadTeacherDetails()
        {
            // Basic Information
            TxtTeacherName.Text = _teacher.Name;
            TxtName.Text = _teacher.Name;
            TxtSpecialization.Text = string.IsNullOrEmpty(_teacher.Specialization) ? "غير محدد" : _teacher.Specialization;
            TxtPhone.Text = string.IsNullOrEmpty(_teacher.Phone) ? "غير محدد" : _teacher.Phone;
            TxtEmail.Text = string.IsNullOrEmpty(_teacher.Email) ? "غير محدد" : _teacher.Email;
            TxtCreatedAt.Text = _teacher.CreatedAt.ToString("yyyy/MM/dd");
            TxtNotes.Text = string.IsNullOrEmpty(_teacher.Notes) ? "لا توجد ملاحظات" : _teacher.Notes;

            // Salary Information
            if (_teacher.IsFixedSalary)
            {
                TxtSalaryType.Text = "راتب ثابت شهري";
                TxtSalaryAmount.Text = $"{_teacher.MonthlySalary:C}";
            }
            else
            {
                TxtSalaryType.Text = "راتب لكل حصة";
                TxtSalaryAmount.Text = $"{_teacher.SalaryPerSession:C}";
            }

            TxtCalculatedSalary.Text = $"{_teacher.CalculatedSalary:C}";

            // Load detailed data
            await LoadDetailedData();
        }

        private async Task LoadDetailedData()
        {
            try
            {
                // Load teacher with all related data
                var teacherWithData = await _context.Teachers
                    .Include(t => t.Groups)
                    .ThenInclude(g => g.Students)
                    .Include(t => t.Sessions)
                    .ThenInclude(s => s.Group)
                    .FirstOrDefaultAsync(t => t.Id == _teacher.Id);

                if (teacherWithData != null)
                {
                    // Groups Information
                    TxtTotalGroups.Text = teacherWithData.Groups?.Count.ToString() ?? "0";
                    
                    if (teacherWithData.Groups != null && teacherWithData.Groups.Any())
                    {
                        DgGroups.ItemsSource = teacherWithData.Groups.ToList();
                    }

                    // Performance Statistics
                    var sessionsThisMonth = teacherWithData.Sessions?.Count(s => 
                        s.Date.Month == DateTime.Now.Month && s.Date.Year == DateTime.Now.Year) ?? 0;
                    TxtSessionsThisMonth.Text = sessionsThisMonth.ToString();

                    var totalStudents = teacherWithData.Groups?.Sum(g => g.Students?.Count ?? 0) ?? 0;
                    TxtTotalStudents.Text = totalStudents.ToString();

                    var expectedRevenue = teacherWithData.Groups?.Sum(g => g.TotalMonthlyRevenue) ?? 0;
                    TxtExpectedRevenue.Text = $"{expectedRevenue:C}";

                    // Recent Sessions
                    var recentSessions = teacherWithData.Sessions?
                        .OrderByDescending(s => s.Date)
                        .ThenByDescending(s => s.StartTime)
                        .Take(10)
                        .ToList();

                    if (recentSessions != null && recentSessions.Any())
                    {
                        DgRecentSessions.ItemsSource = recentSessions;
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل البيانات التفصيلية: {ex.Message}", "خطأ", 
                              MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void BtnEdit_Click(object sender, RoutedEventArgs e)
        {
            var editWindow = new AddEditTeacherWindow(_teacher);
            if (editWindow.ShowDialog() == true)
            {
                // Refresh the data
                LoadTeacherDetails();
            }
        }

        private async void BtnViewSchedule_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // Load teacher's schedule for current week
                var startOfWeek = DateTime.Today.AddDays(-(int)DateTime.Today.DayOfWeek);
                var endOfWeek = startOfWeek.AddDays(7);

                var weekSessions = await _context.Sessions
                    .Include(s => s.Group)
                    .Where(s => s.TeacherId == _teacher.Id && 
                               s.Date >= startOfWeek && s.Date < endOfWeek)
                    .OrderBy(s => s.Date)
                    .ThenBy(s => s.StartTime)
                    .ToListAsync();

                var scheduleWindow = new TeacherScheduleWindow(_teacher, weekSessions);
                scheduleWindow.ShowDialog();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في عرض الجدول: {ex.Message}", "خطأ", 
                              MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void BtnSalaryReport_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var salaryReportWindow = new TeacherSalaryReportWindow(_teacher);
                salaryReportWindow.ShowDialog();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في عرض تقرير الراتب: {ex.Message}", "خطأ", 
                              MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void BtnClose_Click(object sender, RoutedEventArgs e)
        {
            Close();
        }

        protected override void OnClosed(EventArgs e)
        {
            _context?.Dispose();
            base.OnClosed(e);
        }
    }
}
